/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package eventsubscriber

import (
	"encoding/base64"
	zmq "github.com/pebbe/zmq4"
	"github.com/stretchr/testify/assert"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"testing"
	"time"
)

var (
	loggerObj = NewLogger("EventBus", consts.BlankString)

	count = 0
)

func TestMain(m *testing.M) {

	SetLogLevel(consts.LogLevelTrace)

	m.Run()
}

func TestEventSubscriber(t *testing.T) {

	eventCallback := func(event MotadataMap) {

		if event.Contains("a") {

			count++
		}

	}

	publisher, _ := context.NewSocket(zmq.PUB)

	publisher.SetLinger(0)

	publisher.Bind("tcp://*:6868")

	time.Sleep(3 * time.Second)

	subscriber, _ := New("localhost", uint16(6868), "test", eventCallback, &loggerObj)

	subscriber.Start()

	time.Sleep(2 * time.Second)

	publisher.Send("test"+base64.StdEncoding.EncodeToString([]byte(MotadataMap{"a": 1}.ToJSON().ToString())), 0)

	time.Sleep(2 * time.Second)

	assert.Equal(t, 1, count)

	publisher.Unbind("tcp://*:6868")

	publisher.Close()

	subscriber.Shutdown()

}
