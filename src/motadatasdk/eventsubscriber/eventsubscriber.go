/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package eventsubscriber

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	zmq "github.com/pebbe/zmq4"
	. "motadatasdk/globals"
	"strings"
	"time"
)

var context *zmq.Context

func init() {

	context, _ = zmq.NewContext()
}

type EventSubscriber struct {
	subscriber *zmq.Socket

	eventTopic string

	logger *Logger

	endpoint string

	shutdown bool

	eventCallback func(event MotadataMap)
}

func New(host string, port uint16, eventTopic string, eventCallback func(event MotadataMap), logger *Logger) (EventSubscriber, error) {

	subscriber := EventSubscriber{}

	subscriber.subscriber, _ = context.NewSocket(zmq.SUB)

	subscriber.eventTopic = eventTopic

	subscriber.logger = logger

	subscriber.endpoint = "tcp://" + host + ":" + ToString(port)

	subscriber.eventCallback = eventCallback

	if subscriber.subscriber == nil {

		return subscriber, errors.New("failed to create event subscriber")
	}

	return subscriber, nil

}

func (subscriber *EventSubscriber) Start() error {

	if subscriber.eventCallback == nil {

		subscriber.logger.Fatal("failed to start event subscriber, reason: invalid event callback")

		return errors.New("failed to start event subscriber, reason: invalid event callback")
	}

	if subscriber.subscriber != nil {

		subscriber.subscriber.SetLinger(0 * time.Second)

		subscriber.subscriber.SetRcvhwm(5000)

		subscriber.subscriber.SetRcvtimeo(-1 * time.Second)

		subscriber.subscriber.SetSubscribe(subscriber.eventTopic)

		err := subscriber.subscriber.Connect(subscriber.endpoint)

		if err != nil {

			subscriber.logger.Fatal(MotadataString(fmt.Sprintf("failed to start event subscriber, reason: %v", err.Error())))

			return err
		} else {

			go subscriber.subscribe()

			subscriber.logger.Info(MotadataString(fmt.Sprintf("event subscriber connected to %v successfully...", subscriber.endpoint)))
		}

	} else {

		subscriber.logger.Fatal("failed to start event subscriber, reason: invalid socket")

		return errors.New("failed to start event subscriber, reason: invalid socket")
	}

	return nil
}

func (subscriber *EventSubscriber) subscribe() {

	for {

		if !subscriber.shutdown {

			event, err := subscriber.subscriber.Recv(0)

			if len(event) > 0 {

				tokens := MotadataString(event).Split(subscriber.eventTopic)

				if len(tokens) == 1 && tokens[0].IsNotEmpty() {

					bytes, _ := base64.StdEncoding.DecodeString(tokens[0].ToString())

					event := make(MotadataMap)

					json.Unmarshal(bytes, &event)

					subscriber.logger.Trace(MotadataString(fmt.Sprintf("event received %v", event)))

					subscriber.eventCallback(event)
				}
			} else if err != nil && strings.Contains(err.Error(), "Context was terminated") {

				subscriber.subscriber.Disconnect(subscriber.endpoint)

				subscriber.subscriber.Close()

				return
			}

		} else {

			break
		}
	}
}

func (subscriber *EventSubscriber) Shutdown() {

	if subscriber.subscriber != nil {

		subscriber.shutdown = true

		context.Term()

		subscriber.logger.Info("event subscriber stopped...")
	}
}
