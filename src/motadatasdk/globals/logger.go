/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package globals

import (
	"motadatasdk/consts"
	"os"
)

type Logger struct {
	component MotadataString

	module MotadataString
}

var logLevel = consts.LogLevelInfo

func TraceEnabled() bool {

	return consts.LogLevelTrace >= logLevel
}

func DebugEnabled() bool {

	return consts.LogLevelDebug >= logLevel
}

func SetLogLevel(value int) {

	logLevel = value
}

func NewLogger(component MotadataString, module MotadataString) Logger {

	return Logger{component: component, module: module}
}

func (logger *Logger) Trace(message MotadataString) {

	if TraceEnabled() {

		currentDate := MotadataTimeString(consts.LogFileDateFormat).Format()

		currentTime := MotadataTimeString(consts.LogFileTimeFormat).Format()

		message := currentDate + consts.SpaceSeparator + currentTime + consts.SpaceSeparator + "TRACE [" + logger.component + "]:" +
			message

		logger.write(message.ToString())

	}
}

func (logger *Logger) Debug(message MotadataString) {

	if DebugEnabled() {

		currentDate := MotadataTimeString(consts.LogFileDateFormat).Format()

		currentTime := MotadataTimeString(consts.LogFileTimeFormat).Format()

		message := currentDate + consts.SpaceSeparator + currentTime + consts.SpaceSeparator + "DEBUG [" + logger.component + "]:" +
			message

		logger.write(message.ToString())

	}
}

func (logger *Logger) Info(message MotadataString) {

	currentDate := MotadataTimeString(consts.LogFileDateFormat).Format()

	currentTime := MotadataTimeString(consts.LogFileTimeFormat).Format()

	message = currentDate + consts.SpaceSeparator + currentTime + consts.SpaceSeparator + "INFO [" + logger.component + "]:" +
		message

	logger.write(message.ToString())

}

func (logger *Logger) Warn(message MotadataString) {

	currentDate := MotadataTimeString(consts.LogFileDateFormat).Format()

	currentTime := MotadataTimeString(consts.LogFileTimeFormat).Format()

	message = currentDate + consts.SpaceSeparator + currentTime + consts.SpaceSeparator + "WARN [" + logger.component + "]:" + message

	logger.write(message.ToString())

}

func (logger *Logger) Fatal(message MotadataString) {

	currentDate := MotadataTimeString(consts.LogFileDateFormat).Format()

	currentTime := MotadataTimeString(consts.LogFileTimeFormat).Format()

	message = currentDate + consts.SpaceSeparator + currentTime + consts.SpaceSeparator + "FATAL [" + logger.component + "]:" +
		message

	logger.write(message.ToString())

}

func (logger *Logger) write(message string) {

	logDir := consts.CurrentDir + consts.PathSeparator + consts.LogDirectory + consts.PathSeparator + consts.PluginEngineLogDirectory

	_, err := os.Stat(logDir)

	if os.IsNotExist(err) {

		err := os.MkdirAll(logDir, 0755)

		if err != nil {

			panic(err)
		}

	}

	logFile := logDir + consts.PathSeparator

	currentDate := MotadataTimeString(consts.LogFileDateFormat).Format()

	if logger.module.IsNotEmpty() {

		logFile = logFile + MotadataString(consts.LogFile).ReplaceAll("@@@", currentDate).ReplaceAll("###", logger.module).ToString()

	} else {

		logFile = logFile + MotadataString(consts.LogFile).ReplaceAll("@@@", currentDate).ReplaceAll("###", "Plugin Engine").ToString()
	}

	file, err := os.OpenFile(logFile, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0644)

	if err != nil {

		panic(err)
	}

	defer func(file *os.File) {
		_ = file.Close()
	}(file)

	if _, err = file.WriteString(message + consts.NewLineSeparator); err != nil {

		panic(err)
	}
}
