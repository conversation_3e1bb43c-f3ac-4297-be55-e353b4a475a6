/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package globals

import (
	"github.com/stretchr/testify/assert"
	"motadatasdk/consts"
	"testing"
)

func TestMotadataTypesMap(t *testing.T) {

	testMap := make(MotadataMap)

	assert.False(t, testMap.IsNotEmpty())

	testMap["string_value"] = "value1"

	assert.True(t, testMap.IsNotEmpty())

	assert.True(t, testMap.Contains("string_value"))

	assert.False(t, testMap.Contains("unknown_key"))

	assert.Equal(t, "value1", testMap.GetStringValue("string_value"))

	// Map with Primitive type values type conversions to Native as well as MotadataTypes

	testMap["int_value"] = 1

	testMap["int8_value"] = int8(1)

	testMap["int16_value"] = int16(1)

	testMap["int32_value"] = int32(1)

	testMap["int64_value"] = int64(1)

	testMap["uint_value"] = uint(1)

	testMap["uint8_value"] = uint8(1)

	testMap["uint16_value"] = uint16(1)

	testMap["uint32_value"] = uint32(1)

	testMap["uint64_value"] = uint64(1)

	testMap["motadata_int_value"] = MotadataINT(1)

	testMap["motadata_uint_value"] = MotadataUINT(1)

	testMap["float32_value"] = float32(1)

	testMap["float64_value"] = float64(1)

	testMap["motadata_float64_value"] = MotadataFloat64(1)

	testMap["uint8_slice_value"] = []uint8{'h', 'e', 'l', 'l', 'o', ' ', 'w', 'o', 'r', 'l', 'd'}

	testMap["string_value"] = "9"

	testMap["float_string_value"] = "9.55"

	assert.Equal(t, 1, testMap.GetIntValue("motadata_int_value"))

	assert.Equal(t, 1, testMap.GetIntValue("int_value"))

	assert.Equal(t, 1, testMap.GetIntValue("int8_value"))

	assert.Equal(t, 1, testMap.GetIntValue("int16_value"))

	assert.Equal(t, 1, testMap.GetIntValue("int32_value"))

	assert.Equal(t, 1, testMap.GetIntValue("int64_value"))

	assert.Equal(t, 1, testMap.GetIntValue("uint_value"))

	assert.Equal(t, 1, testMap.GetIntValue("uint8_value"))

	assert.Equal(t, 1, testMap.GetIntValue("uint16_value"))

	assert.Equal(t, 1, testMap.GetIntValue("uint32_value"))

	assert.Equal(t, 1, testMap.GetIntValue("uint64_value"))

	assert.Equal(t, 9, testMap.GetIntValue("string_value"))

	assert.Equal(t, int64(1), testMap.GetInt64Value("int64_value"))

	assert.Equal(t, MotadataINT(1), testMap.GetINTValue("int_value"))

	assert.Equal(t, MotadataINT(1), testMap.GetINTValue("motadata_int_value"))

	assert.Equal(t, MotadataINT(1), testMap.GetINTValue("int8_value"))

	assert.Equal(t, MotadataINT(1), testMap.GetINTValue("int16_value"))

	assert.Equal(t, MotadataINT(1), testMap.GetINTValue("int32_value"))

	assert.Equal(t, MotadataINT(1), testMap.GetINTValue("int64_value"))

	assert.Equal(t, MotadataINT(1), testMap.GetINTValue("uint_value"))

	assert.Equal(t, MotadataINT(1), testMap.GetINTValue("uint8_value"))

	assert.Equal(t, MotadataINT(1), testMap.GetINTValue("uint16_value"))

	assert.Equal(t, MotadataINT(1), testMap.GetINTValue("uint32_value"))

	assert.Equal(t, MotadataINT(1), testMap.GetINTValue("uint64_value"))

	assert.Equal(t, MotadataINT(1), testMap.GetINTValue("float64_value"))

	assert.Equal(t, MotadataINT(9), testMap.GetINTValue("string_value"))

	assert.Equal(t, MotadataUINT(9), testMap.GetUINTValue("string_value"))

	assert.Equal(t, MotadataUINT8(1), testMap.GetUINT8Value("int_value"))

	assert.Equal(t, MotadataUINT16(1), testMap.GetUINT16Value("int_value"))

	assert.Equal(t, MotadataUINT(1), testMap.GetUINTValue("motadata_uint_value"))

	assert.Equal(t, MotadataUINT(1), testMap.GetUINTValue("int_value"))

	assert.Equal(t, MotadataUINT(1), testMap.GetUINTValue("int8_value"))

	assert.Equal(t, MotadataUINT(1), testMap.GetUINTValue("int16_value"))

	assert.Equal(t, MotadataUINT(1), testMap.GetUINTValue("int32_value"))

	assert.Equal(t, MotadataUINT(1), testMap.GetUINTValue("int64_value"))

	assert.Equal(t, MotadataUINT(1), testMap.GetUINTValue("uint8_value"))

	assert.Equal(t, MotadataUINT(1), testMap.GetUINTValue("uint16_value"))

	assert.Equal(t, MotadataUINT(1), testMap.GetUINTValue("uint32_value"))

	assert.Equal(t, MotadataUINT(1), testMap.GetUINTValue("uint64_value"))

	assert.Equal(t, MotadataUINT(1), testMap.GetUINTValue("uint_value"))

	assert.Equal(t, MotadataFloat64(1), testMap.GetFloat64Value("float64_value"))

	assert.Equal(t, MotadataFloat64(1), testMap.GetFloat64Value("motadata_float64_value"))

	assert.Equal(t, float64(1), testMap.GetFloatValue("float32_value"))

	assert.Equal(t, float64(1), testMap.GetFloatValue("float64_value"))

	assert.Equal(t, 9.55, testMap.GetFloatValue("float_string_value"))

	assert.Equal(t, MotadataFloat64(9.55), testMap.GetFloat64Value("float_string_value"))

	// Map with Primitive Types to String Conversions

	assert.Equal(t, "1", testMap.GetStringValue("int_value"))

	assert.Equal(t, "1", testMap.GetStringValue("int64_value"))

	assert.Equal(t, "hello world", testMap.GetStringValue("uint8_slice_value"))

	assert.Equal(t, MotadataString("1"), testMap.GetMotadataStringValue("int_value"))

	assert.Equal(t, MotadataString("1"), testMap.GetMotadataStringValue("int64_value"))

	assert.Equal(t, MotadataString("1"), testMap.GetMotadataStringValue("float64_value"))

	assert.Equal(t, MotadataString("hello world"), testMap.GetMotadataStringValue("uint8_slice_value"))

	// Traffic value in bytes conversion

	assert.Equal(t, MotadataFloat64(1*1024), testMap.GetTrafficKBValue("int_value"))

	assert.Equal(t, MotadataFloat64(1*1024*1024), testMap.GetTrafficMBValue("int_value"))

	assert.Equal(t, MotadataFloat64(1*1024*1024*1024), testMap.GetTrafficGBValue("int_value"))

	// Map with Map type as values

	testMap["map_value"] = make(MotadataMap)

	assert.NotEqual(t, testMap.GetMapValue("map_value"), nil)

	testMap.Delete("map_value")

	assert.False(t, testMap.Contains("map_value"))

	//  Map with slice type values

	testMap["interface_slice_value"] = []interface{}{"a", "b", 1, 2, 3}

	assert.Equal(t, []interface{}(nil), testMap.GetSliceValue("unknown_key"))

	assert.Equal(t, []interface{}{"a", "b", 1, 2, 3}, testMap.GetSliceValue("interface_slice_value"))

	testMap["list_value"] = MotadataStringList{"a", "b", "c", "d"}

	assert.Equal(t, MotadataStringList{"a", "b", "c", "d"}, testMap.GetListValue("list_value"))

	assert.Equal(t, MotadataStringList(nil), testMap.GetListValue("unknown_key"))

	testMap["map_slice_value"] = []MotadataMap{
		{
			"key1": "value1",
			"key2": "value2",
		},
		{
			"key3": "value3",
			"key4": "value4",
		},
	}

	testMap2 := testMap.GetMapSliceValue("map_slice_value")[0].DeepCopy()

	assert.True(t, testMap2.ContainValues(testMap.GetMapSliceValue("map_slice_value")))

	for _, value := range testMap.GetMapSliceValue("map_slice_value") {

		value.Delete("key1")

		value.Delete("key2")

		value["key3"] = "value3"
	}

	assert.False(t, testMap2.ContainValues(testMap.GetMapSliceValue("map_slice_value")))

	testMap["string_map_slice_value"] = []MotadataStringMap{
		{
			"key1": "value1",
		},
		{
			"key2": "value2",
		},
	}

	assert.Equal(t, []MotadataStringMap{{"key1": "value1"}, {"key2": "value2"}}, testMap.GetStringMapSliceValue("string_map_slice_value"))

	assert.Equal(t, []MotadataStringMap(nil), testMap.GetStringMapSliceValue("unknown_key"))

	// Other Map Variant Types Test :- MotadataIntFloatMap, MotadataStringMap, MotadataIntMap

	testMap["int_float_map_value"] = MotadataIntFloatMap{
		1: 1.4,
		2: 2.4,
	}

	assert.Equal(t, MotadataIntFloatMap{1: 1.4, 2: 2.4}, testMap.GetIntFloatMapValue("int_float_map_value"))

	assert.Equal(t, MotadataIntFloatMap(nil), testMap.GetIntFloatMapValue("unknown_key"))

	testIntMap := make(MotadataIntMap)

	testIntMap[1] = "value"

	assert.True(t, testIntMap.Contains(1))

	assert.False(t, testIntMap.Contains(-1))

	testStringMap := make(MotadataStringMap)

	assert.False(t, testStringMap.IsNotEmpty())

	assert.False(t, testStringMap.Contains("wrong key"))

	testStringMap["key1"] = "value1"

	testStringMap["key2"] = "value2"

	assert.True(t, testStringMap.Contains("key1"))

	testStringMap.Delete("key2")

	assert.False(t, testStringMap.Contains("key2"))

	testIntFloatMap := make(MotadataIntFloatMap)

	assert.False(t, testIntFloatMap.IsNotEmpty())

	testIntFloatMap[1] = 1.0

	testIntFloatMap[2] = 2.0

	testIntFloatMap[3] = 3.0

	testIntFloatMap[4] = 4.0

	assert.Equal(t, 4, testIntFloatMap.GetMaxKey())

	assert.True(t, testIntFloatMap.IsNotEmpty())

	//MapSlice

	var testMapSlice []MotadataMap

	assert.False(t, IsNotEmptyMapSlice(testMapSlice))

	testMapSlice = []MotadataMap{
		{
			"key1": "value1",
			"key2": "value2",
		},
		{
			"key1": "value1",
			"key2": "value2",
		},
	}

	assert.True(t, IsNotEmptyMapSlice(testMapSlice))

	// interface to MotadataMap

	var testInterface interface{}

	testInterface = make(map[string]interface{})

	assert.Equal(t, MotadataMap{}, ToMap(testInterface))

}

func TestMotadataTypesString(t *testing.T) {

	testString := MotadataString("")

	assert.False(t, testString.IsNotEmpty())

	testString = "123"

	assert.True(t, testString.IsDigit())

	assert.Equal(t, MotadataINT(123), testString.ToINT())

	assert.Equal(t, 123, testString.ToInt())

	testString = "3.1415926"

	assert.Equal(t, MotadataFloat64(3.14), testString.ToFloat64())

	testString = "12.3.4"

	assert.False(t, testString.IsDigit())

	assert.Equal(t, []MotadataString{"12", "3", "4"}, testString.Split("."))

	assert.True(t, testString.Contains("3"))

	assert.Equal(t, MotadataString("1234"), testString.Replace(".", consts.BlankString, 2))

	testString = "Hello, World !!  "

	assert.True(t, testString.HasPrefix("Hello"))

	assert.True(t, testString.HasSuffix("!!"))

	assert.Equal(t, MotadataString("HELLO, WORLD !!"), testString.ToUpper())

	assert.Equal(t, MotadataString("hello, world !!"), testString.ToLower())

	assert.Equal(t, "hello, world !!", testString.ToLowerNative())

	assert.Equal(t, []MotadataString{"Hello,", "World", "!!"}, testString.Fields())

	testString = "a,b,c,a"

	assert.Equal(t, MotadataString("d,b,c,d"), testString.ReplaceAll("a", "d"))

	assert.Equal(t, MotadataString("d,b,c,a"), testString.Replace("a", "d", 1))

	assert.Equal(t, []MotadataString{"a", "b,c,a"}, testString.SplitN(",", 2))

	testString = "!!Hello, Gophers!!"

	assert.Equal(t, MotadataString("Hello, Gophers"), testString.Trim("!"))

	testString = "  string with unnecessary leading and trailing spaces   "

	assert.Equal(t, MotadataString("string with unnecessary leading and trailing spaces"), testString.TrimSpace())

	testString = "!!Hello, Gophers!!!"

	assert.Equal(t, MotadataString("!!Hello, Gophers"), testString.TrimRight("!"))

	assert.Equal(t, MotadataString("!!Hello, Gophers!!"), testString.TrimSuffix("!"))

	assert.True(t, testString.MatchFound("\\d+", "\\s\\w+[^\\w]+"))

	testString = "a,   b,c,d    "

	assert.Equal(t, []MotadataString{"a", "b", "c", "d"}, testString.SplitWithEmptyEntries(","))

	//MotadataStringList and []MotadataString

	testStringList := MotadataStringList{}

	assert.False(t, testStringList.IsNotEmpty())

	testStringList = MotadataStringList{"a", "b", "c", "d"}

	assert.Equal(t, MotadataString("a,b,c,d"), testStringList.Join(","))

	assert.True(t, testStringList.IsNotEmpty())

	assert.True(t, testStringList.Contains("b"))

	assert.False(t, testStringList.Contains("x"))

	assert.Equal(t, 0, testStringList.GetIndexByValues(MotadataStringList{"a", "c", "b", "d"}))

	assert.Equal(t, -1, testStringList.GetIndexByValues(MotadataStringList{"x", "y", "z", "w"}))

	testStringSlice := ToSlice(testStringList)

	assert.True(t, IsNotEmptyStringSlice(testStringSlice))

	assert.False(t, IsNotEmptyStringSlice([]MotadataString{}))

	assert.Equal(t, []MotadataString{"a", "b", "c", "d"}, testStringSlice)

	assert.Equal(t, testStringList, ToList([]string{"a", "b", "c", "d"}))

	assert.False(t, Contains([]string{"a", "b", "c"}, "d"))

	assert.True(t, Contains([]string{"a", "b", "c"}, "b"))

	assert.Equal(t, MotadataStringList{"a", "b", "c", "d"}, StringSliceToList([]MotadataString{"a", "b", "c", "d"}))

}

func TestMotadataTypesNumeric(t *testing.T) {

	//MotadataINT Type Conversions

	testInt := MotadataINT(1)

	assert.Equal(t, MotadataFloat64(1), testInt.ToFloat64())

	assert.Equal(t, 1, testInt.ToInt())

	assert.Equal(t, "1", testInt.ToNativeString())

	assert.Equal(t, MotadataString("1"), testInt.ToString())

	assert.Equal(t, MotadataUINT(1), testInt.ToUINT())

	assert.Equal(t, int64(1), testInt.ToInt64())

	//MotadataUINT Type Conversions

	testUINT := MotadataUINT(1)

	assert.Equal(t, MotadataString("1"), testUINT.ToString())

	assert.Equal(t, 1, testUINT.ToInt())

	assert.Equal(t, MotadataFloat64(1), testUINT.ToFloat64())

	assert.Equal(t, MotadataINT(1), testUINT.ToINT())

	//MotadataUINT8 Type Conversions

	testUINT8 := MotadataUINT8(1)

	assert.Equal(t, MotadataString("1"), testUINT8.ToString())

	assert.Equal(t, 1, testUINT8.ToInt())

	assert.Equal(t, MotadataFloat64(1), testUINT8.ToFloat64())

	assert.Equal(t, MotadataINT(1), testUINT8.ToINT())

	//MotadataUINT16 Type Conversions

	testUINT16 := MotadataUINT16(1)

	assert.Equal(t, MotadataString("1"), testUINT16.ToString())

	assert.Equal(t, 1, testUINT16.ToInt())

	assert.Equal(t, MotadataFloat64(1), testUINT16.ToFloat64())

	assert.Equal(t, MotadataINT(1), testUINT16.ToINT())

	assert.Equal(t, uint16(1), testUINT16.ToUInt16())

	//MotadataUINT32 Type Conversions

	testUINT32 := MotadataUINT32(1)

	assert.Equal(t, MotadataString("1"), testUINT32.ToString())

	assert.Equal(t, MotadataINT(1), testUINT32.ToINT())

	assert.Equal(t, MotadataFloat64(1), testUINT32.ToFloat64())

	//MotadataUINT64 Type Conversions

	testUINT64 := MotadataUINT64(1)

	assert.Equal(t, MotadataString("1"), testUINT64.ToString())

	assert.Equal(t, MotadataINT(1), testUINT64.ToINT())

	assert.Equal(t, MotadataFloat64(1), testUINT64.ToFloat64())

	//MotadataFloat32 Type Conversions

	testFloat32 := MotadataFloat32(1)

	assert.Equal(t, MotadataString("1"), testFloat32.ToString())

	assert.Equal(t, MotadataINT(1), testFloat32.ToINT())

	assert.Equal(t, MotadataFloat64(1), testFloat32.ToFloat64())

	//MotadataFloat64 Type Conversions

	testFloat64 := MotadataFloat64(1)

	assert.Equal(t, MotadataString("1"), testFloat64.ToString())

	assert.Equal(t, MotadataINT(1), testFloat64.ToINT())

	assert.Equal(t, MotadataFloat64(1), testFloat64.ToFloat64())

	assert.Equal(t, MotadataUINT(1), testFloat64.ToUINT())

	assert.Equal(t, MotadataUINT16(1), testFloat64.ToUINT16())

	//interface to int Conversions

	var testInterface interface{}

	testInterface = 1

	assert.Equal(t, 1, ToInt(testInterface))

	testInterface = uint(1)

	assert.Equal(t, 1, ToInt(testInterface))

	testInterface = uint8(1)

	assert.Equal(t, 1, ToInt(testInterface))

	testInterface = uint16(1)

	assert.Equal(t, 1, ToInt(testInterface))

	testInterface = uint32(1)

	assert.Equal(t, 1, ToInt(testInterface))

	testInterface = uint64(1)

	assert.Equal(t, 1, ToInt(testInterface))

	testInterface = int8(1)

	assert.Equal(t, 1, ToInt(testInterface))

	testInterface = int16(1)

	assert.Equal(t, 1, ToInt(testInterface))

	testInterface = int32(1)

	assert.Equal(t, 1, ToInt(testInterface))

	testInterface = int64(1)

	assert.Equal(t, 1, ToInt(testInterface))

	testInterface = "1"

	assert.Equal(t, 1, ToInt(testInterface))

	// interface to MotadataINT

	assert.Equal(t, MotadataINT(1), ToINT(testInterface))

	// interface to String conversion

	testInterface = MotadataString("1")

	assert.Equal(t, "1", ToString(testInterface))

	testInterface = 1

	assert.Equal(t, "1", ToString(testInterface))

	testInterface = true

	assert.Equal(t, "true", ToString(testInterface))

	testInterface = float64(1)

	assert.Equal(t, "1", ToString(testInterface))

	testInterface = []uint8{'h', 'e', 'l', 'l', 'o', ' ', 'w', 'o', 'r', 'l', 'd'}

	assert.Equal(t, "hello world", ToString(testInterface))

	testInterface = []byte{104, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100}

	assert.Equal(t, "hello world", ToString(testInterface))

	// interface to MotadataString conversion

	testInterface = "1"

	assert.Equal(t, MotadataString("1"), ToMotadataString(testInterface))

	testInterface = 1

	assert.Equal(t, MotadataString("1"), ToMotadataString(testInterface))

	testInterface = true

	assert.Equal(t, MotadataString("true"), ToMotadataString(testInterface))

	testInterface = float64(1)

	assert.Equal(t, MotadataString("1"), ToMotadataString(testInterface))

	testInterface = []uint8{'h', 'e', 'l', 'l', 'o', ' ', 'w', 'o', 'r', 'l', 'd'}

	assert.Equal(t, MotadataString("hello world"), ToMotadataString(testInterface))

	testInterface = []byte{104, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100}

	assert.Equal(t, MotadataString("hello world"), ToMotadataString(testInterface))

	// interface to Float Conversion

	testInterface = float32(1)

	assert.Equal(t, float64(1), ToFloat(testInterface))

	testInterface = float64(1)

	assert.Equal(t, float64(1), ToFloat(testInterface))

	// interface to MotadataFloat Conversion

	assert.Equal(t, MotadataFloat64(1), ToMotadataFloat(testInterface))

	// interface to [] interface

	testInterface = []interface{}{1, "a", "b", 2, 3}

	assert.Equal(t, []interface{}{1, "a", "b", 2, 3}, ToObjectSlice(testInterface))

	// interface to []MotadataString

	testInterface = []MotadataString{"a", "b", "c", "d"}

	assert.Equal(t, []MotadataString{"a", "b", "c", "d"}, ToStringSlice(testInterface))

}

func TestMotadataTypesByte(t *testing.T) {

	testMotadataKB := MotadataKB(1)

	assert.Equal(t, MotadataFloat64(1*1024), testMotadataKB.ToBytes())

	testMotadataMB := MotadataMB(1)

	assert.Equal(t, MotadataFloat64(1*1024*1024), testMotadataMB.ToBytes())

	testMotadataGB := MotadataGB(1)

	assert.Equal(t, MotadataFloat64(1*1024*1024*1024), testMotadataGB.ToBytes())

}

func TestMotadataTypesTime(t *testing.T) {

	testTime := MotadataTime(91234)

	assert.Equal(t, MotadataString(" 1 days 1 hours 20 minutes 34 seconds"), testTime.ToString())

	testTime = 1624710474

	assert.Equal(t, MotadataString("2021-06-26 17:57:54"), testTime.Format())

}
