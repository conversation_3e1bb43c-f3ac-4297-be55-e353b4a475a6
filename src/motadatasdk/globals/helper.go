/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
 * Change Logs:
 * Date			  Author			Notes
 * 05-Jun-2025    Darshan Parmar    MOTADATA-6338: encode method added
 * 07-Aug-2025    Jenil             MOTADATA-6883: added method Normalizeseprator

 */

package globals

import (
	"encoding/base64"
	"fmt"
	"motadatasdk/consts"
	"net"
	"reflect"
	"regexp"
	"strings"
	"time"
	"unicode"
)

var (
	PanicLogger = NewLogger("Panic Error", "Panic Error Log")
)

func SetupGlobalCleanupRoutine(responses chan<- MotadataMap, response MotadataMap, requestType MotadataString) {

	if r := recover(); r != nil {

		err := make(MotadataStringMap)

		err[consts.Message] = fmt.Sprintf("%v", r)

		err[consts.ErrorCode] = consts.ErrorCodeInternalError

		err[consts.Error] = fmt.Sprintf("%v", r)

		response[consts.Errors] = append(response.GetStringMapSliceValue(consts.Errors), err)

		PanicLogger.Fatal(MotadataString(fmt.Sprintf("serious error %v occurred in the plugin %v", r, RemoveSensitiveFields(response).ToJSON())))
	}

	if requestType == consts.EventPoll {

		AddStatusField(response)
	}

	if responses != nil {

		responses <- response
	}
}

func RemoveSensitiveFields(context MotadataMap) MotadataMap {

	context = context.DeepCopy()

	if context.Contains(consts.DiscoveryCredentialProfiles) {

		for _, profile := range context.GetSliceValue(consts.DiscoveryCredentialProfiles) {

			for _, field := range consts.SensitiveFields {

				if ctx := ToMap(profile); ctx.Contains(field) {

					ctx.Delete(field)
				}
			}
		}

	} else {

		for _, field := range consts.SensitiveFields {

			if context.Contains(field) {

				context.Delete(field)
			}
		}

	}

	return context
}

func AddStatusField(context MotadataMap) {

	if context[consts.Result] != nil && reflect.TypeOf(context[consts.Result]).Kind() == reflect.Map && context.GetMapValue(consts.Result).IsNotEmpty() {

		context[consts.Status] = consts.StatusSucceed

	} else if context[consts.Result] != nil && reflect.TypeOf(context[consts.Result]).Kind() == reflect.String && context.GetMotadataStringValue(consts.Result).IsNotEmpty() {

		context[consts.Status] = consts.StatusSucceed

	} else {

		context[consts.Status] = consts.StatusFail
	}
}

func Append(objs MotadataMap) (objects []MotadataMap) {

	for _, object := range objs {

		objects = append(objects, ToMap(object))
	}

	return
}

func ReplaceVariables(command MotadataString, variables MotadataMap) MotadataString {

	systemVariables := []MotadataString{"monitor.ip", "monitor.port", "monitor.host", "monitor.target", "monitor.username",
		"monitor.password", "monitor.name", "monitor.type", "monitor.category", "monitor.status",
		"monitor.groups", "monitor.uptime", "monitor.downtime", "monitor.database", "count"}

	for _, variable := range systemVariables {

		if variables["$$$"+variable.ToString()+"$$$"] != nil {

			command = command.ReplaceAll("$$$"+variable+"$$$", ToINT(variables["$$$"+variable.ToString()+"$$$"]).ToString())
		}
	}

	if variables.IsNotEmpty() {

		for key, value := range variables {

			if reflect.ValueOf(command).Kind().String() == "string" {

				command = command.ReplaceAll(MotadataString(key), ToINT(value).ToString())
			}
		}
	}

	return command
}

func MotadataStringSliceToMACAddress(tokens []MotadataString) MotadataString {

	macAddress := MotadataString("")

	for index, token := range tokens {

		hex := MotadataString(fmt.Sprintf("%X", uint8(token.ToInt())))

		if len(hex) == 1 {

			hex = "0" + hex
		}

		if index == len(tokens)-1 {

			macAddress = macAddress + hex

		} else {

			macAddress = macAddress + hex + ":"

		}
	}

	return macAddress
}

func StringSliceToMACAddress(tokens interface{}) (macAddress MotadataString) {

	macAddress = consts.BlankString

	if tokens != nil {

		for index, token := range tokens.([]uint8) {

			hex := MotadataString(fmt.Sprintf("%X", token))

			if len(hex) == 1 {

				hex = "0" + hex
			}

			if index == len(tokens.([]uint8))-1 {

				macAddress = macAddress + hex

			} else {

				macAddress = macAddress + hex + ":"
			}
		}
	}

	return
}

func IsASCII(value string) bool {

	for _, char := range value {

		if char > unicode.MaxASCII {

			return false
		}
	}
	return true
}

func IsValidCharacter(value string) bool {

	for _, char := range value {

		//All non-printable characters except carriage return and line-feed
		if !unicode.IsPrint(char) && char != 13 && char != 10 {
			return false
		}
	}

	return true
}

func FormatIP(tokens interface{}) (ip MotadataString) {

	ip = consts.BlankString

	for _, token := range tokens.([]uint8) {

		ip += MotadataString(fmt.Sprintf("%d", token)) + consts.DotSeparator
	}

	return ip.Trim(consts.DotSeparator)
}

func IsOpened(port MotadataUINT16, target MotadataString) bool {

	timeout := 1 * time.Second

	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", target.ToString(), port.ToInt()), timeout)

	if err != nil {
		return false
	}

	if conn != nil {
		err := conn.Close()
		if err != nil {
			return false
		}

		return true
	}

	return false
}

func ReplaceSpecialCharacters(value MotadataString) MotadataString {
	return value.Replace("#", "_", -1).Replace("/", "-", -1).Replace("\\", "-", -1).Replace("*", "_", -1).Replace("(", "[", -1).Replace(")", "]", -1)
}

func ConvertToMACAddress(macPattern *regexp.Regexp, macPattern2Digit *regexp.Regexp, macPattern4Digit *regexp.Regexp, value MotadataString, macScanner bool) MotadataString {

	value = value.Replace("0x", "", -1).Replace("-", ":", -1).Replace(" ", ":", -1).Replace(".", ":", -1).ToUpper()

	if !value.Contains(":") && len(value) == 12 {

		if macPattern.MatchString(string(value)) {

			value = MotadataString(value.ToString()[0:2] +
				":" + value.ToString()[2:4] +
				":" + value.ToString()[4:6] +
				":" + value.ToString()[6:8] +
				":" + value.ToString()[8:10] +
				":" + value.ToString()[10:12])

		}

	} else if strings.Count(value.ToString(), ":") == 5 {

		if len(value) < 17 {

			value = formatMACAddress(2, value)
		}

	} else if strings.Count(value.ToString(), ":") == 2 {

		if len(value) < 14 {

			value = formatMACAddress(4, value)
		}

		if macPattern4Digit.MatchString(string(value)) {

			value = value.Replace(":", "", -1)

			value = MotadataString(value.ToString()[0:2] +
				":" + value.ToString()[2:4] +
				":" + value.ToString()[4:6] +
				":" + value.ToString()[6:8] +
				":" + value.ToString()[8:10] +
				":" + value.ToString()[10:12])
		}
	}

	if !macPattern2Digit.MatchString(string(value)) {

		value = consts.BlankString
	}

	if macScanner && (value == "FF:FF:FF:FF:FF:FF" || value == "00:00:00:00:00:00") {

		value = consts.BlankString
	}

	return value
}

func formatMACAddress(digit int, macAddress MotadataString) MotadataString {

	digit2 := map[int]MotadataString{0: "00", 1: "0", 2: ""}

	digit4 := map[int]MotadataString{0: "0000", 1: "000", 2: "00", 3: "0", 4: ""}

	formattedMACAddress := MotadataString("")

	for _, token := range macAddress.Split(":") {

		if digit == 2 {

			formattedMACAddress += digit2[(len(token.Strip()))] + token + ":"

		} else if digit == 4 {

			formattedMACAddress += digit4[(len(token.Strip()))] + token + ":"
		}

	}

	return formattedMACAddress.Trim(":")
}

func BytesToIPv6Address(bytes []MotadataString) MotadataString {

	bufferBytes := make([]byte, 16)

	for index := 0; index < 16; index++ {

		bufferBytes[index] = byte(bytes[index].ToInt())
	}

	return ToMotadataString(net.IP(bufferBytes).To16().String())
}

func Encode(input string) string {

	return base64.StdEncoding.EncodeToString([]byte(input))
}

func NormalizeSeparators(token string) MotadataString {

	token = strings.TrimSpace(token)

	for _, item := range []string{"~", "^", "@#$", "#@#", "#", "@"} {

		token = strings.ReplaceAll(token, item, "")
	}

	return MotadataString(token)
}
