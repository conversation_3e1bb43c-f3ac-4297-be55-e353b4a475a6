/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package globals

import (
	"github.com/stretchr/testify/assert"
	"motadatasdk/consts"
	"os"
	"path/filepath"
	"testing"
)

func TestMain(m *testing.M) {

	consts.CurrentDir = filepath.Dir(filepath.Dir(filepath.Dir(consts.CurrentDir)))

	logDir := filepath.Dir(filepath.Dir(consts.CurrentDir)) + consts.PathSeparator + consts.LogDirectory + consts.PathSeparator + consts.PluginEngineLogDirectory

	_ = os.RemoveAll(logDir)

	m.Run()
}

func assertLoggerTestResult(module string) bool {

	logDir := consts.CurrentDir + consts.PathSeparator + consts.LogDirectory + consts.PathSeparator + consts.PluginEngineLogDirectory

	file, _ := os.Stat(logDir)

	if len(file.Name()) > 0 {

		currentDate := MotadataTimeString(consts.LogFileDateFormat).Format()

		var logFile string

		if len(module) > 0 {

			logFile = logDir + consts.PathSeparator + logFile + MotadataString(consts.LogFile).ReplaceAll("@@@", currentDate).ReplaceAll("###", MotadataString(module)).ToString()

		} else {

			logFile = logDir + consts.PathSeparator + logFile + MotadataString(consts.LogFile).ReplaceAll("@@@", currentDate).ReplaceAll("###", "Plugin Engine").ToString()
		}

		file, _ := os.Stat(logFile)

		return len(file.Name()) > 0
	}

	return false
}

func TestLoggerTraceModule(t *testing.T) {

	assertions := assert.New(t)

	SetLogLevel(0)

	traceLog := NewLogger("Test", "Windows")

	traceLog.Trace("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult("Windows"))
}

func TestLoggerTraceNoModule(t *testing.T) {

	assertions := assert.New(t)

	SetLogLevel(0)

	logger := NewLogger("Test", "")

	logger.Trace("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult(""))
}

func TestLoggerTraceNoModuleLowerLogLevel(t *testing.T) {

	assertions := assert.New(t)

	SetLogLevel(1)

	logger := NewLogger("Test", "")

	logger.Trace("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult(""))
}

func TestLoggerTraceModuleLowerLogLevel(t *testing.T) {

	assertions := assert.New(t)

	logger := NewLogger("Test", "Windows")

	SetLogLevel(0)

	logger.Trace("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult("Windows"))
}

func TestLoggerDebugModule(t *testing.T) {

	assertions := assert.New(t)

	logger := NewLogger("Test", "Windows")

	SetLogLevel(1)

	logger.Debug("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult("Windows"))
}

func TestLoggerDebugNoModule(t *testing.T) {

	assertions := assert.New(t)

	SetLogLevel(1)

	logger := NewLogger("Test", "")

	logger.Debug("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult(""))
}

func TestLoggerDebugWithModuleLowerLogLevel(t *testing.T) {

	assertions := assert.New(t)

	logger := NewLogger("Test", "Windows")

	SetLogLevel(2)

	logger.Debug("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult(""))
}

func TestLoggerDebugNoModuleLowerLogLevel(t *testing.T) {

	assertions := assert.New(t)

	SetLogLevel(2)

	logger := NewLogger("Test", "")

	logger.Debug("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult(""))
}

func TestLoggerInfoNoModule(t *testing.T) {

	assertions := assert.New(t)

	SetLogLevel(2)

	logger := NewLogger("Test", "")

	logger.Info("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult(""))
}

func TestLoggerInfoModule(t *testing.T) {

	assertions := assert.New(t)

	logger := NewLogger("Test", "Windows")

	logger.Info("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult(""))
}

func TestLoggerWarnModule(t *testing.T) {

	assertions := assert.New(t)

	logger := NewLogger("Test", "Windows")

	SetLogLevel(3)

	logger.Warn("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult(""))
}

func TestLoggerWarnNoModule(t *testing.T) {

	assertions := assert.New(t)

	SetLogLevel(3)

	logger := NewLogger("Test", "")

	logger.Warn("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult(""))
}

func TestLoggerFatalModule(t *testing.T) {

	assertions := assert.New(t)

	logger := NewLogger("Test", "Windows")

	logger.Fatal("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult("Windows"))
}

func TestLoggerFatalNoModule(t *testing.T) {

	assertions := assert.New(t)

	logger := NewLogger("Test", "")

	logger.Fatal("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult(""))
}

func TestLoggerFatalModuleLowerLogLevel(t *testing.T) {

	assertions := assert.New(t)

	logger := NewLogger("Test", "")

	logger.Fatal("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult("Windows"))
}

func TestLoggerFatalNoModuleLowerLogLevel(t *testing.T) {

	assertions := assert.New(t)

	logger := NewLogger("Test", "")

	logger.Fatal("Hello World ....... inside Trace with module..............")

	assertions.True(assertLoggerTestResult(""))
}
