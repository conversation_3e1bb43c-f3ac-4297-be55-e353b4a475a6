/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package globals

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/stretchr/testify/assert"
	"math/rand"
	"motadatasdk/consts"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"testing"
	"time"
)

var (
	envType string

	versionPattern = regexp.MustCompile("[.][0-9_x]*$")
)

const (
	PluginMetrics = "plugin-metrics"
)

func init() {

	var context MotadataMap

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(consts.CurrentDir))))) + consts.PathSeparator + consts.ConfigDirectory + consts.PathSeparator + "context.json")

	if err == nil {

		err = json.Unmarshal(bytes, &context)

		SetEnvironmentType(context.GetStringValue("env.type"))
	}

}

func AssertNodeDiscoveryTestResult(result MotadataMap, props MotadataStringList, assertions *assert.Assertions) {

	if !props.IsNotEmpty() {

		props = MotadataStringList{consts.ObjectIP}
	}

	assertions.Equal(consts.StatusSucceed, result[consts.Status])

	if result.Contains(consts.Objects) {

		assertions.True(len(result.GetMapSliceValue(consts.Objects)) > 0)
	}

	for _, prop := range props {

		assertions.NotNil(result.GetMapSliceValue(consts.Objects)[0][prop])
	}

	assertions.False(result.Contains(consts.Errors))
}

func AssertOffice365OnlineDiscoveryTestResult(result MotadataMap, assertions *assert.Assertions) {

	assertions.False(result.Contains(consts.Error))

	assertions.Equal(consts.StatusSucceed, result[consts.Status])

	assertions.NotNil(result.GetMapSliceValue(consts.Objects))

	assertions.Greater(len(result.GetMapSliceValue(consts.Objects)), 0)

	for _, object := range result.GetMapSliceValue(consts.Objects) {

		assertions.Len(object, 4)

		assertions.True(object.Contains(consts.ObjectType) && len(object.GetMotadataStringValue(consts.ObjectType)) > 0)

		assertions.True(object.Contains(consts.ObjectName) && len(object.GetMotadataStringValue(consts.ObjectName)) > 0)

		assertions.True(object.Contains(consts.ObjectTarget) && len(object.GetMotadataStringValue(consts.ObjectTarget)) > 0)

		assertions.True(object.Contains(consts.ObjectAccountId) && len(object.GetMotadataStringValue(consts.ObjectAccountId)) > 0)

	}

}

func AssertSystemProcessDiscoveryTestResult(result MotadataMap, props MotadataStringList, assertions *assert.Assertions) {

	if !props.IsNotEmpty() {

		props = MotadataStringList{consts.ObjectIP}
	}

	assertions.Equal(consts.StatusSucceed, result[consts.Status])

	if result.Contains(consts.Objects) {

		assertions.True(len(result.GetMapSliceValue(consts.Objects)) > 0)
	}
	for _, prop := range props {

		if result.Contains(consts.Objects) {

			assertions.NotNil(result.GetMapSliceValue(consts.Objects)[0][prop])

		} else {

			assertions.Contains(result, consts.Objects)

		}
	}

	for _, object := range result.GetMapSliceValue(consts.Objects) {

		assertions.NotNil(object.GetStringValue(consts.ObjectName))

		assertions.True(object.GetMotadataStringValue(consts.ObjectName).Contains(consts.Separator)) // process object's should have command value with process name separated with '|'.
	}

	assertions.False(result.Contains(consts.Errors))
}

func AssertInvalidNodeDiscoveryTestResult(result MotadataMap, errorCode string, numberOfErrors int, assertions *assert.Assertions) {

	assertions.Equal(consts.StatusFail, result[consts.Status])

	assertions.True(result.Contains(consts.Errors))

	assertions.True(len(result.GetStringMapSliceValue(consts.Errors)) > 0)

	errors := result.GetStringMapSliceValue(consts.Errors)

	assertions.Equal(numberOfErrors, len(errors))

	for _, err := range errors {

		assertions.Equal(errorCode, err[consts.ErrorCode])

		assertions.GreaterOrEqual(len(err[consts.Message]), 0)
	}
}

func AssertNodeDiscoveryMultipleCredentialsTestResult(result MotadataMap, props MotadataStringList, assertions *assert.Assertions) {

	if !props.IsNotEmpty() {

		props = MotadataStringList{consts.ObjectIP}
	}

	assertions.Equal(consts.StatusSucceed, result[consts.Status])

	assertions.True(result.Contains(consts.Objects))

	assertions.True(len(result.GetMapSliceValue(consts.Objects)) > 0)

	for _, prop := range props {

		assertions.NotNil(result.GetMapSliceValue(consts.Objects)[0][prop])
	}
}

func AssertInvalidNodeDiscoveryMultipleCredentialsTestResult(result MotadataMap, errorCode string, assertions *assert.Assertions) {

	assertions.Equal(consts.StatusFail, result[consts.Status])

	assertions.True(result.Contains(consts.Errors))

	assertions.True(len(result.GetStringMapSliceValue(consts.Errors)) > 0)

	flag := false

	for _, err := range result.GetStringMapSliceValue(consts.Errors) {

		if errorCode == err[consts.ErrorCode] {

			flag = true
		}

		assertions.GreaterOrEqual(len(err[consts.Message]), 0)
	}
	assertions.True(flag)
}

// AssertInvalidMultipleErrorsTestResult for testing function result with multiple errors
func AssertInvalidMultipleErrorsTestResult(eventType MotadataString, result MotadataMap, errorCodes MotadataStringList, assertions *assert.Assertions) {

	codes := make(MotadataMap)

	for _, errorCode := range errorCodes {

		codes[errorCode] = codes.GetIntValue(errorCode) + 1

	}

	if eventType == consts.EventDiscovery {

		assertions.Equal(consts.StatusFail, result[consts.Status])

	}

	assertions.True(result.Contains(consts.Errors))

	errors := result.GetStringMapSliceValue(consts.Errors)

	assertions.Equal(len(errorCodes), len(errors))

	for _, err := range errors {

		assertions.Contains(codes, err[consts.ErrorCode])

		assertions.GreaterOrEqual(len(err[consts.Message]), 0)

		codes[err[consts.ErrorCode]] = codes.GetIntValue(err[consts.ErrorCode]) - 1

	}

	for _, errCode := range codes {

		assertions.Equal(ToInt(errCode), 0)

	}

}

func AssertInvalidCloudDiscoveryTestResult(result MotadataMap, errorCode string, assertions *assert.Assertions) {

	assertions.Greater(len(result.GetStringMapSliceValue(consts.Errors)), 0)

	assertions.Equal(consts.StatusFail, result[consts.Status])

	for _, err := range result.GetStringMapSliceValue(consts.Errors) {

		assertions.Equal(errorCode, err[consts.ErrorCode])
	}

}

func AssertInvalidMetricPluginTestResult(result MotadataMap, errorCode string, assertions *assert.Assertions) {

	assertions.True(result.Contains(consts.Errors))

	assertions.Greater(len(result.GetStringMapSliceValue(consts.Errors)), 0)

	for _, err := range result.GetStringMapSliceValue(consts.Errors) {

		assertions.Equal(errorCode, err[consts.ErrorCode])
	}
}

func AssertInvalidMetricPluginTestResultErrors(result MotadataMap, errorCode MotadataStringList, assertions *assert.Assertions) {

	assertions.True(result.Contains(consts.Errors))

	assertions.Greater(len(result.GetStringMapSliceValue(consts.Errors)), 0)

	for _, err := range result.GetStringMapSliceValue(consts.Errors) {

		assertions.Contains(errorCode, err[consts.ErrorCode])

	}

}

func GetLinuxNodes(context MotadataMap) []MotadataMap {

	nodes := []MotadataMap{
		GetContext(context, MotadataStringList{consts.Linux, "172.16.8.159"}),
		GetContext(context, MotadataStringList{consts.Linux, "172.16.8.188"}),
		GetContext(context, MotadataStringList{consts.Linux, "rhel", "7.5"}),
		GetContext(context, MotadataStringList{consts.Linux, "rhel", "7.6"}),
		GetContext(context, MotadataStringList{consts.Linux, "rhel", "7.7"}),
		GetContext(context, MotadataStringList{consts.Linux, "centos", "7.5"}),
		GetContext(context, MotadataStringList{consts.Linux, "centos", "7.6"}),
		GetContext(context, MotadataStringList{consts.Linux, "centos", "7.7"}),
		GetContext(context, MotadataStringList{consts.Linux, "ubuntu", "14"}),
		GetContext(context, MotadataStringList{consts.Linux, "ubuntu", "16"}),
		GetContext(context, MotadataStringList{consts.Linux, "ubuntu", "18"}),
		GetContext(context, MotadataStringList{consts.Linux, "172.16.8.173"}),
		GetContext(context, MotadataStringList{consts.Linux, "172.16.8.52"}),
	}

	return nodes
}

func GetLinuxDiscoveryNodes(context MotadataMap) []MotadataMap {

	nodes := []MotadataMap{

		GetContext(context, MotadataStringList{consts.Linux, "172.16.8.159.discovery"}),
		GetContext(context, MotadataStringList{consts.Linux, "172.16.8.188.discovery"}),
		GetContext(context, MotadataStringList{consts.Linux, "rhel", "7.5.discovery"}),
		GetContext(context, MotadataStringList{consts.Linux, "rhel", "7.6.discovery"}),
		GetContext(context, MotadataStringList{consts.Linux, "rhel", "7.7.discovery"}),
		GetContext(context, MotadataStringList{consts.Linux, "centos", "7.5.discovery"}),
		GetContext(context, MotadataStringList{consts.Linux, "centos", "7.6.discovery"}),
		GetContext(context, MotadataStringList{consts.Linux, "centos", "7.7.discovery"}),
		GetContext(context, MotadataStringList{consts.Linux, "ubuntu", "14.discovery"}),
		GetContext(context, MotadataStringList{consts.Linux, "ubuntu", "16.discovery"}),
		GetContext(context, MotadataStringList{consts.Linux, "ubuntu", "18.discovery"}),
		GetContext(context, MotadataStringList{consts.Linux, "172.16.8.173.discovery"}),
		GetContext(context, MotadataStringList{consts.Linux, "172.16.8.52.discovery"}),
	}

	return nodes
}

func GetLinuxProcessPluginNodes(context map[string]interface{}) []MotadataMap {

	nodes := []MotadataMap{

		GetContext(context, MotadataStringList{consts.Linux, "172.16.8.159"}),
		GetContext(context, MotadataStringList{consts.Linux, "172.16.8.188"}),
		GetContext(context, MotadataStringList{consts.Linux, "172.16.8.47"}),
		GetContext(context, MotadataStringList{consts.Linux, "rhel", "7.5"}),
		GetContext(context, MotadataStringList{consts.Linux, "centos", "7.5"}),
		GetContext(context, MotadataStringList{consts.Linux, "ubuntu", "14"}),
	}

	return nodes
}

func GetSolarisNodes(context map[string]interface{}) []MotadataMap {

	nodes := []MotadataMap{

		GetContext(context, MotadataStringList{consts.Solaris, "valid.version10"}),
		GetContext(context, MotadataStringList{consts.Solaris, "valid.version11"}),
	}

	return nodes
}

func BeforeTest(context MotadataMap) MotadataMap {

	context = context.Copy()

	context[consts.Errors] = []MotadataStringMap{}

	context[consts.Result] = make(MotadataMap)

	return context
}

func GetContext(contexts MotadataMap, keys MotadataStringList) (result MotadataMap) {

	for _, key := range keys {

		if result == nil {

			result = contexts.GetMapValue(key)

		} else {

			result = result.GetMapValue(key)

		}

	}

	return
}

func AssertCloudDiscoveryTestResult(result MotadataMap, objectLocationKey string, downInstanceDiscovery bool, assertions *assert.Assertions) {

	if result.Contains(consts.Errors) {

		assertions.Equal(0, len(result.GetStringMapSliceValue(consts.Errors)))

	}

	assertions.Equal(consts.StatusSucceed, result.GetStringValue(consts.Status))

	assertions.Greater(len(result.GetMapSliceValue(consts.Objects)), 0)

	for _, object := range result.GetMapSliceValue(consts.Objects) {

		count := 6

		assertions.True(assertions.NotNil(object.GetStringValue(consts.ObjectType)), len(object.GetStringValue(consts.ObjectType)) > 0)

		if (MotadataStringList{"Amazon EC2", "AWS EC2", "Azure VM"}).Contains(MotadataString(object.GetStringValue(consts.ObjectType))) {

			assertions.True(assertions.NotNil(object.GetStringValue(consts.ObjectIP)), len(object.GetStringValue(consts.ObjectIP)) >= 0)

			count += 1

		}

		if object.Contains(consts.Status) {

			count += 1

			if downInstanceDiscovery {

				assertions.True(object.GetStringValue(consts.Status) == consts.StatusDown || object.GetStringValue(consts.Status) == consts.StatusUp)

			} else {

				assertions.True(object.GetStringValue(consts.Status) == consts.StatusUp)
			}

		}

		if object.Contains(consts.ObjectMakeModel) {

			count += 1

			assertions.Greater(len(object.GetStringValue(consts.ObjectMakeModel)), 0)
		}

		if object.Contains(consts.ObjectInstanceId) {

			count += 1

			assertions.Greater(len(object.GetStringValue(consts.ObjectInstanceId)), 0)
		}

		assertions.True(assertions.NotNil(object[objectLocationKey]), len(object.GetStringValue(objectLocationKey)) > 0)

		assertions.Equal(count, len(object))

		assertions.True(assertions.True(object.GetMapValue(consts.ObjectContext).IsNotEmpty()))

		assertions.True(assertions.NotNil(object.GetStringValue(consts.ObjectType), len(object.GetStringValue(consts.ObjectType)) > 0))

		assertions.True(assertions.NotNil(object.GetStringValue(consts.ObjectName), len(object.GetStringValue(consts.ObjectName)) > 0))

		assertions.True(assertions.NotNil(object.GetStringValue(consts.ObjectTarget), len(object.GetStringValue(consts.ObjectTarget)) > 0))

		assertions.True(assertions.NotNil(object.GetStringValue(consts.ObjectAccountId), len(object.GetStringValue(consts.ObjectAccountId)) > 0))

	}
}

func AssertRunbookPluginTestResult(result MotadataMap, t *testing.T, success bool, errorCode string) {
	assertions := assert.New(t)

	assertions.NotNil(result)

	if success {
		assertions.True(len(result) > 0)

		assertions.True(result[consts.Status] == consts.StatusSucceed)

		if reflect.ValueOf(result[consts.Result]).Kind().String() == "string" {
			assertions.NotNil(result.GetStringValue(consts.Result))

			assertions.True(len(result.GetStringValue(consts.Result)) > 0)
		} else if reflect.ValueOf(result[consts.Result]).Kind().String() == "MotadataMap" {
			assertions.NotNil(result.GetMapValue(consts.Result))

			assertions.True(len(result.GetMapValue(consts.Result)) > 0)
		} else if reflect.ValueOf(result[consts.Result]).Kind().String() == "map[string]interface {}" {
			assertions.NotNil(result.GetListValue(consts.Result))

			assertions.True(len(result.GetListValue(consts.Result)) > 0)
		} else if reflect.ValueOf(result[consts.Result]).Kind().String() == "slice" {
			assertions.NotNil(result.GetMapSliceValue(consts.Result))

			assertions.True(len(result.GetMapSliceValue(consts.Result)) > 0)
		}
	} else {
		assertions.Nil(result[consts.Result])

		assertions.True(result[consts.Status] == consts.StatusFail)

		assertions.True(len(result.GetStringMapSliceValue(consts.Errors)) > 0)

		if errorCode != consts.BlankString {

			foundErrorCode := false

			for _, err := range result.GetStringMapSliceValue(consts.Errors) {

				if err[consts.ErrorCode] == errorCode {

					foundErrorCode = true

					break
				}
			}

			assertions.True(foundErrorCode)
		}
	}
}

func AssertMetricPluginTestResult(result MotadataMap, t *testing.T, success bool, errorCode string) {

	assertions := assert.New(t)

	assertions.NotNil(result)

	if success {

		assertions.True(len(result) > 0)

		assertions.True(result[consts.Status] == consts.StatusSucceed)

		if reflect.ValueOf(result[consts.Result]).Kind().String() == "string" {
			assertions.NotNil(result.GetStringValue(consts.Result))

			assertions.True(len(result.GetStringValue(consts.Result)) > 0)
		} else if reflect.ValueOf(result[consts.Result]).Kind().String() == "MotadataMap" {
			assertions.NotNil(result.GetMapValue(consts.Result))

			assertions.True(len(result.GetMapValue(consts.Result)) > 0)
		} else if reflect.ValueOf(result[consts.Result]).Kind().String() == "map[string]interface {}" {
			assertions.NotNil(result.GetListValue(consts.Result))

			assertions.True(len(result.GetListValue(consts.Result)) > 0)
		} else if reflect.ValueOf(result[consts.Result]).Kind().String() == "slice" {
			assertions.NotNil(result.GetMapSliceValue(consts.Result))

			assertions.True(len(result.GetMapSliceValue(consts.Result)) > 0)
		}
	} else {
		assertions.Nil(result[consts.Result])

		assertions.True(result[consts.Status] == consts.StatusFail)

		assertions.True(len(result.GetStringMapSliceValue(consts.Errors)) > 0)

		if errorCode != consts.BlankString {

			foundErrorCode := false

			for _, err := range result.GetStringMapSliceValue(consts.Errors) {

				if err[consts.ErrorCode] == errorCode {

					foundErrorCode = true

					break
				}
			}

			assertions.True(foundErrorCode)
		}
	}
}

func SetEnvironmentType(env string) {
	envType = env
}

func GetEnvironmentType() (result string) {
	return envType
}

func IsValidResult(metrics MotadataMap, correlationMetric string) bool {

	for metric, value := range metrics {

		if metric == correlationMetric {

			continue
		}

		if reflect.TypeOf(value).String() == "[]globals.MotadataMap" { // means instance level metrics

			for _, instance := range metrics.GetMapSliceValue(metric) {

				if !instance.Contains(metric) {

					return false
				}
			}
		}
	}

	return true
}

func LoadMetricGroups(file string) MotadataMap {

	var groups MotadataMap

	byteBuffer, _ := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(consts.CurrentDir))))) + consts.PathSeparator + PluginMetrics + consts.PathSeparator + file)

	_ = json.Unmarshal(byteBuffer, &groups)

	return groups

}

func GetMetricNames(group string, groups MotadataMap, scalarMetricGroup bool) MotadataStringList {

	var metrics MotadataStringList

	if scalarMetricGroup == true {

		for _, value := range groups.GetKeys() {

			if value != group && !versionPattern.MatchString(value) {

				metrics = append(metrics, value)

			}
		}
	}

	for _, metricName := range groups.GetSliceValue(group) {

		if (GetEnvironmentType() == consts.ProdEnv || !(ToMap(metricName).Contains("env.metric.dev") && ToMap(metricName).GetStringValue("env.metric.dev") == "no")) && !(ToMap(metricName).Contains("is.random") && ToMap(metricName).GetStringValue("is.random") == consts.Yes) { // if any metric contains "env.metric.dev" key in dev environment, then we will not include that metric as a counter. (for cloud plugins)

			metrics = append(metrics, ToMap(metricName).GetStringValue(consts.MetricName))
		}
	}

	sort.Strings(metrics)

	return metrics

}

func MockBlockingSessions(driver MotadataString, dataSource MotadataString, tableName MotadataString, logger *Logger) {

	go updateQuery(driver.ToString(), dataSource.ToString(), tableName.ToString(), logger)

	time.Sleep(1 * time.Second)

	go selectOneQuery(driver.ToString(), dataSource.ToString(), tableName.ToString(), logger)

	go selectStarQuery(driver.ToString(), dataSource.ToString(), tableName.ToString(), logger)

}

func MockSlowQueryResult(driver MotadataString, dataSource MotadataString, logger *Logger) {

	db, err := sql.Open(driver.ToString(), dataSource.ToString())

	defer func() {
		_ = db.Close()
	}()

	if err != nil {

		logger.Warn(MotadataString(fmt.Sprintf("Error Occured while opening connection %v", err)))

	}

	tx, err := db.Begin()

	if err != nil {

		logger.Warn(MotadataString(fmt.Sprintf("Error Occured while begin transaction %v", err)))

	}

	//_, err = tx.Query(fmt.Sprintf("select * from sys.tables WAITFOR DELAY '00:00:%s'", strconv.Itoa(rand.Intn(9)+1)))

	_, err = tx.Query(fmt.Sprintf("select top %s * from sys.tables", strconv.Itoa(rand.Intn(9)+1)))

	if err != nil {

		logger.Warn(MotadataString(fmt.Sprintf("Error Occured while executing query %v", err)))

	}

	_ = tx.Commit()

}

func updateQuery(driver string, dataSource string, tableName string, logger *Logger) {

	//Here we can't use databaseClient because we have to wait 30 seconds before committing the transaction in-order to keep session active.
	db, err := sql.Open(driver, dataSource)

	defer func() {
		_ = db.Close()
	}()

	if err != nil {

		logger.Warn(MotadataString(fmt.Sprintf("Error Occured while opening connection %v", err)))

	}

	tx, err := db.Begin()

	if err != nil {

		logger.Warn(MotadataString(fmt.Sprintf("Error Occured while begin transaction %v", err)))

	}

	_, err = tx.Query(fmt.Sprintf("update %s set name='sample' where id=1", tableName))

	if err != nil && MotadataString(err.Error()).Contains("Invalid object name") { // if table not exist, create dummy table

		logger.Warn(MotadataString(fmt.Sprintf("Error Occured while executing query %v", err)))

		_, err = tx.Query(fmt.Sprintf("CREATE TABLE %s (id INT PRIMARY KEY, name VARCHAR(50) NOT NULL)", tableName))

		if err == nil { // insert dummy records

			_, err = tx.Query(fmt.Sprintf("INSERT INTO %s (ID, Name) VALUES (1, 'Alice'), (2, 'Bob')", tableName))

			if err != nil {

				logger.Warn(MotadataString(fmt.Sprintf("Error Occured while executing query %v", err)))

			}

		}

	}

	time.Sleep(30 * time.Second)

	_ = tx.Commit()

}

func selectOneQuery(driver string, dataSource string, tableName string, logger *Logger) {

	db, err := sql.Open(driver, dataSource)

	defer func() {
		_ = db.Close()
	}()

	if err != nil {

		logger.Warn(MotadataString(fmt.Sprintf("Error Occured while opening connection %v", err)))

	}

	tx, err := db.Begin()

	if err != nil {

		logger.Warn(MotadataString(fmt.Sprintf("Error Occured while begin transaction %v", err)))

	}

	_, err = tx.Query(fmt.Sprintf("select * from %s where id=1", tableName))

	if err != nil {

		logger.Warn(MotadataString(fmt.Sprintf("Error Occured while executing query %v", err)))

	}

	time.Sleep(30 * time.Second)

	_ = tx.Commit()

}

func selectStarQuery(driver string, dataSource string, tableName string, logger *Logger) {

	db, err := sql.Open(driver, dataSource)

	defer func() {
		_ = db.Close()
	}()

	if err != nil {

		logger.Warn(MotadataString(fmt.Sprintf("Error Occured while opening connection %v", err)))

	}

	tx, err := db.Begin()

	if err != nil {

		logger.Warn(MotadataString(fmt.Sprintf("Error Occured while begin transaction %v", err)))

	}

	_, err = tx.Query(fmt.Sprintf("select * from %s", tableName))

	if err != nil {

		logger.Warn(MotadataString(fmt.Sprintf("Error Occured while executing query %v", err)))

	}

	time.Sleep(30 * time.Second)

	_ = tx.Commit()

}
