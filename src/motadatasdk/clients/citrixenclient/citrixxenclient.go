/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package citrixenclient

import (
	xenapi "github.com/terra-farm/go-xen-api-client"
	"motadatasdk/clients/httpclient"
	"motadatasdk/consts"
	. "motadatasdk/globals"
)

type CitrixXenClient struct {
	client *xenapi.Client

	session xenapi.SessionRef

	userName MotadataString

	password MotadataString

	target MotadataString

	protocol MotadataString

	port MotadataUINT16

	timeout MotadataINT

	logger *Logger

	errors []MotadataStringMap
}

func (citrixXenClient *CitrixXenClient) SetContext(context MotadataMap, logger *Logger) *CitrixXenClient {

	return citrixXenClient.setTarget(context).setPort(context).setUsername(context).setPassword(context).setProtocol(context).setTimeout(context).setLogger(logger)
}

func (citrixXenClient *CitrixXenClient) setLogger(loggerObj *Logger) *CitrixXenClient {

	citrixXenClient.logger = loggerObj

	return citrixXenClient
}

func (citrixXenClient *CitrixXenClient) setTarget(context MotadataMap) *CitrixXenClient {

	if context.Contains(consts.ObjectIP) {

		citrixXenClient.target = context.GetMotadataStringValue(consts.ObjectIP)

	} else {

		citrixXenClient.target = consts.LocalIP
	}

	return citrixXenClient
}

func (citrixXenClient *CitrixXenClient) setPort(context MotadataMap) *CitrixXenClient {

	if context.Contains(consts.Port) {

		citrixXenClient.port = context.GetUINT16Value(consts.Port)

	} else {

		citrixXenClient.port = MotadataUINT16(443)
	}

	return citrixXenClient
}

func (citrixXenClient *CitrixXenClient) setUsername(context MotadataMap) *CitrixXenClient {

	if context.Contains(consts.UserName) {

		citrixXenClient.userName = context.GetMotadataStringValue(consts.UserName)

	}

	return citrixXenClient
}

func (citrixXenClient *CitrixXenClient) setPassword(context MotadataMap) *CitrixXenClient {

	if context.Contains(consts.Password) {

		citrixXenClient.password = context.GetMotadataStringValue(consts.Password)

	}

	return citrixXenClient
}

func (citrixXenClient *CitrixXenClient) setProtocol(context MotadataMap) *CitrixXenClient {

	if context.Contains(httpclient.URLProtocol) {

		citrixXenClient.protocol = context.GetMotadataStringValue(httpclient.URLProtocol)

	} else {

		citrixXenClient.protocol = httpclient.URLProtocolHTTPs

	}

	return citrixXenClient
}

func (citrixXenClient *CitrixXenClient) setTimeout(context MotadataMap) *CitrixXenClient {

	if context.Contains(consts.Timeout) {

		citrixXenClient.timeout = context.GetINTValue(consts.Timeout)

	} else {

		citrixXenClient.timeout = MotadataINT(60)
	}

	return citrixXenClient
}

func (citrixXenClient *CitrixXenClient) GetErrors() []MotadataStringMap {

	return citrixXenClient.errors
}
