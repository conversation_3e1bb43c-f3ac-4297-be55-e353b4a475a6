/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package citrixenclient

import (
	"crypto/tls"
	"fmt"
	"github.com/beevik/etree"
	xenapi "github.com/terra-farm/go-xen-api-client"
	"motadatasdk/clients/httpclient"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"net/http"
	"runtime"
	"strings"
	"time"
)

var loggerObj = NewLogger("Panic Error", "Panic Error Log")

func SetupCleanupRoutine(citrixXenClient *CitrixXenClient, response MotadataMap, requestType MotadataString, responses chan<- MotadataMap) {

	if r := recover(); r != nil {

		err := make(MotadataStringMap)

		err[consts.Message] = fmt.Sprintf("%v", r)

		err[consts.ErrorCode] = consts.ErrorCodeInternalError

		err[consts.Error] = fmt.Sprintf("%v", r)

		response[consts.Errors] = append(response.GetStringMapSliceValue(consts.Errors), err)

		stackTraceBytes := make([]byte, consts.StackTraceSizeInBytes)

		loggerObj.Fatal(MotadataString(fmt.Sprintf("serious error %v occurred in the plugin %v\nStack Trace ==> %v\n", r, RemoveSensitiveFields(response).ToJSON(), string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))))
	}

	DestroyCitrixSession(citrixXenClient, citrixXenClient.session, citrixXenClient.client, citrixXenClient.target, citrixXenClient.port.ToINT())

	if responses != nil {

		if requestType == consts.EventPoll {

			AddStatusField(response)

		}

		responses <- response

	}

}

func GetCitrixConnection(citrixXenClient *CitrixXenClient, context MotadataMap, ip MotadataString) (xenapi.SessionRef, *xenapi.Client) {

	var session xenapi.SessionRef

	session = consts.BlankString

	// Custom TLS Config for weak ciphers
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true, // WARNING: Disables certificate validation (use only if needed)
		CipherSuites: []uint16{
			tls.TLS_RSA_WITH_AES_128_CBC_SHA,
			tls.TLS_RSA_WITH_AES_256_CBC_SHA,
			tls.TLS_RSA_WITH_AES_128_CBC_SHA256,
		},
		MinVersion: tls.VersionTLS10, // Allow TLS 1.0 and above
	}

	transport := &http.Transport{
		TLSClientConfig: tlsConfig,
		IdleConnTimeout: 30 * time.Second,
	}

	url := fmt.Sprintf("%s://%v:%d/", citrixXenClient.protocol, ip, citrixXenClient.port)

	client, err := xenapi.NewClient(url, transport)

	if err != nil {

		citrixXenClient.session = session

		citrixXenClient.client = client

		citrixXenClient.errors = append(citrixXenClient.GetErrors(), MotadataStringMap{

			consts.Error: err.Error(),

			consts.Message: "unable to create a client",

			consts.ErrorCode: consts.ErrorCodeConnectionFailed,
		})

	} else {

		session, err = client.Session.LoginWithPassword(context.GetStringValue(consts.UserName), context.GetStringValue(consts.Password), "1.0", consts.BlankString)

		if err != nil {

			errorMessage := MotadataString(err.Error())

			errorCode := consts.ErrorCodeConnectionFailed

			if errorMessage.Contains("SESSION_AUTHENTICATION_FAILED") {

				errorMessage = MotadataString(fmt.Sprintf(consts.ErrorMessageInvalidCredentials, ip, context.GetIntValue(consts.Port)))

				errorCode = consts.ErrorCodeInvalidCredentials

			} else if errorMessage.Contains("HOST_IS_SLAVE") {

				return GetCitrixConnection(citrixXenClient, context, MotadataString(strings.Split(err.Error(), "API Error: HOST_IS_SLAVE ")[1]).Strip()) // hack: when we connect to cluster using slave it gives error and
				// return master ip for the hint.... so try to reconnect using that hint (master ip)

			} else if errorMessage.Contains("no route to host") || errorMessage.Contains("connection reset by peer") {

				errorMessage = MotadataString(fmt.Sprintf(consts.ErrorMessageInvalidPort, citrixXenClient.port))

				errorCode = consts.ErrorCodeInvalidPort

			} else if errorMessage.Contains("timed out") || errorMessage.Contains("connection refused") {

				if IsOpened(citrixXenClient.port, ip) {

					errorMessage = MotadataString(fmt.Sprintf(consts.ErrorMessageConnectionFailed, consts.BlankString, ip, citrixXenClient.port))

					errorCode = consts.ErrorCodeConnectionFailed

				} else {

					errorMessage = MotadataString(fmt.Sprintf(consts.ErrorMessageInvalidPort, citrixXenClient.port))

					errorCode = consts.ErrorCodeInvalidPort

				}

			} else {

				errorMessage = MotadataString(fmt.Sprintf(consts.ErrorMessageConnectionFailed, consts.BlankString, ip, citrixXenClient.port))

				errorCode = consts.ErrorCodeConnectionFailed
			}

			citrixXenClient.session = session

			citrixXenClient.client = client

			citrixXenClient.errors = append(citrixXenClient.GetErrors(), MotadataStringMap{

				consts.Error: err.Error(),

				consts.ErrorCode: errorCode,

				consts.Message: errorMessage.ToString(),
			})

			return session, client

		} else {

			if _, err = client.Host.GetAll(session); err != nil && strings.Contains(err.Error(), "HOST_IS_SLAVE") {

				return GetCitrixConnection(citrixXenClient, context, MotadataString(strings.Split(err.Error(), "API Error: HOST_IS_SLAVE ")[1]).Strip())

			}

			citrixXenClient.session = session

			citrixXenClient.client = client

			return session, client

		}
	}

	return session, client
}

func DestroyCitrixSession(citrixXenClient *CitrixXenClient, session xenapi.SessionRef, client *xenapi.Client, ip MotadataString, port MotadataINT) {

	if session != consts.BlankString && client != nil {

		_ = client.Session.Logout(session)

		citrixXenClient.logger.Debug(MotadataString(fmt.Sprintf(consts.InfoMessageConnectionDestroyed, ip, port.ToString())))

	}

}

func DiscoverCitrixXen(citrixXenClient *CitrixXenClient, context MotadataMap, logger *Logger) MotadataMap {

	result := make(MotadataMap)

	result[consts.Status] = consts.StatusFail

	credentialProfiles := context.GetSliceValue(consts.DiscoveryCredentialProfiles)

	for _, credentialProfile := range credentialProfiles {

		credentialProfile := ToMap(credentialProfile)

		credentialProfile[consts.ObjectIP] = context[consts.ObjectIP]

		credentialProfile[consts.Port] = context[consts.Port]

		credentialProfile[consts.Timeout] = context[consts.Timeout]

		credentialProfile[httpclient.URLProtocol] = context[httpclient.URLProtocol]

		citrixXenClient.SetContext(credentialProfile, logger)

		session, _ := GetCitrixConnection(citrixXenClient, credentialProfile, context.GetMotadataStringValue(consts.ObjectIP))

		if session != consts.BlankString {

			result[consts.ObjectCredentialProfile] = credentialProfile[consts.Id]

			result[consts.CredentialProfileName] = credentialProfile[consts.CredentialProfileName]

			break
		}
	}

	buildCitrixDiscoveryResult(citrixXenClient, context, result)

	if result.GetStringValue(consts.Status) == consts.StatusFail && len(citrixXenClient.GetErrors()) == 0 {

		citrixXenClient.errors = append(citrixXenClient.GetErrors(), MotadataStringMap{

			consts.ErrorCode: consts.ErrorCodeNoResponse,

			consts.Message: "Citrix Xen API is not accessible",
		})
	}

	if len(citrixXenClient.GetErrors()) > 0 {

		result[consts.Errors] = citrixXenClient.GetErrors()
	}

	return result

}

func buildCitrixDiscoveryResult(citrixXenClient *CitrixXenClient, context, result MotadataMap) MotadataMap {

	var objects []MotadataMap

	if citrixXenClient.session != consts.BlankString {

		result[consts.Status] = consts.StatusSucceed

		citrixXenClient.logger.Debug(MotadataString("citrixxen api connection created on " + context.GetStringValue(consts.ObjectIP)))

		nodes, err := citrixXenClient.client.Host.GetAll(citrixXenClient.session)

		if err != nil {

			citrixXenClient.errors = append(citrixXenClient.GetErrors(), MotadataStringMap{

				consts.Error: err.Error(),

				consts.ErrorCode: consts.ErrorCodeInternalError,

				consts.Message: "unable to fetch nodes",
			})

		} else {

			loggerObj.Debug(MotadataString("citrixxen api hosts: " + context.GetStringValue(consts.ObjectIP)))

			if nodes != nil && len(nodes) > 0 {

				for _, node := range nodes {

					host, _ := citrixXenClient.client.Host.GetRecord(citrixXenClient.session, node)

					label, _ := citrixXenClient.client.Host.GetNameLabel(citrixXenClient.session, node)

					if host.Address == context.GetStringValue(consts.ObjectIP) || label == context.GetStringValue(consts.ObjectIP) {

						object := make(MotadataMap)

						object[consts.ObjectIP] = context.GetStringValue(consts.ObjectIP)

						object[consts.ObjectName] = label

						if context[consts.ObjectHost] == nil {

							object[consts.ObjectHost], _ = citrixXenClient.client.Host.GetHostname(citrixXenClient.session, node)

						}

						var virtualMachines []MotadataMap

						vms, err := citrixXenClient.client.VM.GetAll(citrixXenClient.session)

						if err != nil {

							citrixXenClient.errors = append(citrixXenClient.GetErrors(), MotadataStringMap{

								consts.Error: err.Error(),

								consts.ErrorCode: consts.ErrorCodeInternalError,

								consts.Message: "unable to fetch nodes",
							})

						}

						loggerObj.Debug(MotadataString(fmt.Sprintf("citrixxen node %v vms: %v", node, vms)))

						if vms != nil && len(vms) > 0 {

							for _, vm := range vms {

								affinity, _ := citrixXenClient.client.VM.GetAffinity(citrixXenClient.session, vm)

								uuid, _ := citrixXenClient.client.Host.GetUUID(citrixXenClient.session, affinity)

								if host.UUID == uuid {

									template, _ := citrixXenClient.client.VM.GetIsATemplate(citrixXenClient.session, vm)

									domain, _ := citrixXenClient.client.VM.GetIsControlDomain(citrixXenClient.session, vm)

									if !template && !domain {

										virtualMachine := GetCitrixVMNode(citrixXenClient, citrixXenClient.session, vm, true, consts.ObjectName)

										if len(virtualMachine) > 0 {

											virtualMachines = append(virtualMachines, virtualMachine)
										}

									}
								}
							}
						}

						if len(virtualMachines) > 0 {

							object[consts.Objects] = virtualMachines

						}

						objects = append(objects, object)

					}
				}
			}
		}
	}

	result[consts.Objects] = objects

	return result
}

func GetCitrixVMNode(citrixXenClient *CitrixXenClient, session xenapi.SessionRef, object xenapi.VMRef, discoveryRequest bool, metricName string) MotadataMap {

	vm := make(MotadataMap)

	vm[metricName], _ = citrixXenClient.client.VM.GetNameLabel(session, object)

	ip := consts.ObjectIP

	if !discoveryRequest {

		ip = metricName + ".ip"

		state, err := citrixXenClient.client.VM.GetPowerState(session, object)

		vm[metricName+".power.state"] = getCitrixVMPowerState(state)

		if err != nil {

			citrixXenClient.errors = append(citrixXenClient.GetErrors(), MotadataStringMap{

				consts.Error: err.Error(),

				consts.ErrorCode: consts.ErrorCodeInternalError,

				consts.Message: "unable to fetch power state",
			})

		}

		metric, err := citrixXenClient.client.VM.GetGuestMetrics(session, object)

		if err != nil {

			citrixXenClient.errors = append(citrixXenClient.GetErrors(), MotadataStringMap{

				consts.Error: err.Error(),

				consts.ErrorCode: consts.ErrorCodeInternalError,

				consts.Message: "unable to fetch guest metric",
			})

		}

		if metric != consts.BlankString && len(metric) > 0 && metric != "OpaqueRef:NULL" {

			networks, _ := citrixXenClient.client.VMGuestMetrics.GetNetworks(session, metric)

			if networks != nil && len(networks) > 0 {

				vifs, _ := citrixXenClient.client.VM.GetVIFs(session, object)

				if vifs != nil && len(vifs) > 0 {

					for _, vif := range vifs {

						device, err := citrixXenClient.client.VIF.GetDevice(session, vif)

						if err != nil {

							citrixXenClient.errors = append(citrixXenClient.GetErrors(), MotadataStringMap{

								consts.Error: err.Error(),

								consts.ErrorCode: consts.ErrorCodeInternalError,

								consts.Message: "unable to fetch device",
							})

						}

						if device != consts.BlankString && len(device) > 0 {

							for index := range networks {

								if networks[index] != consts.BlankString && strings.ToLower(index) == device+"/ip" {

									if vm.Contains(ip) {

										vm[ip] = vm.GetStringValue(ip) + "," + networks[index]

									} else {

										vm[ip] = networks[index]
									}
								}

							}

						}
					}
				}

			}

		}

	} else {

		ip = consts.ObjectIP

		if state, err := citrixXenClient.client.VM.GetPowerState(session, object); state == consts.CitrixRunningPowerState {

			if err != nil {

				citrixXenClient.errors = append(citrixXenClient.GetErrors(), MotadataStringMap{

					consts.Error: err.Error(),

					consts.ErrorCode: consts.ErrorCodeInternalError,

					consts.Message: "unable to fetch power state",
				})

			}

			vm[consts.Status] = consts.StatusUp

		} else {

			vm[consts.Status] = consts.StatusDown

		}

		vm[consts.ObjectType] = consts.VMNode

		metric, err := citrixXenClient.client.VM.GetGuestMetrics(session, object)

		if err != nil {

			citrixXenClient.errors = append(citrixXenClient.GetErrors(), MotadataStringMap{

				consts.Error: err.Error(),

				consts.ErrorCode: consts.ErrorCodeInternalError,

				consts.Message: "unable to fetch guest metric",
			})

		}

		if metric != consts.BlankString && len(metric) > 0 && metric != "OpaqueRef:NULL" {

			networks, _ := citrixXenClient.client.VMGuestMetrics.GetNetworks(session, metric)

			if networks != nil && len(networks) > 0 {

				vifs, _ := citrixXenClient.client.VM.GetVIFs(session, object)

				if vifs != nil && len(vifs) > 0 {

					for _, vif := range vifs {

						device, err := citrixXenClient.client.VIF.GetDevice(session, vif)

						if err != nil {

							citrixXenClient.errors = append(citrixXenClient.GetErrors(), MotadataStringMap{

								consts.Error: err.Error(),

								consts.ErrorCode: consts.ErrorCodeInternalError,

								consts.Message: "unable to fetch device",
							})

						}

						if device != consts.BlankString && len(device) > 0 {

							for index := range networks {

								if networks[index] != consts.BlankString && strings.ToLower(index) == device+"/ip" {

									if vm.Contains(ip) {

										vm[ip] = vm.GetStringValue(ip) + "," + networks[index]

									} else {

										vm[ip] = networks[index]
									}
								}

							}

						}
					}
				}

			}

		}

	}

	if !vm.Contains(ip) {

		vm[ip] = consts.BlankString

	}

	return vm

}

func getCitrixVMPowerState(powerState xenapi.VMPowerState) string {

	if powerState == consts.CitrixRunningPowerState {

		return consts.CitrixRunningPowerState

	} else if powerState == consts.CitrixHaltedPowerState {

		return consts.CitrixHaltedPowerState

	} else if powerState == consts.CitrixSuspendedPowerState {

		return consts.CitrixSuspendedPowerState

	} else if powerState == consts.CitrixPausedPowerState {

		return consts.CitrixPausedPowerState
	}

	return consts.BlankString
}

func GetCitrixAPIResponse(citrixXenClient *CitrixXenClient, context MotadataMap) MotadataMap {

	response := make(MotadataMap)

	client := httpclient.HTTPClient{}

	client.SetContext(context, citrixXenClient.logger)

	if client.Init(false) {

		response = client.ExecuteGETRequest()

		if client.GetErrors() != nil {

			citrixXenClient.errors = append(citrixXenClient.errors, client.GetErrors()...)

		} else {

			response = processAPIResponse(citrixXenClient, response)

		}
	}

	return response
}

func processAPIResponse(citrixXenClient *CitrixXenClient, response MotadataMap) MotadataMap {

	attributes := make(MotadataMap)

	if response != nil && response.Contains(httpclient.URLResponseCode) && response.GetIntValue(httpclient.URLResponseCode) == 200 {

		if len(response.GetMotadataStringValue(consts.URLBuffer).Strip()) > 0 {

			document := etree.NewDocument()

			err := document.ReadFromString(ToString(response.GetMotadataStringValue(consts.URLBuffer)))

			if err != nil {

				citrixXenClient.errors = append(citrixXenClient.GetErrors(), MotadataStringMap{

					consts.Error: err.Error(),

					consts.ErrorCode: consts.ErrorCodeInternalError,

					consts.Message: "unable to read the response from http client.Parsing failed for xml response",
				})

				return attributes
			}

			for _, xmlNode := range document.SelectElements("rrd")[0].ChildElements() {

				name := ""

				if xmlNode.Tag == "ds" {

					for _, child := range xmlNode.ChildElements() {

						if child.Tag == "name" {

							name = child.Text()

						} else if child.Tag == "value" {

							attributes[name] = child.Text()

						}

					}

				}
			}

		}

	}

	return attributes

}
