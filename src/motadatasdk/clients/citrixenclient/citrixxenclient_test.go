/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package citrixenclient

import (
	"encoding/json"
	"fmt"
	"github.com/stretchr/testify/assert"
	"motadatasdk/clients/httpclient"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"os"
	"path/filepath"
	"testing"
)

var (
	context MotadataMap

	contexts MotadataMap
)

func TestMain(m *testing.M) {

	SetLogLevel(consts.LogLevelDebug)

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(consts.CurrentDir)))) + consts.PathSeparator + consts.ConfigDirectory + consts.PathSeparator + "context.json")

	if err == nil {

		contexts = make(MotadataMap)

		err = json.Unmarshal(bytes, &contexts)

		if err == nil {

			context = GetContext(contexts, MotadataStringList{"citrix7.1", "valid"})

			m.Run()

		}
	}
}

func TestCitrixXenClientInvalidHost(t *testing.T) {

	citrixXenClient := &CitrixXenClient{}

	assertion := assert.New(t)

	invalidContext := context.Copy()

	invalidContext[consts.ObjectIP] = consts.InvalidHost

	citrixXenClient.SetContext(invalidContext, &loggerObj)

	session, _ := GetCitrixConnection(citrixXenClient, invalidContext, invalidContext.GetMotadataStringValue(consts.ObjectIP))

	assertion.True(session == consts.BlankString)

	assertion.GreaterOrEqual(len(citrixXenClient.GetErrors()), 0)

	assertion.Equal(consts.ErrorCodeInvalidPort, citrixXenClient.GetErrors()[0][consts.ErrorCode])

}

func TestCitrixXenClientInvalidPort(t *testing.T) {

	citrixXenClient := &CitrixXenClient{}

	assertion := assert.New(t)

	invalidContext := context.Copy()

	invalidContext[consts.Port] = consts.InvalidPort

	citrixXenClient.SetContext(invalidContext, &loggerObj)

	session, _ := GetCitrixConnection(citrixXenClient, invalidContext, invalidContext.GetMotadataStringValue(consts.ObjectIP))

	assertion.True(session == consts.BlankString)

	assertion.GreaterOrEqual(len(citrixXenClient.GetErrors()), 0)

	assertion.Equal(consts.ErrorCodeInvalidPort, citrixXenClient.GetErrors()[0][consts.ErrorCode])

}

func TestCitrixXenClientInvalidUsername(t *testing.T) {

	citrixXenClient := &CitrixXenClient{}

	assertion := assert.New(t)

	invalidContext := context.Copy()

	invalidContext[consts.UserName] = consts.InvalidUserName

	citrixXenClient.SetContext(invalidContext, &loggerObj)

	session, _ := GetCitrixConnection(citrixXenClient, invalidContext, invalidContext.GetMotadataStringValue(consts.ObjectIP))

	assertion.True(session == consts.BlankString)

	assertion.GreaterOrEqual(len(citrixXenClient.GetErrors()), 0)

	assertion.Equal(consts.ErrorCodeInvalidCredentials, citrixXenClient.GetErrors()[0][consts.ErrorCode])

}

func TestCitrixXenClientInvalidPassword(t *testing.T) {

	citrixXenClient := &CitrixXenClient{}

	assertion := assert.New(t)

	invalidContext := context.Copy()

	invalidContext[consts.Password] = consts.InvalidPassword

	citrixXenClient.SetContext(invalidContext, &loggerObj)

	session, _ := GetCitrixConnection(citrixXenClient, invalidContext, invalidContext.GetMotadataStringValue(consts.ObjectIP))

	assertion.True(session == consts.BlankString)

	assertion.GreaterOrEqual(len(citrixXenClient.GetErrors()), 0)

	assertion.Equal(consts.ErrorCodeInvalidCredentials, citrixXenClient.GetErrors()[0][consts.ErrorCode])

}

func TestCitrixXenClient(t *testing.T) {

	citrixXenClient := &CitrixXenClient{}

	assertion := assert.New(t)

	citrixXenClient.SetContext(context, &loggerObj)

	session, _ := GetCitrixConnection(citrixXenClient, context, context.GetMotadataStringValue(consts.ObjectIP))

	assertion.True(session != consts.BlankString)

	assertion.Equal(len(citrixXenClient.GetErrors()), 0)

}

func TestCitrixXenClientDiscoverCitrixXen(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	context = GetContext(contexts, MotadataStringList{"citrix7.1", "cluster.valid.discovery"})

	context[httpclient.URLProtocol] = httpclient.URLProtocolHTTPs

	citrixXenClient := &CitrixXenClient{}

	defer SetupCleanupRoutine(citrixXenClient, context, consts.EventDiscovery, nil)

	result := DiscoverCitrixXen(citrixXenClient, context, &loggerObj)

	defer DestroyCitrixSession(citrixXenClient, citrixXenClient.session, citrixXenClient.client, citrixXenClient.target, citrixXenClient.port.ToINT())

	assertions.NotNil(result)

	assertCitrixXenClientDiscoverCitrixXenTestResult(result, assertions)
}

func assertCitrixXenClientDiscoverCitrixXenTestResult(result MotadataMap, assertions *assert.Assertions) {

	var objects []MotadataMap

	var props []string

	assertions.True(len(result) > 0)

	assertions.Equal(len(result.GetStringMapSliceValue(consts.Errors)), 0)

	assertions.Greater(len(result), 0)

	assertions.Equal(result.GetStringValue(consts.Status), consts.StatusSucceed)

	props = append(props, consts.ObjectName, consts.ObjectIP, consts.ObjectHost)

	objects = result.GetMapValue(consts.Objects).GetMapSliceValue(consts.Objects)

	assertions.GreaterOrEqual(len(objects), 0)

	for _, prop := range props {

		for _, object := range objects {

			assertions.GreaterOrEqual(len(object.GetStringValue(prop)), 0)
		}
	}

}

func TestCitrixXenClientGetCitrixAPIResponse(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	context = GetContext(contexts, MotadataStringList{"citrix7.1", "valid"})

	context = BeforeTest(context)

	citrixXenClient := &CitrixXenClient{}

	citrixXenClient.SetContext(context, &loggerObj)

	context[consts.ObjectTarget] = MotadataString(fmt.Sprintf("%s://%s:%d/host_rrd", context.GetStringValue(httpclient.URLProtocol), context.GetStringValue(consts.ObjectIP), context.GetIntValue(consts.Port)))

	response := GetCitrixAPIResponse(citrixXenClient, context)

	defer DestroyCitrixSession(citrixXenClient, citrixXenClient.session, citrixXenClient.client, citrixXenClient.target, citrixXenClient.port.ToINT())

	assertions.NotNil(response)

	assertions.Greater(len(response), 0)
}

func TestCitrixXenClientGetCitrixVMNode(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	context = GetContext(contexts, MotadataStringList{"citrix7.1", "valid"})

	context = BeforeTest(context)

	citrixXenClient := &CitrixXenClient{}

	citrixXenClient.SetContext(context, &loggerObj)

	context[consts.ObjectTarget] = MotadataString(fmt.Sprintf("%s://%s:%d/host_rrd", context.GetStringValue(httpclient.URLProtocol), context.GetStringValue(consts.ObjectIP), context.GetIntValue(consts.Port)))

	session, client := GetCitrixConnection(citrixXenClient, context, MotadataString(context.GetStringValue(consts.ObjectIP)))

	defer DestroyCitrixSession(citrixXenClient, citrixXenClient.session, citrixXenClient.client, citrixXenClient.target, citrixXenClient.port.ToINT())

	objects, _ := client.VM.GetAll(session)

	response := GetCitrixVMNode(citrixXenClient, session, objects[0], false, "citrix.xen.vm")

	assertions.NotNil(response)

	assertions.Equal(len(response), 3)

	assertions.Greater(len(response.GetStringValue("citrix.xen.vm")), 0)

	assertions.Greater(len(response.GetStringValue("citrix.xen.vm.power.state")), 0)
}

func TestCitrixXenClientGetCitrixVMNodeError(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	context = GetContext(contexts, MotadataStringList{"citrix7.1", "valid"})

	context = BeforeTest(context)

	citrixXenClient := &CitrixXenClient{}

	citrixXenClient.SetContext(context, &loggerObj)

	context[consts.ObjectTarget] = MotadataString(fmt.Sprintf("%s://%s:%d/host_rrd", context.GetStringValue(httpclient.URLProtocol), context.GetStringValue(consts.ObjectIP), context.GetIntValue(consts.Port)))

	session, _ := GetCitrixConnection(citrixXenClient, context, MotadataString(context.GetStringValue(consts.ObjectIP)))

	defer DestroyCitrixSession(citrixXenClient, citrixXenClient.session, citrixXenClient.client, citrixXenClient.target, citrixXenClient.port.ToINT())

	response := GetCitrixVMNode(citrixXenClient, session, "vmQWE!@#", false, "citrix.xen.vm")

	assertions.NotNil(response)

	assertions.Greater(len(response), 0)

	assertions.Equal(len(response.GetStringValue("citrix.xen.vm")), 0)

	assertions.Equal(len(response.GetStringValue("citrix.xen.vm.power.state")), 0)

	assertions.Equal(len(response.GetStringValue("citrix.xen.vm.ip")), 0)
}
