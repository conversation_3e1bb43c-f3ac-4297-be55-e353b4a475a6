/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package snmpclient

import (
	"encoding/json"
	g "github.com/gosnmp/gosnmp"
	"github.com/stretchr/testify/assert"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"os"
	"path/filepath"
	"testing"
)

var (
	fileBytes []byte

	loggerObj = NewLogger("Test SNMPClient", consts.BlankString)
)

func TestMain(m *testing.M) {

	fileBytes, _ = os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(consts.CurrentDir)))) + consts.PathSeparator + consts.ConfigDirectory + consts.PathSeparator + "context.json")

	m.Run()
}

func TestSNMPClientInvalidHost(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	context := GetContext(contexts, MotadataStringList{"snmp-client", "v2c"})

	context[consts.ObjectIP] = consts.InvalidHost

	context[consts.Timeout] = 10

	snmpClient.SetContext(context, &loggerObj)

	assert.False(t, snmpClient.Init())

	assert.True(t, containErrorCode(snmpClient.GetErrors()))

	snmpClient.Destroy()
}

func TestSNMPClientInvalidPort(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	context := GetContext(contexts, MotadataStringList{"snmp-client", "v2c"})

	context[consts.Timeout] = 10

	context[consts.Port] = consts.Port

	snmpClient.SetContext(context, &loggerObj)

	assert.False(t, snmpClient.Init())

	assert.True(t, containErrorCode(snmpClient.GetErrors()))

	snmpClient.Destroy()

}

func TestSNMPClientInvalidUsernameSNMPv3MD5AuthAES192Priv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{"snmp-client", "v3.md5.auth.aes192c.priv"})

	context[SNMPSecurityUserName] = consts.InvalidUserName

	snmpClient.SetContext(context, &loggerObj)

	assertions.False(snmpClient.Init())

	for _, err := range snmpClient.GetErrors() {

		assertions.Equal(err[consts.ErrorCode], consts.ErrorCodeInvalidUserName)

	}
	snmpClient.Destroy()

}

func TestSNMPClientInvalidPasswordSNMPv3MD5AuthAES192Priv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{"snmp-client", "v3.md5.auth.aes192c.priv"})

	context[SNMPAuthPassword] = consts.InvalidPassword

	snmpClient.SetContext(context, &loggerObj)

	assertions.False(snmpClient.Init())

	for _, err := range snmpClient.GetErrors() {

		assertions.Equal(err[consts.ErrorCode], consts.ErrorCodeInvalidAuthPassword)

	}
	snmpClient.Destroy()

}

func TestSNMPClientInvalidUsernameSNMPv3MD5AuthAES256Priv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{"snmp-client", "v3.md5.auth.aes256c.priv"})

	context[SNMPSecurityUserName] = consts.InvalidUserName

	snmpClient.SetContext(context, &loggerObj)

	assertions.False(snmpClient.Init())

	for _, err := range snmpClient.GetErrors() {

		assertions.Equal(err[consts.ErrorCode], consts.ErrorCodeInvalidUserName)

	}

	snmpClient.Destroy()

}

func TestSNMPClientInvalidAuthPasswordSNMPv3MD5AuthAES256Priv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{"snmp-client", "v3.md5.auth.aes256c.priv"})

	context[SNMPAuthPassword] = consts.InvalidPassword

	snmpClient.SetContext(context, &loggerObj)

	assertions.False(snmpClient.Init())

	for _, err := range snmpClient.GetErrors() {

		assertions.Equal(err[consts.ErrorCode], consts.ErrorCodeInvalidAuthPassword)

	}

	snmpClient.Destroy()

}

func TestSNMPClientInvalidConnectionSNMPv3MD5AuthAES192Priv(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"snmp-client", "v3.md5.auth.aes192c.priv"})

	context[consts.SNMPCheckTimeoutSeconds] = float64(0)

	snmpClient.SetContext(context, &loggerObj)

	assertions.False(snmpClient.Init())

	for _, err := range snmpClient.GetErrors() {

		assertions.Equal(err[consts.ErrorCode], consts.ErrorCodeTimeout)

	}

	snmpClient.Destroy()

}

func TestSNMPClientInvalidOIDValueSNMPv3MD5AuthAES192Priv(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}
	assertions := assert.New(t)

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	var invalidMetrics MotadataStringList

	context := GetContext(contexts, MotadataStringList{"snmp-client", "v3.md5.auth.aes192c.priv"})

	snmpClient.SetContext(context, &loggerObj)

	snmpClient.Init()

	snmpClient.GetScalarMetrics(GetContext(contexts, MotadataStringList{"snmp-client", "faulty.oid.scalar.metrics"}).ToStringMap(), invalidMetrics, nil, nil, false)

	for _, err := range snmpClient.GetErrors() {

		if err[consts.ErrorCode] == consts.ErrorCodeInvalidOIDValue {

			assertions.Equal(err[consts.ErrorCode], consts.ErrorCodeInvalidOIDValue)
		} else {
			assertions.Equal(err[consts.ErrorCode], consts.ErrorCodeInternalError)
		}
	}

	snmpClient.Destroy()

}

func TestSNMPClientGetDynamicTables(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"snmp-client", "v2c"})

	snmpClient.SetContext(context, &loggerObj)

	snmpClient.Init()

	tables := snmpClient.GetDynamicTables(MotadataMap{"ifHighSpeed": MotadataStringList{"*******.********.1.1.15"}})

	snmpClient.Destroy()

	assert.Equal(t, 1000, tables.GetMapSliceValue("ifHighSpeed")[0].GetIntValue("ifHighSpeed.value"))

}

func TestSNMPClientGetTables(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"snmp-client", "v2c"})

	snmpClient.SetContext(context, &loggerObj)

	snmpClient.Init()

	tables := snmpClient.GetTables(MotadataMap{"IETF Management": MotadataMap{"*******.2.1.2": "interface"}}, true, true, true, context)

	snmpClient.Destroy()

	assert.Greater(t, len(tables.GetMapSliceValue("IETF Management")), 0)
}

// BUG : https://ad-motadata:8443/Motadata/Motadata/_workitems/edit/24663/

func TestSNMPClientDecodeHexValue(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"snmp-client", "v2c"})

	context[consts.Timeout] = 10

	snmpClient.SetContext(context, &loggerObj)

	snmpClient.Init()

	rows := snmpClient.getTable(MotadataMap{".*******.*******.********.1.4": "cdp.neighbor", ".*******.*******.********.1.7": "cdp.neighbor.interface"}, true, true, true, context)

	snmpClient.Destroy()

	assert.GreaterOrEqual(t, len(rows), 1)

	for _, row := range rows {

		assert.NotEmpty(t, row.GetStringValue("cdp.neighbor"))

		assert.NotEmpty(t, row.GetStringValue("cdp.neighbor.index"))

		assert.NotEmpty(t, row.GetStringValue("cdp.neighbor.interface"))
	}

}

func TestSNMPClientGetTablesConverters(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"snmp-client", "v2c"})

	context["oid.group.converters"] = MotadataMap{"*******.2.1.2": MotadataString("")}

	snmpClient.SetContext(context, &loggerObj)

	snmpClient.Init()

	tables := snmpClient.GetTables(MotadataMap{"IETF Management": MotadataMap{"*******.2.1.2": "interface"}}, true, true, true, context)

	snmpClient.Destroy()

	assert.NotEqual(t, tables.GetMapSliceValue("IETF Management")[0].GetStringValue("interface"), consts.StatusUnknown)

	assert.Greater(t, len(tables.GetMapSliceValue("IETF Management")), 0)
}

func TestSNMPClientSNMPv1(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v1"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv2c(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v2c"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv3NoAuthNoPriv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.no.auth.no.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv3MD5AuthNoPriv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.md5.auth.no.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv3SHAAuthNoPriv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.sha.auth.no.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv3MD5AuthDESPriv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.md5.auth.des.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv3SHAAuthDESPriv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.sha.auth.des.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv3MD5AuthAESPriv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.md5.auth.aes.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv3SHAAuthAESPriv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.sha.auth.aes.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv3MD5AuthAES192Priv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.md5.auth.aes192c.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv3SHAAuthAES192Priv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.sha.auth.aes192c.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv3MD5AuthAES256Priv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.md5.auth.aes256c.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv3SHAAuthAES256Priv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.sha.auth.aes256c.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientSNMPv3SHA224AuthAES128Priv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.sha224.auth.aes.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()

}

func TestSNMPClientSNMPv3SHA256AuthAES128Priv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.sha256.auth.aes.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()

}

func TestSNMPClientSNMPv3SHA384AuthAES128Priv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.sha384.auth.aes.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()

}

func TestSNMPClientSNMPv3SHA512AuthAES128Priv(t *testing.T) {

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient.SetContext(GetContext(contexts, MotadataStringList{"snmp-client", "v3.sha512.auth.aes.priv"}), &loggerObj)

	result := snmpClient.Init()

	assert.Equal(t, true, result)

	snmpClient.Destroy()
}

func TestSNMPClientGetInterfaceMetrics(t *testing.T) {

	var contexts MotadataMap

	snmpClient := &SNMPClient{}

	assertions := assert.New(t)

	_ = json.Unmarshal(fileBytes, &contexts)

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	context := GetContext(contexts, MotadataStringList{consts.SNMPDevice, "valid.user.interface.data"})

	context = BeforeTest(context)

	snmpClient.SetContext(context, &loggerObj)

	snmpClient.Init()

	var errors []MotadataStringMap

	result, errors := GetInterfaceMetrics(context.GetSliceValue(consts.Objects), true, snmpClient, errors, consts.Yes)

	assertions.NotNil(result)

	assertions.Nil(errors)

	assertions.Greater(len(result), 0)

	snmpClient.Destroy()

}

func TestSNMPClientDiscoverAccessPoint(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"cisco.wireless.multiple.credentials", "valid"})

	snmpClient := &SNMPClient{}

	snmpClient.SetContext(context, &loggerObj)

	snmpClient.Init()

	discoveryOIDS := MotadataMap{

		consts.WirelessAccessPoint: MotadataMap{
			".*******.4.1.14179.*******.3":  consts.WirelessAccessPoint,
			".*******.4.1.14179.*******.19": consts.WirelessAccessPointIPAddress,
			".*******.4.1.14179.*******.1":  consts.WirelessAccessPointMACAddress,
			".*******.4.1.14179.*******.6":  consts.WirelessAccessPointOperationalStatus,
		},
	}

	assertions := assert.New(t)

	result := DiscoverAccessPoints(snmpClient, discoveryOIDS)

	assertions.NotNil(result)

	assertions.Greater(len(result), 0)

	assertions.Contains(result[0], consts.Status)

	assertions.Contains(result[0], consts.WirelessAccessPointOperationalStatus)

	assertions.Contains(result[0], consts.WirelessAccessPointMACAddress)

	assertions.Contains(result[0], consts.WirelessAccessPoint)

	assertions.Contains(result[0], consts.ObjectType)

	assertions.Contains(result[0], consts.ObjectName)

	snmpClient.Destroy()

}

func TestSNMPClientGetCompositeMetrics(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}
	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{consts.SNMPDevice, "valid.custom.scalar.with.composite"})

	context = BeforeTest(context)

	snmpClient := &SNMPClient{}

	snmpClient.SetContext(context, &loggerObj)

	compositeOIDs := MotadataMap{

		"(.*******.*******.********.5*100)/(.*******.*******.********.5+.*******.*******.********.6)": "Calculated EIRP",
	}

	probes := MotadataMap{

		consts.SystemDescription: ".*******.*******.0",
		consts.SystemOID:         ".*******.*******.0",
		consts.SystemName:        ".*******.*******.0",
		consts.SystemLocation:    ".*******.*******.0",
		"system.uptime.sec":      ".*******.********.1.3.0",
		"system.uptime":          ".*******.*******.0",
	}

	snmpClient.Init()

	err := GetCompositeMetrics(compositeOIDs.ToStringMap(), context.GetMapValue(consts.Result), snmpClient, context, probes, true)

	assertions := assert.New(t)

	assertions.True(len(err) == 0)

	assertions.True(len(context.GetStringMapSliceValue(consts.Errors)) == 0)

	assertions.NotNil(context.GetMapValue(consts.Result))

	assertions.GreaterOrEqual(len(context.GetMapValue(consts.Result)), 1)

	snmpClient.Destroy()

}

func TestSNMPClientGetScalarMetrics(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{consts.SNMPDevice, "test.composite.oid.computation"})

	context = BeforeTest(context)

	snmpClient := &SNMPClient{}

	snmpClient.SetContext(context, &loggerObj)

	oids := MotadataMap{

		".*******.4.1.12356.*********.0": "system.os.version",
		".*******.4.1.12356.*********.0": "system.disk.capacity.fileBytes",
	}

	snmpClient.Init()

	result, invalidMetrics := snmpClient.GetScalarMetrics(oids.ToStringMap(), nil, context, MotadataMap{}, true)

	assertions := assert.New(t)

	assertions.True(len(invalidMetrics) == 0)

	assertions.NotNil(result)

	assertions.Greater(len(result), 0)

	assertions.Equal(len(context.GetStringMapSliceValue(consts.Errors)), 0)

	snmpClient.Destroy()

}

func TestSNMPClientDiscover(t *testing.T) {

	assertions := assert.New(t)

	snmpClient := &SNMPClient{}

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}
	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{consts.SNMPDevice, "snmp.with.multiple.credential.discovery"})

	var errors []MotadataStringMap

	result := DoDiscoveryProbes(context, snmpClient, &loggerObj)

	assertions.NotNil(result)

	assertions.Equal(len(result), 2)

	assertions.Contains(result, consts.ObjectCredentialProfile)

	assertions.Contains(result, consts.CredentialProfileName)

	context[consts.ObjectCredentialProfile] = result[consts.ObjectCredentialProfile]

	context[consts.CredentialProfileName] = result[consts.CredentialProfileName]

	errors = Discover(context, snmpClient, errors)

	assertions.Nil(errors)

	assertions.Equal(len(errors), 0)

	snmpClient.Destroy()
}

func TestSNMPClientGetCompositeTables(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{consts.SNMPDevice, "valid.custom.tabular.with.composite"})

	context = BeforeTest(context)

	assertions := assert.New(t)

	snmpClient.SetContext(context, &loggerObj)

	snmpClient.Init()

	tabularOIDs := context.GetMapValue(OIDGroupOIDs).Copy()

	parentOID := context.GetStringValue(OIDGroupParentOID)

	compositeOIDS := MotadataMap{

		"(.*******.*******.********.6 + .*******.*******.********.5)":                                       "memory.pool.total",
		"(.*******.*******.********.5 * 100) / (.*******.*******.********.6 + .*******.*******.********.5)": "memory.pool.utilization",
	}

	result := snmpClient.GetCompositeTables(MotadataMap{parentOID: MotadataMap{consts.OIDs: tabularOIDs, consts.CompositeOIDs: compositeOIDS}}, true, false, true, context)

	assertions.NotNil(result)

	assertions.Contains(result, "memory.pool")

	assertions.NotNil(result["memory.pool"])

	snmpClient.Destroy()
}

func TestSNMPClientResetErrors(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient := &SNMPClient{}

	errors := []MotadataStringMap{

		{consts.ErrorCode: consts.ErrorCodeInternalError, consts.Message: "Unknown Error"},
	}

	snmpClient.errors = errors

	ResetErrors(snmpClient)

	assertions := assert.New(t)

	assertions.Equal(len(snmpClient.errors), 0)

}

func TestSNMPClientSetOIDs(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"ipsla", "valid"})

	snmpClient.SetContext(context, &loggerObj)

	assertions := assert.New(t)

	assertions.True(snmpClient.Init())

	valid, err := setOIDs(snmpClient, "10113", context, ToMap(context.GetSliceValue(consts.Objects)[0]))

	assertions.True(valid)

	assertions.Len(err, 0)

	// delete the created index

	var variableBindings []g.SnmpPDU

	variableBindings = append(variableBindings, BuildSNMPPDUIntVariable(".*******.*******.********.1.9.10113", 6))

	assertions.True(snmpClient.Set(variableBindings))
}

func TestSNMPClientSetOIDsInvalidCommunity(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"ipsla", "valid"})

	snmpClient.SetContext(context, &loggerObj)

	snmpClient.SetCommunity(context.GetStringValue(consts.InvalidSNMPCommunity))

	assertions := assert.New(t)

	snmpClient.Init()

	valid, _ := setOIDs(snmpClient, "10113", context, ToMap(context.GetSliceValue(consts.Objects)[0]))

	assertions.False(valid)

	assertions.Greater(len(snmpClient.GetErrors()), 0)

	assertions.Equal(snmpClient.GetErrors()[0][consts.ErrorCode], consts.ErrorCodeTimeout)
}

func TestSNMPClientSetOIDsNotEnoughAccess(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"ipsla", "valid"})

	context[consts.ObjectIP] = "************"

	snmpClient.SetContext(context, &loggerObj)

	assertions := assert.New(t)

	assertions.True(snmpClient.Init())

	valid, _ := setOIDs(snmpClient, "10113", context, ToMap(context.GetSliceValue(consts.Objects)[0]))

	assertions.False(valid)

	assertions.Greater(len(snmpClient.GetErrors()), 0)

	assertions.Equal(consts.ErrorCodeNoAccess, snmpClient.GetErrors()[0][consts.ErrorCode])
}

func TestSNMPClientSetOIDsInternalError(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	snmpClient := &SNMPClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(fileBytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"ipsla", "valid"})

	context[consts.ObjectIP] = "************"

	context.Delete(SNMPVersion)

	snmpClient.SetContext(context, &loggerObj)

	assertions := assert.New(t)

	assertions.True(snmpClient.Init())

	valid, _ := setOIDs(snmpClient, "10113", context, ToMap(context.GetSliceValue(consts.Objects)[0]))

	assertions.False(valid)

	assertions.Greater(len(snmpClient.GetErrors()), 0)

	assertions.Equal(consts.ErrorCodeInternalError, snmpClient.GetErrors()[0][consts.ErrorCode])
}

func containErrorCode(errors []MotadataStringMap) bool {

	if errors[0][consts.ErrorCode] == consts.ErrorCodeInternalError {

		return true

	} else if errors[0][consts.ErrorCode] == consts.ErrorCodeConnectionFailed {

		return true

	} else if errors[0][consts.ErrorCode] == consts.ErrorCodeTimeout {

		return true

	} else {

		return false
	}
}

func setOIDs(snmpClient *SNMPClient, index string, context, object MotadataMap) (result bool, errors []MotadataStringMap) {

	var variableBindings []g.SnmpPDU

	// initially set the admin status to not in service
	variableBindings = append(variableBindings, BuildSNMPPDUIntVariable(".*******.*******.********.1.9."+index, 6))

	// set the rttType and protocol for given object type
	variableBindings = append(variableBindings, BuildSNMPPDUIntVariable(".*******.*******.********.1.4."+index, 1))

	variableBindings = append(variableBindings, BuildSNMPPDUIntVariable(".*******.*******.42.1.*******."+index, 2))

	//set owner
	variableBindings = append(variableBindings, BuildSNMPPDUStringVariable(".*******.*******.42.1.*******."+index, "MOTADATA-IP-SLA-"+index+"-Test"))

	// When set to true, this entry will be shown in 'show running' command and can be saved into Non-volatile memory.
	variableBindings = append(variableBindings, BuildSNMPPDUIntVariable(".*******.*******.42.1.*******0."+index, 1))

	// set source ip address -> if interface is selected in that case interface local ip address as source
	pdu, err := BuildSNMPPDUIPVariable(".*******.*******.********.1.6."+index, object.GetStringValue("source.ip.address"))

	if err == nil {

		variableBindings = append(variableBindings, pdu)

		// set destination ip address
		pdu, err = BuildSNMPPDUIPVariable(".*******.*******.********.1.2."+index, object.GetStringValue("destination.ip.address"))

		if err == nil {

			variableBindings = append(variableBindings, pdu)

			// set frequency of initiating each RTT operation.
			variableBindings = append(variableBindings, BuildSNMPPDUIntVariable(".*******.*******.42.1.*******."+index, context.GetIntValue("operation.frequency")))

			// set the duration to wait for a RTT operation completion.
			variableBindings = append(variableBindings, BuildSNMPPDUIntVariable(".*******.*******.********.1.7."+index, context.GetIntValue("operation.timeout")))

			// set the rtt life to 2147483647 (i.e. infinite life)
			variableBindings = append(variableBindings, BuildSNMPPDUIntVariable(".*******.*******.********.1.1."+index, 2147483647))

			// set the rtt start time to 1 (i.e. immediately transition the rttMonCtrlOperState object into 'active' state)
			variableBindings = append(variableBindings, BuildSNMPPDUTimeTickVariable(".*******.*******.********.1.2."+index, 1))

			// set the status to create and go
			variableBindings = append(variableBindings, BuildSNMPPDUIntVariable(".*******.*******.********.1.9."+index, 4))

			result = snmpClient.Set(variableBindings)

		} else {

			errors = append(errors, MotadataStringMap{
				consts.ErrorCode: consts.ErrorCodeBadRequest,
				consts.Message:   err.Error(),
			})

		}

	} else {

		errors = append(errors, MotadataStringMap{
			consts.ErrorCode: consts.ErrorCodeBadRequest,
			consts.Message:   err.Error(),
		})

	}

	return
}
