/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
 * Change Logs:
 *  Date			Author			Notes
 *  22-Apr-2025     Darshan Parmar  MOTADATA-5993: SNMP octetString slice parsing improvement
 *  26-Apr-2025     Darshan Parmar  MOTADATA-6221: SNMP octetString slice parsing improvement for date values
 */

package snmpclient

import (
	"bytes"
	"encoding/hex"
	"fmt"
	g "github.com/gosnmp/gosnmp"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"net"
	"reflect"
	"strings"
	"time"
	"unicode"
)

var (
	authProtocols = map[string]g.SnmpV3AuthProtocol{
		"MD5":    g.MD5,
		"SHA":    g.SHA,
		"SHA224": g.SHA224,
		"SHA256": g.SHA256,
		"SHA384": g.SHA384,
		"SHA512": g.SHA512,
	}

	privProtocols = map[string]g.SnmpV3PrivProtocol{
		"DES":     g.DES,
		"AES":     g.AES,
		"AES192":  g.AES192,
		"AES256":  g.AES256,
		"AES192C": g.AES192C,
		"AES256C": g.AES256C,
	}

	// This is added as a solution for BUG: 24663 & 27212.
	// This list contains metrics which needs conversion from HEX_STRING to IP or MAC address
	IPMACMetrics = MotadataStringList{"spm.neighbor", "isis.neighbor", "cdp.neighbor", "lldp.neighbor", "ospf.neighbor", "bgp.neighbor", "ipsla.path.target.address"}
)

const (
	SNMPDiscoveryOID = "discover.oid"

	SNMPCommunity = "snmp.community"

	SNMPWriteCommunity = "snmp.write.community"

	SNMPVersion = "snmp.version"

	SNMPVersion2c = "v2c"

	SNMPVersion3 = "v3"

	SNMPSecurityLevel = "snmp.security.level"

	SNMPSecurityLevelAuthPriv = "Authentication Privacy"

	SNMPSecurityLevelAuthNoPriv = "Authentication No Privacy"

	SNMPSecurityLevelNoAuthNoPriv = "No Authentication No Privacy"

	SNMPSecurityUserName = "snmp.security.user.name"

	SNMPAuthProtocol = "snmp.authentication.protocol"

	SNMPAuthPassword = "snmp.authentication.password"

	SNMPPrivacyProtocol = "snmp.privacy.protocol"

	SNMPPrivatePassword = "snmp.private.password"

	OID = "oid"

	InvalidOIDs = "invalid.oids"

	ValidOIDs = "valid.oids"

	SystemOID = ".*******.*******.0"

	ObjectVendor = "object.vendor"

	VendorCisco = "Cisco Systems"

	VendorHuawei = "Huawei"

	VendorH3C = "H3C"

	VendorNetgear = "Netgear"

	VendorHP = "Hewlett Packard Enterprise"

	VendorJuniper = "Juniper Networks"

	VendorExtreme = "Extreme Networks"

	VendorDLink = "D-Link"

	VendorFortinet = "Fortinet"

	VendorSonicWall = "SonicWall"

	VendorCheckPoint = "Check Point"

	VendorMoxa = "Moxa"

	OIDGroupOIDs = "oid.group.oids"

	OIDGroupConverters = "oid.group.converters"

	OIDGroupComputations = "oid.group.computations"

	OIDGroupRegex = "oid.group.regex"

	OIDGroup = "oid.group"

	OIDGroupID = "oid.group.id"

	OIDGroupParentOID = "oid.group.parent.oid"

	OIDGroupType = "oid.group.type"

	OIDGroupTypeScalar = "scalar"

	OIDGroupTypeTabular = "tabular"

	OIDGroupInstanceCountMetricStatus = "oid.group.instance.count.metric.status"

	OIDGroupCorrelationMetric = "oid.group.correlation.metric"

	OIDGroupInstanceCountMetricName = "oid.group.instance.count.metric.name"
)

type SNMPClient struct {
	target MotadataString

	port MotadataUINT16

	timeout MotadataUINT

	retries MotadataUINT8

	params *g.GoSNMP

	discoveryOID MotadataString

	errors []MotadataStringMap

	bulkWalk bool

	connected bool

	logger *Logger
}

func (snmpClient *SNMPClient) Destroy() {

	if snmpClient.params.Conn != nil {

		_ = snmpClient.params.Conn.Close()
	}

}

func (snmpClient *SNMPClient) SetBulkWalk(value bool) {

	snmpClient.bulkWalk = value
}

func (snmpClient *SNMPClient) GetErrors() []MotadataStringMap {

	return snmpClient.errors
}

func (snmpClient *SNMPClient) ResetErrors(index int) {

	snmpClient.errors = append(snmpClient.errors[:index], snmpClient.errors[index+1:]...)
}

func (snmpClient *SNMPClient) setTarget(context MotadataMap) *SNMPClient {

	if context.Contains(consts.ObjectIP) {

		snmpClient.target = context.GetMotadataStringValue(consts.ObjectIP)
	}

	return snmpClient
}

func (snmpClient *SNMPClient) setPort(context MotadataMap) *SNMPClient {

	if context.Contains(consts.Port) {

		snmpClient.port = context.GetUINT16Value(consts.Port)

	} else {

		snmpClient.port = MotadataUINT16(161)
	}

	return snmpClient
}

func (snmpClient *SNMPClient) setRetries(context MotadataMap) *SNMPClient {

	if context.Contains(consts.SNMPCheckRetries) {

		snmpClient.retries = context.GetUINT8Value(consts.SNMPCheckRetries)

	} else {

		snmpClient.retries = MotadataUINT8(2)
	}

	return snmpClient
}

func (snmpClient *SNMPClient) setDiscoveryOID(context MotadataMap) *SNMPClient {

	if context.Contains(SNMPDiscoveryOID) {

		snmpClient.discoveryOID = context.GetMotadataStringValue(SNMPDiscoveryOID)
	}

	return snmpClient
}

func (snmpClient *SNMPClient) setTimeout(context MotadataMap) *SNMPClient {

	if context.Contains(consts.SNMPCheckTimeoutSeconds) {

		snmpClient.timeout = MotadataUINT(context.GetIntValue(consts.SNMPCheckTimeoutSeconds))

	} else {

		snmpClient.timeout = MotadataUINT(2)
	}

	return snmpClient
}

func (snmpClient *SNMPClient) setParams(context MotadataMap) *SNMPClient {

	params := &g.GoSNMP{
		Target:             (snmpClient.target).ToString(),
		Port:               (snmpClient.port).ToUInt16(),
		Timeout:            time.Duration(snmpClient.timeout) * time.Second,
		Retries:            (snmpClient.retries).ToInt(),
		ExponentialTimeout: true,
	}

	switch context[SNMPVersion] {

	case SNMPVersion2c:

		params.Version = g.Version2c

		params.Community = context.GetStringValue(SNMPCommunity)

		break

	case SNMPVersion3:

		if context.Contains(consts.Context) {

			params.ContextName = context.GetStringValue(consts.Context)
		}

		params.Version = g.Version3

		params.SecurityModel = g.UserSecurityModel

		switch context[SNMPSecurityLevel] {

		case SNMPSecurityLevelAuthNoPriv:

			params.MsgFlags = g.AuthNoPriv

			params.SecurityParameters = &g.UsmSecurityParameters{
				UserName: context.GetStringValue(SNMPSecurityUserName),

				AuthenticationProtocol: authProtocols[context.GetStringValue(SNMPAuthProtocol)],

				AuthenticationPassphrase: context.GetStringValue(SNMPAuthPassword),
			}
			break

		case SNMPSecurityLevelAuthPriv:

			params.MsgFlags = g.AuthPriv

			params.SecurityParameters = &g.UsmSecurityParameters{

				UserName: context.GetStringValue(SNMPSecurityUserName),

				AuthenticationProtocol: authProtocols[context.GetStringValue(SNMPAuthProtocol)],

				AuthenticationPassphrase: context.GetStringValue(SNMPAuthPassword),

				PrivacyProtocol: privProtocols[context.GetStringValue(SNMPPrivacyProtocol)],

				PrivacyPassphrase: context.GetStringValue(SNMPPrivatePassword),
			}
			break

		default:

			params.MsgFlags = g.NoAuthNoPriv

			params.SecurityParameters = &g.UsmSecurityParameters{UserName: context.GetStringValue(SNMPSecurityUserName)}
		}
		break

	default:

		params.Version = g.Version1

		params.Community = context.GetStringValue(SNMPCommunity)
	}

	snmpClient.params = params

	return snmpClient
}

func (snmpClient *SNMPClient) setLogger(logger *Logger) *SNMPClient {

	snmpClient.logger = logger

	return snmpClient
}

func (snmpClient *SNMPClient) SetContext(context MotadataMap, logger *Logger) *SNMPClient {

	return snmpClient.setTarget(context).setPort(context).setTimeout(context).setRetries(context).setDiscoveryOID(context).setParams(context).setLogger(logger)
}

// allow to set community explicitly in case of write community is there!
func (snmpClient *SNMPClient) SetCommunity(community string) *SNMPClient {

	snmpClient.params.Community = community

	return snmpClient
}

func (snmpClient *SNMPClient) Init() bool {

	err := snmpClient.params.Connect()

	snmpClient.logger.Debug(MotadataString(fmt.Sprintf("Connecting to  %s on port %d", snmpClient.target, snmpClient.port)))

	if err != nil {

		snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
			consts.Error:     err.Error(),
			consts.ErrorCode: consts.ErrorCodeConnectionFailed,
			consts.Message:   fmt.Sprintf(consts.ErrorMessageConnectionFailed, "SNMP", snmpClient.target, snmpClient.port),
		})

	} else {

		oid := make(MotadataStringMap)

		if !snmpClient.discoveryOID.IsNotEmpty() {

			oid[SystemOID] = SNMPDiscoveryOID

		} else {

			oid[snmpClient.discoveryOID.ToString()] = SNMPDiscoveryOID
		}

		metrics, _ := snmpClient.GetScalarMetrics(oid, nil, nil, nil, false)

		if metrics.IsNotEmpty() {

			snmpClient.connected = true

			snmpClient.logger.Debug(MotadataString(fmt.Sprintf("SNMP connection established to %s:%d", snmpClient.target, snmpClient.port)))

		} else {

			snmpClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageConnectionFailed, "SNMP", snmpClient.target, snmpClient.port)))
		}

	}

	return snmpClient.connected
}

func (snmpClient *SNMPClient) getTable(oids MotadataMap, appendIndex, appendOID, manipulate bool, context MotadataMap) (table []MotadataMap) {

	rows := make(map[int]MotadataMap)

	var err error

	for oid := range oids {

		variableBindings := snmpClient.Walk(oid, err)

		if variableBindings != nil && len(variableBindings) > 0 {

			for index := range variableBindings {

				if _, found := rows[index]; !found {

					rows[index] = make(MotadataMap)
				}

				if variableBindings[index].Value != nil {

					oidIndex := oids.GetStringValue(oid)

					switch variableBindings[index].Type {

					case g.OctetString:

						snmpClient.logger.Trace(MotadataString(fmt.Sprintf("OctetString type with value %s in string and %v in slice", ToString(variableBindings[index].Value.([]uint8)), variableBindings[index].Value)))

						if reflect.ValueOf(variableBindings[index].Value).Kind().String() == "string" {

							rows[index][oidIndex] = variableBindings[index].Value

						} else if reflect.ValueOf(variableBindings[index].Value).Kind().String() == "slice" {

							if manipulate {

								rows[index][oidIndex] = ToString(variableBindings[index].Value.([]uint8))

								// ipv6 case
								if net.IP(variableBindings[index].Value.([]uint8)).To4() == nil && net.IP(variableBindings[index].Value.([]uint8)).To16() != nil && net.ParseIP(ToString(variableBindings[index].Value)) != nil {

									rows[index][oidIndex] = net.IP(variableBindings[index].Value.([]uint8)).String()

								} else if IsASCII(ToString(variableBindings[index].Value.([]uint8))) {

									if IPMACMetrics.Contains(MotadataString(oidIndex)) {

										//ipv6 case
										if net.IP(variableBindings[index].Value.([]uint8)).To4() == nil && net.IP(variableBindings[index].Value.([]uint8)).To16() != nil && net.ParseIP(ToString(variableBindings[index].Value)) != nil && consts.IPv6Pattern.MatchString(net.IP(variableBindings[index].Value.([]uint8)).To16().String()) {

											snmpClient.logger.Trace(MotadataString(fmt.Sprintf("OctetString type with value %s in string and %v in slice in IPv6 as %s", ToString(variableBindings[index].Value.([]uint8)), variableBindings[index].Value, net.IP(variableBindings[index].Value.([]uint8)).String())))

											rows[index][oidIndex] = net.IP(variableBindings[index].Value.([]uint8)).String()

										} else if net.IP(variableBindings[index].Value.([]uint8)).To4() != nil && net.ParseIP(ToString(variableBindings[index].Value)) != nil && consts.IPPattern.MatchString(net.IP(variableBindings[index].Value.([]uint8)).To4().String()) {

											snmpClient.logger.Trace(MotadataString(fmt.Sprintf("OctetString type with value %s in string and %v in slice in IPv4 as %s", ToString(variableBindings[index].Value.([]uint8)), variableBindings[index].Value, net.IP(variableBindings[index].Value.([]uint8)).To4().String())))

											//ipv4 case
											rows[index][oidIndex] = net.IP(variableBindings[index].Value.([]uint8)).To4().String()

										} else if mac, err := net.ParseMAC(hex.EncodeToString(variableBindings[index].Value.([]uint8))); err == nil {

											snmpClient.logger.Trace(MotadataString(fmt.Sprintf("OctetString type with value %s in string and %v in slice in mac as %s", ToString(variableBindings[index].Value.([]uint8)), variableBindings[index].Value, mac.String())))

											//mac address case
											rows[index][oidIndex] = mac.String()

										} else {

											snmpClient.logger.Warn(MotadataString(fmt.Sprintf("metric %s with oid %s having value %s failed to parse to IP/MAC address", oidIndex, oid, variableBindings[index].Value.([]uint8))))
										}
									}

								} else {
									// value is in hex format

									if len(ToString(variableBindings[index].Value.([]uint8))) == 4 { //TODO : Need to verify logic in other more cases

										bytes, _ := hex.DecodeString(hex.EncodeToString(variableBindings[index].Value.([]uint8)))

										rows[index][oidIndex] = fmt.Sprintf("%v.%v.%v.%v", bytes[0], bytes[1], bytes[2], bytes[3])

									} else if len(ToString(variableBindings[index].Value.([]uint8))) == 6 {

										rows[index][oidIndex] = StringSliceToMACAddress(variableBindings[index].Value)

									} else if len(variableBindings[index].Value.([]uint8)) == 8 || len(variableBindings[index].Value.([]uint8)) == 11 {
										/* Added handling for SNMP OIDs returning date/time values in hex format.
											Previously, we only handled IP (length 4) and MAC addresses (length 6).
										On some systems (Windows/Linux), specific OIDs can return datetime in 8 or 11 byte formats.
											This block checks for such cases and decodes them into a human-readable timestamp.
										*/

										value := variableBindings[index].Value.([]uint8)

										if len(value) >= 8 && bytes.Equal(value[:8], make([]byte, 8)) {

											rows[index][oidIndex] = ""

											snmpClient.logger.Warn(MotadataString(fmt.Sprintf("metric %s with oid %s having value %s has not qualified into any hex check", oidIndex, oid, variableBindings[index].Value.([]uint8))))

										} else {

											timezone := time.UTC

											if len(value) == 11 && value[8] == '-' {

												timezone = time.FixedZone("", -(int(value[9])*3600 + int(value[10])*60))

											} else if len(value) == 11 {

												timezone = time.FixedZone("", int(value[9])*3600+int(value[10])*60)

											}

											// Convert binary datetime bytes to time.Time object

											// Binary format: [year_high, year_low, month, day, hour, minute, second, decisecond, timezone_info...]

											parsedTime := time.Date(

												int(value[0])<<8|int(value[1]),

												time.Month(value[2]),

												int(value[3]),

												int(value[4]),

												int(value[5]),

												0,

												0,

												timezone,
											)

											rows[index][oidIndex] = parsedTime.Format("Mon, Jan 02 2006, 15:04")

										}

									} else {

										snmpClient.logger.Warn(MotadataString(fmt.Sprintf("metric %s with oid %s having value %s has not qualified into any hex check", oidIndex, oid, variableBindings[index].Value.([]uint8))))
									}
								}

							} else {

								rows[index][oidIndex] = variableBindings[index].Value.([]uint8)

							}

						} else {

							rows[index][oidIndex] = ToString(variableBindings[index].Value.([]byte))

						}

					default:

						rows[index][oidIndex] = variableBindings[index].Value
					}

					rows[index][oidIndex] = DoTransformation(snmpClient, context, oid, rows[index][oidIndex], false)

					if appendIndex {

						indexes := strings.Split(variableBindings[index].Name, consts.DotSeparator)

						rows[index][oidIndex+".index"] = indexes[len(indexes)-1]
					}

					if appendOID {

						rows[index][oidIndex+".oid"] = variableBindings[index].Name
					}

					// if snmp oid is related to ip address & if ip address is loopback/multicast/unspecified than we will skip it
					if oidIndex == "address" && !validInterface(ToMotadataString(rows[index][oidIndex])) {

						delete(rows, index)
					}

				} else {

					snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
						consts.Error:     "Invalid SNMP OID Values",
						consts.ErrorCode: consts.ErrorCodeInvalidOIDValue,
						OID:              variableBindings[index].Name,
						consts.Message:   "Invalid SNMP OID Values",
					})

					snmpClient.logger.Trace(MotadataString(fmt.Sprintf("Error %s occured while processing tabular result %s for %s", ToJSON(snmpClient.errors), oids, snmpClient.target)))
				}
			}
		}
	}

	for row := range rows {

		table = append(table, rows[row])

	}

	if IsNotEmptyMapSlice(table) {

		snmpClient.logger.Debug(MotadataString(fmt.Sprintf("tabular result %s for %s", table, snmpClient.target)))

	} else {
		snmpClient.logger.Trace(MotadataString(fmt.Sprintf("for oids %s tabular result not found on %s", oids, snmpClient.target)))
	}

	return
}

// This method is used when we want mapping bases on the oid suffix (MOTADATA-3664)
func (snmpClient *SNMPClient) getTableByOIDIndex(oids MotadataMap, appendIndex, appendOID, manipulate bool) (table []MotadataMap) {

	rows := make(map[string]MotadataMap)

	var variableBindings []g.SnmpPDU

	var err error

	for oid := range oids {

		if snmpClient.bulkWalk == true {

			variableBindings, err = snmpClient.params.BulkWalkAll(oid)

		} else {

			variableBindings, err = snmpClient.params.WalkAll(oid)
		}

		//version v1

		if err == nil && variableBindings == nil {

			snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
				consts.Error:     "Unknown Error",
				consts.ErrorCode: consts.ErrorCodeInternalError,
				consts.Message:   "Unknown Error",
				OID:              oid,
			})

			snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Unknown error occured while getting tabular oid %s for %s", oid, snmpClient.target)))
		} else /*for v2c/v3 */ if err != nil {

			snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
				consts.Error:     err.Error(),
				consts.ErrorCode: consts.ErrorCodeInternalError,
				consts.Message:   err.Error(),
				OID:              oid,
			})

			snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Error %s occured while getting tabular oid %s for %s", err.Error(), oid, snmpClient.target)))

		}

		if variableBindings != nil && len(variableBindings) > 0 {

			for index := range variableBindings {

				tokens := strings.Split(variableBindings[index].Name, oid)

				if len(tokens) > 1 && MotadataString(tokens[1]).IsNotEmpty() {

					var token = tokens[1]

					if _, found := rows[token]; !found {

						rows[token] = make(MotadataMap)
					}

					if variableBindings[index].Value != nil {

						oidIndex := oids.GetStringValue(oid)

						switch variableBindings[index].Type {

						case g.OctetString:

							if reflect.ValueOf(variableBindings[index].Value).Kind().String() == "string" {

								rows[token][oidIndex] = variableBindings[index].Value

							} else if reflect.ValueOf(variableBindings[index].Value).Kind().String() == "slice" {

								if manipulate {

									rows[token][oidIndex] = ToString(variableBindings[index].Value.([]uint8))

									// ipv6 case
									if net.IP(variableBindings[index].Value.([]uint8)).To4() == nil && net.IP(variableBindings[index].Value.([]uint8)).To16() != nil && net.ParseIP(ToString(variableBindings[index].Value)) != nil {

										rows[token][oidIndex] = net.IP(variableBindings[index].Value.([]uint8)).String()

									} else if IsASCII(ToString(variableBindings[index].Value.([]uint8))) {

										if IPMACMetrics.Contains(MotadataString(oidIndex)) {

											//ipv6 case
											if net.IP(variableBindings[index].Value.([]uint8)).To4() == nil && net.IP(variableBindings[index].Value.([]uint8)).To16() != nil && consts.IPv6Pattern.MatchString(net.IP(variableBindings[index].Value.([]uint8)).To16().String()) {

												rows[token][oidIndex] = net.IP(variableBindings[index].Value.([]uint8)).String()

											} else if net.IP(variableBindings[index].Value.([]uint8)).To4() != nil && consts.IPPattern.MatchString(net.IP(variableBindings[index].Value.([]uint8)).To4().String()) {

												//ipv4 case
												rows[token][oidIndex] = net.IP(variableBindings[index].Value.([]uint8)).To4().String()

											} else if mac, err := net.ParseMAC(hex.EncodeToString(variableBindings[index].Value.([]uint8))); err == nil {

												//mac address case
												rows[token][oidIndex] = mac.String()

											} else {

												snmpClient.logger.Warn(MotadataString(fmt.Sprintf("metric %s with oid %s having value %s failed to parse to IP/MAC address", oidIndex, oid, variableBindings[index].Value.([]uint8))))
											}
										}

									} else {
										// value is in hex format

										if len(ToString(variableBindings[index].Value.([]uint8))) == 4 { //TODO : Need to verify logic in other more cases

											bytes, _ := hex.DecodeString(hex.EncodeToString(variableBindings[index].Value.([]uint8)))

											rows[token][oidIndex] = fmt.Sprintf("%v.%v.%v.%v", bytes[0], bytes[1], bytes[2], bytes[3])

										} else if len(ToString(variableBindings[index].Value.([]uint8))) == 6 {

											rows[token][oidIndex] = StringSliceToMACAddress(variableBindings[index].Value)

										} else {

											snmpClient.logger.Warn(MotadataString(fmt.Sprintf("metric %s with oid %s having value %s has not qualified into any hex check", oidIndex, oid, variableBindings[index].Value.([]uint8))))
										}
									}

								} else {

									rows[token][oidIndex] = variableBindings[index].Value.([]uint8)

								}

							} else {

								rows[token][oidIndex] = ToString(variableBindings[index].Value.([]byte))

							}
						default:

							rows[token][oidIndex] = variableBindings[index].Value
						}

						if appendIndex {

							indexes := strings.Split(variableBindings[index].Name, ".")

							rows[token][oidIndex+".index"] = indexes[len(indexes)-1]
						}

						if appendOID {

							rows[token][oidIndex+".oid"] = variableBindings[index].Name
						}
					} else {

						snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
							consts.Error:     "Invalid SNMP OID Values",
							consts.ErrorCode: consts.ErrorCodeInvalidOIDValue,
							OID:              variableBindings[index].Name,
							consts.Message:   "Invalid SNMP OID Values",
						})

						snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Error %s occured while processing tabuler result %s for %s", ToJSON(snmpClient.errors), oids, snmpClient.target)))
					}

				}
			}
		}
	}

	for row := range rows {

		table = append(table, rows[row])

	}

	if IsNotEmptyMapSlice(table) {

		snmpClient.logger.Debug(MotadataString(fmt.Sprintf("tabular result %s for %s", table, snmpClient.target)))

	} else {
		snmpClient.logger.Warn(MotadataString(fmt.Sprintf("for oids %s tabular result not found on %s", oids, snmpClient.target)))
	}

	return
}

func (snmpClient *SNMPClient) buildResult(result *g.SnmpPacket, metrics MotadataMap, oids MotadataStringMap, context MotadataMap, probes MotadataMap, discoveryRequest bool) MotadataStringList {

	var invalidMetrics MotadataStringList

	if result != nil {

		for _, variable := range result.Variables {

			if variable.Value != nil {

				oid := oids[variable.Name]

				switch variable.Type {

				case g.OctetString:

					if reflect.ValueOf(variable.Value).Kind().String() == "slice" {

						if IsASCII(ToString(variable.Value.([]uint8))) {

							if IsValidCharacter(ToString(variable.Value.([]uint8))) {

								metrics[oid] = ToString(variable.Value.([]uint8))

							} else {

								metrics[oid] = strings.Map(func(char rune) rune {
									if unicode.IsPrint(char) {
										return char
									}
									return -1
								}, ToString(variable.Value.([]uint8)))

							}

						} else if len(variable.Value.([]uint8)) == 6 {

							metrics[oid] = StringSliceToMACAddress(variable.Value)

						} else {

							metrics[oid] = consts.BlankString

						}

					} else {

						metrics[oid] = ToString(variable.Value.([]byte))

					}
				default:

					metrics[oid] = variable.Value
				}

				metrics[oid] = DoTransformation(snmpClient, context, variable.Name, metrics[oid], false)

				// for only discoveryRequest request we will build map for validOIDs

				if discoveryRequest {

					probes[oid] = variable.Name
				}

			} else {

				snmpClient.logger.Trace(MotadataString(fmt.Sprintf("OID %s is not valid for %s", oids, snmpClient.target)))

				invalidMetrics = append(invalidMetrics, variable.Name)
			}
		}
	}

	snmpClient.logger.Debug(MotadataString(fmt.Sprintf("result of oids %s for target %s : %s", oids, snmpClient.target, metrics.ToJSON())))

	return invalidMetrics
}

//This method is used for Scalar metrics

func (snmpClient *SNMPClient) GetScalarMetrics(oids MotadataStringMap, faultyOIDs MotadataStringList, context MotadataMap, probes MotadataMap, discoveryRequest bool) (metrics MotadataMap, invalidMetrics MotadataStringList) {

	metrics = make(MotadataMap)

	invalidOIDs := make(MotadataStringMap)

	var validOIDs MotadataStringList

	if faultyOIDs.IsNotEmpty() {

		for _, invalidMetric := range faultyOIDs {

			invalidOIDs[invalidMetric] = invalidMetric
		}
	}

	if oids.IsNotEmpty() {

		for oid := range oids {

			if !invalidOIDs.Contains(oid) {

				validOIDs = append(validOIDs, strings.TrimSpace(oid))
			}

		}

		result, err := snmpClient.params.Get(validOIDs)

		if err == nil {

			faultyOIDs = snmpClient.buildResult(result, metrics, oids, context, probes, discoveryRequest)

			if result == nil && snmpClient.params.Version == g.Version1 { //only for v1

				for _, oid := range validOIDs {

					oidValue, _ := snmpClient.params.Get(MotadataStringList{oid})

					partialFaultyMetrics := snmpClient.buildResult(oidValue, metrics, oids, context, probes, discoveryRequest)

					if partialFaultyMetrics.IsNotEmpty() {

						faultyOIDs = append(faultyOIDs, partialFaultyMetrics...)
					}

				}

			}

			if faultyOIDs.IsNotEmpty() {

				invalidOIDs := MotadataStringList{}

				for _, oid := range faultyOIDs {

					if !strings.HasSuffix(oid, ".0") {

						tableOIDs := make(MotadataMap)

						tableOIDs[oid] = oids[oid]

						rows := snmpClient.getTable(tableOIDs, false, false, false, nil)

						if IsNotEmptyMapSlice(rows) {

							metrics[oids[oid]] = DoSummationMetric(rows, MotadataString(oids[oid]))

							if discoveryRequest {

								probes[oids[oid]] = oid
							}

						} else {

							invalidOIDs = append(invalidOIDs, oid)
						}

					} else {

						invalidOIDs = append(invalidOIDs, oid)
					}
				}

				faultyOIDs = invalidOIDs
			}

		} else {

			if MotadataString(err.Error()).Contains("unknown username") {

				snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
					consts.Error:     err.Error(),
					consts.ErrorCode: consts.ErrorCodeInvalidUserName,
					consts.Message:   consts.ErrorMessageInvalidUserName,
				})

				snmpClient.logger.Warn(MotadataString(fmt.Sprintf("User name is invalid %s", snmpClient.target)))
			} else if MotadataString(err.Error()).Contains("wrong digest") {

				snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
					consts.Error:     err.Error(),
					consts.ErrorCode: consts.ErrorCodeInvalidAuthPassword,
					consts.Message:   consts.ErrorMessageInvalidAuthPassword,
				})

				snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Auth Password is invalid %s", snmpClient.target)))
			} else if MotadataString(err.Error()).Contains("timeout") {

				snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
					consts.Error:     err.Error(),
					consts.ErrorCode: consts.ErrorCodeTimeout,
					consts.Message:   err.Error(),
				})

				snmpClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageConnectionTimeout, "SNMP", snmpClient.target, snmpClient.port)))
			} else if MotadataString(err.Error()).Contains("connection refused") {

				snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
					consts.Error:     err.Error(),
					consts.ErrorCode: consts.ErrorCodeConnectionFailed,
					consts.Message:   err.Error(),
				})

			} else {
				snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
					consts.Error:     err.Error(),
					consts.ErrorCode: consts.ErrorCodeInternalError,
					consts.Message:   err.Error(),
				})

				snmpClient.logger.Warn(MotadataString(err.Error()))
			}

		}

	}

	if faultyOIDs.IsNotEmpty() {

		invalidMetrics = faultyOIDs

		for _, oid := range faultyOIDs {

			snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
				consts.Error:     "Invalid SNMP OID Value",
				consts.ErrorCode: consts.ErrorCodeInvalidOIDValue,
				OID:              oid,
				consts.Message:   "Invalid SNMP OID Value",
			})

		}
	}

	snmpClient.logger.Debug(MotadataString(fmt.Sprintf("Scalar OID of %s result of %s is %s", oids, snmpClient.target, metrics.ToJSON())))

	return
}

// GetTables This method is used for Tabular metrics
func (snmpClient *SNMPClient) GetTables(tabularOIDs MotadataMap, appendIndex bool, appendOID bool, manipulate bool, context MotadataMap) (tables MotadataMap) {

	tables = make(MotadataMap)

	for key, oids := range tabularOIDs {

		rows := snmpClient.getTable(ToMap(oids), appendIndex, appendOID, manipulate, context)

		if IsNotEmptyMapSlice(rows) {

			tables[key] = rows
		}
	}

	snmpClient.logger.Debug(MotadataString(fmt.Sprintf("tabular OID result of metric %s for target %s : %s", tabularOIDs, snmpClient.target, tables.ToJSON())))

	return
}

// GetNext sends an SNMP GETNEXT request
func (snmpClient *SNMPClient) GetDynamicTables(oids MotadataMap) (tables MotadataMap) {

	tables = make(MotadataMap)

	for key := range oids {

		variableBindings, err := snmpClient.params.GetNext(oids.GetListValue(key))

		if err == nil && variableBindings == nil {

			snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
				consts.Error:     "Unknown Error",
				consts.ErrorCode: consts.ErrorCodeInternalError,
				consts.Message:   "Unknown Error",
				OID:              strings.Join(oids.GetListValue(key), ","),
			})

			snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Unknown error occured while GetNext for oids %s for %s", strings.Join(oids.GetListValue(key), ","), snmpClient.target)))

		} else /*for v2c/v3 */ if err != nil {

			snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
				consts.Error:     err.Error(),
				consts.ErrorCode: consts.ErrorCodeInternalError,
				consts.Message:   err.Error(),
				OID:              strings.Join(oids.GetListValue(key), ","),
			})

			snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Error %s occured while Get Next for oids %s for %s", err.Error(), strings.Join(oids.GetListValue(key), ","), snmpClient.target)))
		}

		if variableBindings != nil && len(variableBindings.Variables) > 0 {

			var table []MotadataMap

			for _, variable := range variableBindings.Variables {
				table = append(table, MotadataMap{
					key:            variable.Name,
					key + ".value": variable.Value,
				})
			}

			tables[key] = table
		}
	}
	return
}

func (snmpClient *SNMPClient) GetCompositeTables(tabularOIDs MotadataMap, appendIndex, appendOID, manipulate bool, context MotadataMap) (tables MotadataMap) {

	tables = make(MotadataMap)

	for key, value := range tabularOIDs {

		rows := map[int]MotadataMap{}

		//0 = .*******.********.3.1.2 -> system.disk.volume.type
		//1 = .*******.********.3.1.3 -> system.disk.volume
		//2 = .*******.********.3.1.5 -> system.disk.volume.capacity.bytes
		//3 = .*******.********.3.1.6 -> system.disk.volume.used.bytes
		oids := ToMap(value).GetMapValue(consts.OIDs)

		//1 = .*******.********.3.1.5 - .*******.********.3.1.6 -> system.disk.volume.free.bytes
		//0 = .*******.********.3.1.5 - .*******.********.3.1.5 -> system.disk.volume.free.bytes.computation//T
		compositeOIDs := ToMap(value).GetMapValue(consts.CompositeOIDs)

		/* normal oids data is transferred into table map which is map[int]MotadataMap
		   this is for removing extra loop for comparing it with composite oid result to append composite oids polled data into result  */
		for _, row := range snmpClient.getTable(oids, appendIndex, appendOID, manipulate, context) {

			for oid, oidValue := range row {

				if MotadataString(oid).HasSuffix(".index") {

					rows[MotadataString(ToString(oidValue)).ToInt()] = row

					break

				}
			}
		}

		for compositeOID := range compositeOIDs {

			// here in compositeOIDTables key is index and value is map
			compositeRows := processCompositeTabularOID(MotadataMap{compositeOID: compositeOIDs[compositeOID]}, snmpClient, context.GetStringMapSliceValue(consts.Errors), context)

			if len(rows) == 0 {

				for index, row := range compositeRows {

					ToMap(row)[".index"] = index

					rows[MotadataString(index).ToInt()] = ToMap(row)

				}

			} else {

				for index, row := range rows {

					if compositeRows[ToString(index)] != nil {

						for key, value := range ToMap(compositeRows[ToString(index)]) {

							ToMap(row)[key] = value

						}
					}
				}
			}

		}

		for index := range rows {

			for key := range rows[index] {

				if MotadataString(key).HasSuffix(".index") {

					rows[index].Delete(key)

				}

			}

		}

		if len(rows) > 0 {

			tables[key] = []MotadataMap{}

			for _, row := range rows {

				tables[key] = append(tables.GetMapSliceValue(key), row)

			}
		}
	}

	snmpClient.logger.Debug(MotadataString(fmt.Sprintf("tabular OID result of metric %s for target %s : %s", tabularOIDs, snmpClient.target, tables)))

	return
}

func (snmpClient *SNMPClient) GetUpInterfaces() MotadataStringList {

	var err error

	indices := MotadataStringList{}

	variableBindings := snmpClient.Walk(InterfaceStatusOID, err)

	if variableBindings != nil && len(variableBindings) > 0 {

		for index := range variableBindings {

			var status = GetInterfaceStatus(MotadataString(ToString(variableBindings[index].Value)))

			snmpClient.logger.Trace(MotadataString(fmt.Sprintf("%s interface status is %s", variableBindings[index].Name, status)))

			if status == consts.StatusUp {

				index := strings.Split(variableBindings[index].Name, consts.DotSeparator)

				indices = append(indices, index[len(index)-1])
			}

		}
	}

	return indices
}

func (snmpClient *SNMPClient) Set(variableBindings []g.SnmpPDU) (status bool) {

	result, err := snmpClient.params.Set(variableBindings)

	if err == nil {

		if result != nil && result.Error == g.NoError {

			status = true

		} else if result != nil && result.Error == g.NoAccess {

			snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
				consts.ErrorCode: consts.ErrorCodeNoAccess,
				consts.Message:   consts.ErrorMessageInsufficientAccess,
			})

		} else if result != nil {

			snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
				consts.ErrorCode: consts.ErrorCodeInternalError,
				consts.Message:   fmt.Sprintf(consts.ErrorMessageSNMPSet, result.Error, result.ErrorIndex),
			})

		} else {

			snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
				consts.ErrorCode: consts.ErrorCodeInternalError,
				consts.Message:   "Unknown error",
			})

		}

	} else if MotadataString(err.Error()).Contains("timeout") {

		snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
			consts.Error:     err.Error(),
			consts.ErrorCode: consts.ErrorCodeTimeout,
			consts.Message:   err.Error(),
		})

		snmpClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageConnectionTimeout, "SNMP", snmpClient.target, snmpClient.port)))

	} else if MotadataString(err.Error()).Contains("connection refused") {

		snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
			consts.Error:     err.Error(),
			consts.ErrorCode: consts.ErrorCodeConnectionFailed,
			consts.Message:   err.Error(),
		})

	} else {

		snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
			consts.Error:     err.Error(),
			consts.ErrorCode: consts.ErrorCodeInternalError,
			consts.Message:   err.Error(),
		})

		snmpClient.logger.Warn(MotadataString(err.Error()))
	}

	return status
}

func (snmpClient *SNMPClient) GetTablesByOIDIndex(tabularOIDs MotadataMap, appendIndex, appendOID, manipulate bool) (tables MotadataMap) {

	tables = make(MotadataMap)

	for key, oids := range tabularOIDs {

		table := snmpClient.getTableByOIDIndex(ToMap(oids), appendIndex, appendOID, manipulate)

		if IsNotEmptyMapSlice(table) {

			tables[key] = table
		}
	}

	snmpClient.logger.Debug(MotadataString(fmt.Sprintf("tabular OID result of metric %s for target %s : %s", tabularOIDs, snmpClient.target, tables)))

	return
}
