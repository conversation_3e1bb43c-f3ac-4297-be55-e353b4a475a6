/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package snmpclient

import (
	"errors"
	"fmt"
	"github.com/apaxa-go/eval"
	g "github.com/gosnmp/gosnmp"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"net"
	"regexp"
	"runtime"
	"strconv"
	"strings"
)

var (
	Pattern = regexp.MustCompile("[/*+()\\-%]")

	InterfaceStatusPattern = regexp.MustCompile(consts.SNMPDigitRegexPattern)

	interfaceType = map[int]string{

		1:   "other",
		2:   "regular1822",
		3:   "hdh1822",
		4:   "ddnX25",
		5:   "rfc877x25",
		6:   "ethernetCsmacd",
		7:   "iso88023Csmacd",
		8:   "iso88024TokenBus",
		9:   "iso88025TokenRing",
		10:  "iso88026Man",
		11:  "starLan",
		12:  "proteon10Mbit",
		13:  "proteon80Mbit",
		14:  "hyperchannel",
		15:  "fddi",
		16:  "lapb",
		17:  "sdlc",
		18:  "ds1",
		19:  "e1",
		20:  "basicISDN",
		21:  "primaryISDN",
		22:  "propPointToPointSerial",
		23:  "ppp",
		24:  "softwareLoopback",
		25:  "eon",
		26:  "ethernet3Mbit",
		27:  "nsip",
		28:  "slip",
		29:  "ultra",
		30:  "ds3",
		31:  "sip",
		32:  "frameRelay",
		33:  "rs232",
		34:  "para",
		35:  "arcnet",
		36:  "arcnetPlus",
		37:  "atm",
		38:  "miox25",
		39:  "sonet",
		40:  "x25ple",
		41:  "iso88022llc",
		42:  "localTalk",
		43:  "smdsDxi",
		44:  "frameRelayService",
		45:  "v35",
		46:  "hssi",
		47:  "hippi",
		48:  "modem",
		49:  "aal5",
		50:  "sonetPath",
		51:  "sonetVT",
		52:  "smdsIcip",
		53:  "propVirtual",
		54:  "propMultiplexor",
		55:  "ieee80212",
		56:  "fibreChannel",
		57:  "hippiInterface",
		58:  "frameRelayInterconnect",
		59:  "aflane8023",
		60:  "aflane8025",
		61:  "cctEmul",
		62:  "fastEther",
		63:  "isdn",
		64:  "v11",
		65:  "v36",
		66:  "g703at64k",
		67:  "g703at2mb",
		68:  "qllc",
		69:  "fastEtherFX",
		70:  "channel",
		71:  "ieee80211",
		72:  "ibm370parChan",
		73:  "escon",
		74:  "dlsw",
		75:  "isdns",
		76:  "isdnu",
		77:  "lapd",
		78:  "ipSwitch",
		79:  "rsrb",
		80:  "atmLogical",
		81:  "ds0",
		82:  "ds0Bundle",
		83:  "bsc",
		84:  "async",
		85:  "cnr",
		86:  "iso88025Dtr",
		87:  "eplrs",
		88:  "arap",
		89:  "propCnls",
		90:  "hostPad",
		91:  "termPad",
		92:  "frameRelayMPI",
		93:  "x213",
		94:  "adsl",
		95:  "radsl",
		96:  "sdsl",
		97:  "vdsl",
		98:  "iso88025CRFPInt",
		99:  "myrinet",
		100: "voiceEM",
		101: "voiceFXO",
		102: "voiceFXS",
		103: "voiceEncap",
		104: "voiceOverIp",
		105: "atmDxi",
		106: "atmFuni",
		107: "atmIma",
		108: "pppMultilinkBundle",
		109: "ipOverCdlc",
		110: "ipOverClaw",
		111: "stackToStack",
		112: "virtualIpAddress",
		113: "mpc",
		114: "ipOverAtm",
		115: "iso88025Fiber",
		116: "tdlc",
		117: "gigabitEthernet",
		118: "hdlc",
		119: "lapf",
		120: "v37",
		121: "x25mlp",
		122: "x25huntGroup",
		123: "transpHdlc",
		124: "interleave",
		125: "fast",
		126: "ip",
		127: "docsCableMaclayer",
		128: "docsCableDownstream",
		129: "docsCableUpstream",
		130: "a12MppSwitch",
		131: "tunnel",
		132: "coffee",
		133: "ces",
		134: "atmSubInterface",
		135: "l2vlan",
		136: "l3ipvlan",
		137: "l3ipxvlan",
		138: "digitalPowerline",
		139: "mediaMailOverIp",
		140: "dtm",
		141: "dcn",
		142: "ipForward",
		143: "msdsl",
		144: "ieee1394",
		145: "if-gsn",
		146: "dvbRccMacLayer",
		147: "dvbRccDownstream",
		148: "dvbRccUpstream",
		149: "atmVirtual",
		150: "mplsTunnel",
		151: "srp",
		152: "voiceOverAtm",
		153: "voiceOverFrameRelay",
		154: "idsl",
		155: "compositeLink",
		156: "ss7SigLink",
		157: "propWirelessP2P",
		158: "frForward",
		159: "rfc1483",
		160: "usb",
		161: "ieee8023adLag",
		162: "bgppolicyaccounting",
		163: "frf16MfrBundle",
		164: "h323Gatekeeper",
		165: "h323Proxy",
		166: "mpls",
		167: "mfSigLink",
		168: "hdsl2",
		169: "shdsl",
		170: "ds1FDL",
		171: "pos",
		172: "dvbAsiIn",
		173: "dvbAsiOut",
		174: "plc",
		175: "nfas",
		176: "tr008",
		177: "gr303RDT",
		178: "gr303IDT",
		179: "isup",
		180: "propDocsWirelessMaclayer",
		181: "propDocsWirelessDownstream",
		182: "propDocsWirelessUpstream",
		183: "hiperlan2",
		184: "propBWAp2Mp",
		185: "sonetOverheadChannel",
		186: "digitalWrapperOverheadChannel",
		187: "aal2",
		188: "radioMAC",
		189: "atmRadio",
		190: "imt",
		191: "mvl",
		192: "reachDSL",
		193: "frDlciEndPt",
		194: "atmVciEndPt",
		195: "opticalChannel",
		196: "opticalTransport",
		197: "propAtm",
		198: "voiceOverCable",
		199: "infiniband",
		200: "teLink",
		201: "q2931",
		202: "virtualTg",
		203: "sipTg",
		204: "sipSig",
		205: "docsCableUpstreamChannel",
		206: "econet",
		207: "pon155",
		208: "pon622",
		209: "bridge",
		210: "linegroup",
		211: "voiceEMFGD",
		212: "voiceFGDEANA",
		213: "voiceDID",
		214: "mpegTransport",
		215: "sixToFour",
		216: "gtp",
		217: "pdnEtherLoop1",
		218: "pdnEtherLoop2",
		219: "opticalChannelGroup",
		220: "homepna",
		221: "gfp",
		222: "ciscoISLvlan",
		223: "actelisMetaLOOP",
		224: "fcipLink",
		225: "rpr",
		226: "qam",
		227: "lmp",
		228: "cblVectaStar",
		229: "docsCableMCmtsDownstream",
		230: "adsl2",
		231: "macSecControlledIF",
		232: "macSecUncontrolledIF",
		233: "aviciOpticalEther",
		234: "atmbond",
		235: "voiceFGDOS",
		236: "mocaVersion1",
		237: "ieee80216WMAN",
		238: "adsl2plus",
		239: "dvbRcsMacLayer",
		240: "dvbTdm",
		241: "dvbRcsTdma",
		242: "x86Laps",
		243: "wwanPP",
		244: "wwanPP2",
		245: "voiceEBS",
		246: "ifPwType",
		247: "ilan",
		248: "pip",
		249: "aluELP",
		250: "gpon",
		251: "vdsl2",
		252: "capwapDot11Profile",
		253: "capwapDot11Bss",
		254: "capwapWtpVirtualRadio",
		255: "bits",
		256: "docsCableUpstreamRfPort",
		257: "cableDownstreamRfPort",
		258: "vmwareVirtualNic",
		259: "ieee802154",
		260: "otnOdu",
		261: "otnOtu",
		262: "ifVfiType",
		263: "g9981",
		264: "g9982",
		265: "g9983",
		266: "aluEpon",
		267: "aluEponOnu",
		268: "aluEponPhysicalUni",
		269: "aluEponLogicalLink",
		270: "aluGponOnu",
		271: "aluGponPhysicalUni",
		272: "vmwareNicTeam",
	}
)

const (
	InterfaceStatusOID = ".*******.*******.1.8"

	InterfaceIndex32BitOID = ".*******.*******.1.10"

	InterfaceIndex64BitOID = ".*******.********.1.1.6"

	Interface32Bit = "0"

	Interface64Bit = "1"

	ComputationValue = "$$$VALUE$$$"

	StatusAssociated = "associated"

	StatusDisassociating = "disassociating"

	StatusDownloading = "downloading"
)

func getOperationStatuses() MotadataStringList {

	return MotadataStringList{StatusAssociated, StatusDisassociating, StatusDownloading}
}

func getAccessPointStatus() MotadataStringList {

	return MotadataStringList{"Up", "Down"}
}

/*
getIPSLAAdminStatuses
Admin statues denotes the status of the conceptual RTT control row (it the configuration status of ipsla configured on source device)
The active status here doesn't indicate that wan link is up.
*/
func getIPSLAAdminStatuses() MotadataIntMap {

	return MotadataIntMap{
		1: "active",
		2: "notInService",
		3: "notReady",
		4: "createAndGo",
		5: "createAndWait",
		6: "destroy",
	}
}

/*
getIPSLARTTCompletionStatuses
These are the completion statues for latest RTT operation
*/
func getIPSLARTTCompletionStatuses() MotadataIntMap {

	return MotadataIntMap{
		0:  "other",
		1:  "ok",
		2:  "disconnected",
		3:  "overThreshold",
		4:  "timeout",
		5:  "busy",
		6:  "notConnected",
		7:  "dropped",
		8:  "sequenceError",
		9:  "verifyError",
		10: "applicationSpecific",
		11: "dnsServerTimeout",
		12: "tcpConnectTimeout",
		13: "httpTransactionTimeout",
		14: "dnsQueryError",
		15: "httpError",
		16: "error",
		17: "mplsLspEchoTxError",
		18: "mplsLspUnreachable",
		19: "mplsLspMalformedReq",
		20: "mplsLspReachButNotFEC",
		21: "enableOk",
		22: "enableNoConnect",
		23: "enableVersionFail",
		24: "enableInternalError",
		25: "enableAbort",
		26: "enableFail",
		27: "enableAuthFail",
		28: "enableFormatError",
		29: "enablePortInUse",
		30: "statsRetrieveOk",
		31: "statsRetrieveNoConnect",
		32: "statsRetrieveVersionFail",
		33: "statsRetrieveInternalError",
		34: "statsRetrieveAbort",
		35: "statsRetrieveFail",
		36: "statsRetrieveAuthFail",
		37: "statsRetrieveFormatError",
		38: "statsRetrievePortInUse",
	}
}

//SNMP OIDs

func getInterface32BitOIDs() MotadataStringMap {

	return MotadataStringMap{
		".*******.*******.1.1":  consts.Interface,
		".*******.*******.1.7":  consts.InterfaceAdminStatus,
		".*******.*******.1.8":  consts.InterfaceOperationalStatus,
		".*******.*******.1.10": consts.InterfaceReceivedOctets,
		".*******.*******.1.11": consts.InterfaceInPackets,
		".*******.*******.1.13": consts.InterfaceReceivedDiscardPackets,
		".*******.*******.1.14": consts.InterfaceReceivedErrorPackets,
		".*******.*******.1.16": consts.InterfaceSentOctets,
		".*******.*******.1.17": consts.InterfaceOutPackets,
		".*******.*******.1.19": consts.InterfaceSentDiscardPackets,
		".*******.*******.1.20": consts.InterfaceSentErrorPackets,
		".*******.*******.1.9":  consts.InterfaceLastChange,
	}

}

func getInterface64BitOIDs() MotadataStringMap {

	return MotadataStringMap{
		".*******.*******.1.1":     consts.Interface,
		".*******.*******.1.7":     consts.InterfaceAdminStatus,
		".*******.*******.1.8":     consts.InterfaceOperationalStatus,
		".*******.********.1.1.6":  consts.InterfaceReceivedOctets,
		".*******.********.1.1.7":  consts.InterfaceInPackets,
		".*******.*******.1.13":    consts.InterfaceReceivedDiscardPackets,
		".*******.*******.1.14":    consts.InterfaceReceivedErrorPackets,
		".*******.********.1.1.10": consts.InterfaceSentOctets,
		".*******.********.1.1.11": consts.InterfaceOutPackets,
		".*******.*******.1.19":    consts.InterfaceSentDiscardPackets,
		".*******.*******.1.20":    consts.InterfaceSentErrorPackets,
		".*******.*******.1.9":     consts.InterfaceLastChange}
}

func getInterface32BitDiscoveryOIDs() MotadataStringMap {

	return MotadataStringMap{
		".*******.*******.1.1":     consts.Interface,
		".*******.********.1.1.1":  consts.InterfaceName,
		".*******.********.1.1.18": consts.InterfaceAlias,
		".*******.*******.1.5":     consts.InterfaceSpeedBytesPerSec,
		".*******.*******.1.6":     consts.InterfaceAddress,
		".*******.*******.1.2":     consts.InterfaceDescription,
		".*******.*******.1.8":     consts.Status,
		".*******.*******.1.3":     consts.InterfaceType}
}

func getInterface64BitDiscoveryOIDs() MotadataStringMap {

	return MotadataStringMap{
		".*******.*******.1.1":     consts.Interface,
		".*******.********.1.1.1":  consts.InterfaceName,
		".*******.********.1.1.18": consts.InterfaceAlias,
		".*******.********.1.1.15": consts.InterfaceSpeedBytesPerSec,
		".*******.*******.1.2":     consts.InterfaceDescription,
		".*******.*******.1.6":     consts.InterfaceAddress,
		".*******.*******.1.8":     consts.Status,
		".*******.*******.1.3":     consts.InterfaceType}
}

func getInterfaceIPAddressOIDs() MotadataMap {

	return MotadataMap{

		consts.InterfaceIPAddress: MotadataMap{
			".*******.********.1.1": "address",
			".*******.********.1.2": "address.index"},
	}

}

func getInterfaceIPv6AddressOIDs() MotadataMap {

	return MotadataMap{

		consts.InterfaceIPAddress: MotadataMap{
			".*******.********.1.3": "address",
		},
	}

}

func getDiscoveryOIDs() MotadataStringMap {

	return MotadataStringMap{
		".*******.*******.0": consts.ObjectSystemOID,
		".*******.*******.0": consts.ObjectName}
}

func getIPSLAOIDs(index string) MotadataStringMap {

	return MotadataStringMap{
		".*******.*******.*********.1.1." + index: consts.Latency,
		".*******.*******.*********.1.2." + index: consts.RTTStatus,
		".*******.*******.********.1.9." + index:  consts.AdminStatus,
		".*******.*******.********.1.2." + index:  consts.Owner,
	}
}

//This method is used for getting interfaces data

func GetInterfaceMetrics(interfaceIndices []interface{}, discovery bool, snmpClient *SNMPClient, errors []MotadataStringMap, discoverDownInterface MotadataString) ([]MotadataMap, []MotadataStringMap) {

	var invalidMetrics MotadataStringList

	var networkInterfaces []MotadataMap

	interfaceIPAddresses := make(MotadataMap)

	contexts, errors := GetInterfaceOIDs(interfaceIndices, discovery, snmpClient, errors, discoverDownInterface)

	if discovery {

		interfaceIPAddresses = GetInterfaceIPAddresses(snmpClient)

	}

	if contexts != nil {

		for _, oids := range contexts {

			networkInterface := make(MotadataMap)

			if oids.IsNotEmpty() && len(oids[consts.InterfaceBitType]) > 0 {

				interfaceBitType := consts.BlankString

				if oids.Contains(consts.InterfaceBitType) {

					interfaceBitType = ToString(oids[consts.InterfaceBitType])

					oids.Delete(consts.InterfaceBitType)

				}

				networkInterface, _ = snmpClient.GetScalarMetrics(oids, invalidMetrics, nil, nil, false)

				networkInterface[consts.InterfaceBitType] = interfaceBitType

				if networkInterface.GetStringValue(consts.Interface) != consts.BlankString {

					networkInterface[consts.Interface] = networkInterface.GetStringValue(consts.Interface)

					if networkInterface[consts.InterfaceSpeedBytesPerSec] != nil {

						if interfaceBitType == Interface64Bit {

							networkInterface[consts.InterfaceSpeedBytesPerSec] = (networkInterface.GetINTValue(consts.InterfaceSpeedBytesPerSec) * 1000000) / 8

						} else {

							networkInterface[consts.InterfaceSpeedBytesPerSec] = networkInterface.GetINTValue(consts.InterfaceSpeedBytesPerSec) / 8
						}
					}
					if networkInterface.IsNotEmpty() && networkInterface[consts.Interface] != nil {

						if interfaceIPAddresses.IsNotEmpty() && interfaceIPAddresses[networkInterface.GetStringValue(consts.Interface)] != nil && discovery {

							ipAddress := ToMotadataString(interfaceIPAddresses[networkInterface.GetStringValue(consts.Interface)])

							networkInterface[consts.InterfaceIPAddress] = interfaceIPAddresses[networkInterface.GetStringValue(consts.Interface)]

							if ipAddress.Contains(consts.IPVersionSeparator) {

								networkInterface[consts.InterfaceIPAddress] = ipAddress.Split(consts.IPVersionSeparator)[0]

								networkInterface[consts.InterfaceLocalIPAddress] = ipAddress.Split(consts.IPVersionSeparator)[1]

							}
						}
						networkInterfaces = append(networkInterfaces, networkInterface)
					}
					if networkInterface.Contains(consts.InterfaceType) {

						networkInterface[consts.InterfaceType] = getInterfaceType(networkInterface.GetIntValue(consts.InterfaceType))
					}
				}

			}

		}

	}

	snmpClient.logger.Debug(MotadataString(fmt.Sprintf("interface result of %s:%d is %s", snmpClient.target, snmpClient.port, ToJSON(networkInterfaces))))

	return networkInterfaces, errors
}

func getInterfaceType(result int) string {

	if value, found := interfaceType[result]; found {

		return value + " (" + strconv.Itoa(result) + ")"
	}

	return consts.StatusUnknown + " (" + strconv.Itoa(result) + ")"
}

func GetInterfaceIPAddresses(snmpClient *SNMPClient) MotadataMap {

	ipAddresses := make(MotadataMap)

	// try to get output with new oids having ipv4 + ipv6 support
	table, found := getInterfaceIPAddresses(snmpClient)

	if !found {

		// if no data found from new oid then it will try to get data from old oids
		tables := snmpClient.GetTables(getInterfaceIPAddressOIDs(), false, true, false, nil)

		if tables[consts.InterfaceIPAddress] != nil {

			for _, row := range tables.GetMapSliceValue(consts.InterfaceIPAddress) {

				if row["address"] != nil && row["address.index"] != nil {

					ipAddresses[row.GetStringValue("address.index")] = row["address"]
				}
			}
		}

	} else {

		for _, row := range table {

			if row["address"] != nil && row["address.index"] != nil {

				ipAddresses[row.GetStringValue("address.index")] = row["address"]
			}
		}
	}

	snmpClient.logger.Debug(MotadataString(fmt.Sprintf("interface ip addresses of %s:%d result is %s", snmpClient.target, snmpClient.port, ipAddresses.ToJSON())))

	return ipAddresses

}

func GetInterfaceOIDs(networkInterfaces []interface{}, discovery bool, snmpClient *SNMPClient, errors []MotadataStringMap, discoverDownInterface MotadataString) ([]MotadataStringMap, []MotadataStringMap) {

	var contexts []MotadataStringMap

	interfaceOIDs32Bit := getInterface32BitOIDs()

	interfaceOIDs64Bit := getInterface64BitOIDs()

	oids := MotadataStringList{}

	if discovery {

		interfaceOIDs32Bit = getInterface32BitDiscoveryOIDs()

		interfaceOIDs64Bit = getInterface64BitDiscoveryOIDs()

		if discoverDownInterface == consts.Yes {

			oids = snmpClient.GetUpInterfaces()
		}

	}

	if IsNotEmpty(networkInterfaces) {

		var interfaceBitType MotadataString

		var interfaceIndex MotadataString

		for _, networkInterface := range networkInterfaces {

			for key, value := range ToMap(networkInterface) {

				if key == consts.InterfaceBitType {

					interfaceBitType = ToMotadataString(value)

				} else if key == consts.Interface {

					interfaceIndex = ToMotadataString(value)
				}
			}
			context := make(MotadataStringMap)

			tokens := interfaceIndex.Split("-")

			if interfaceBitType == Interface32Bit {

				appendIndex(interfaceOIDs32Bit, tokens[len(tokens)-1], context)

				context[consts.InterfaceBitType] = Interface32Bit

				contexts = append(contexts, context)

			} else if interfaceBitType == Interface64Bit {

				appendIndex(interfaceOIDs64Bit, tokens[len(tokens)-1], context)

				context[consts.InterfaceBitType] = Interface64Bit

				contexts = append(contexts, context)
			}
		}

	} else {

		interfaceIndices32Bit, errors := GetInterfaceIndices(InterfaceIndex32BitOID, snmpClient, errors, oids, discoverDownInterface)

		interfaceIndices64Bit, errors := GetInterfaceIndices(InterfaceIndex64BitOID, snmpClient, errors, oids, discoverDownInterface)

		for _, index32Bit := range interfaceIndices32Bit {

			if len(index32Bit) != 0 {

				if !interfaceIndices64Bit.Contains(MotadataString(index32Bit)) {

					context := make(MotadataStringMap)

					appendIndex(interfaceOIDs32Bit, MotadataString(index32Bit), context)

					context[consts.InterfaceBitType] = Interface32Bit

					contexts = append(contexts, context)
				}
			}
		}

		for _, index64Bit := range interfaceIndices64Bit {

			if len(index64Bit) != 0 {

				context := make(MotadataStringMap)

				appendIndex(interfaceOIDs64Bit, MotadataString(index64Bit), context)

				context[consts.InterfaceBitType] = Interface64Bit

				contexts = append(contexts, context)
			}

		}
	}

	snmpClient.logger.Debug(MotadataString(fmt.Sprintf("interface OIDs %s of %s:%d", ToJSON(contexts), snmpClient.target, snmpClient.port)))

	return contexts, errors
}

func GetInterfaceIndices(interfaceOIDType MotadataString, snmpClient *SNMPClient, errors []MotadataStringMap, oids MotadataStringList, discoverDownInterface MotadataString) (MotadataStringList, []MotadataStringMap) {

	var indices MotadataStringList

	variableBindings, err := snmpClient.params.WalkAll(interfaceOIDType.ToString())

	if err != nil {

		errors = append(errors, MotadataStringMap{
			consts.Error:     err.Error(),
			consts.ErrorCode: consts.ErrorCodeInternalError,
			consts.Message:   err.Error(),
		})

		snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Interface Index Variable binding error %s", ToJSON(errors))))
	}
	if variableBindings != nil && len(variableBindings) > 0 {

		for _, variableBinding := range variableBindings {

			if variableBinding.Value != nil && len(variableBinding.Name) > 0 {

				index := strings.Split(variableBinding.Name, consts.DotSeparator)

				if oids != nil && discoverDownInterface == consts.Yes {

					if oids.Contains(MotadataString(index[len(index)-1])) {

						indices = append(indices, index[len(index)-1])
					}
				} else {
					indices = append(indices, index[len(index)-1])
				}

			}
		}
	}
	return indices, errors
}

func appendIndex(oids MotadataStringMap, index MotadataString, context MotadataStringMap) {

	for oid, name := range oids {

		context[oid+consts.DotSeparator+index.ToString()] = name

	}

}

//This method is used for getting composite data

func GetCompositeMetrics(oids MotadataStringMap, result MotadataMap, snmpClient *SNMPClient, converters MotadataMap, probes MotadataMap, discoveryRequest bool) []MotadataStringMap {

	errors := make([]MotadataStringMap, 0)

	if oids.IsNotEmpty() {

		for oid := range oids {

			oidName := oids[oid]

			if len(oidName) > 0 && len(oid) > 0 {

				/* if we already found scalar oid for particular metric then
				we will skip evaluating composite oid for that metric to save unwanted network call.

				For ex : if we already found "system.cpu.percent" oid while evaluating scalar oids then skip composite oid evaluation.
				*/

				if discoveryRequest && probes.IsNotEmpty() && probes.Contains(oidName) {

					continue
				}

				value, err := ProcessCompositeOID(MotadataString(oid), MotadataString(oidName), snmpClient, converters)

				if len(err) > 0 {

					errors = append(errors, err...)

				} else {

					result[oidName] = value

					result[oidName] = DoTransformation(snmpClient, converters, oid, value, false)

					if discoveryRequest {

						probes[oidName] = oid
					}
				}

			}
		}
	}

	snmpClient.logger.Debug(MotadataString(fmt.Sprintf("composite oid %s result of %s:%d is %s", oids, snmpClient.target, snmpClient.port, result.ToJSON())))

	if errors != nil && len(errors) > 0 {

		snmpClient.logger.Debug(MotadataString(fmt.Sprintf("Errors :%s", ToJSON(errors))))
	}

	return errors
}

func ProcessCompositeOID(oid MotadataString, oidName MotadataString, snmpClient *SNMPClient, converters MotadataMap) (MotadataINT, []MotadataStringMap) {

	tokens := Pattern.Split(oid.ToString(), -1)

	errors := make([]MotadataStringMap, 0)

	value := MotadataINT(0)

	if tokens != nil && len(tokens) > 1 {

		validOID := true

		for index := range tokens {

			token := ToMotadataString(tokens[index]).ToString()

			if token == consts.DotSeparator {

				oid = MotadataString(strings.Replace(oid.ToString(), token, consts.BlankString, 1))

			} else if len(token) > 0 && strings.HasPrefix(token, consts.DotSeparator) {

				if strings.HasSuffix(token, ".0") {

					oids := make(MotadataStringMap)

					oids[token] = oidName.ToString()

					metrics, _ := snmpClient.GetScalarMetrics(oids, nil, converters, nil, false)

					if metrics.IsNotEmpty() {

						for metric := range metrics {

							if metrics[metric] != nil {

								value = metrics.GetINTValue(metric)
							}
						}

						oid = MotadataString(strings.Replace(oid.ToString(), token, value.ToNativeString(), 1))

					} else {

						validOID = false

						snmpClient.logger.Trace(MotadataString(fmt.Sprintf("Invalid SNMP OID Values, can not evaluate expression for %s : %s", oidName, oid)))

						errors = append(errors, MotadataStringMap{
							consts.Error:     "Invalid SNMP OID Values",
							consts.ErrorCode: consts.ErrorCodeInvalidOIDValue,
							OID:              token,
							consts.Message:   fmt.Sprintf("Invalid SNMP OID Values, can not evaluate expression %s", oid),
						})

						break
					}

				} else {

					oids := make(MotadataMap)

					oids[token] = oidName.ToString()

					rows := snmpClient.getTable(oids, false, false, false, converters)

					if IsNotEmptyMapSlice(rows) {

						value = DoSummationMetric(rows, oidName)

						oid = MotadataString(strings.Replace(oid.ToString(), token, value.ToNativeString(), 1))

					} else {

						validOID = false

						snmpClient.logger.Trace(MotadataString(fmt.Sprintf("Invalid SNMP OID Values, can not evaluate expression for %s : %s", oidName, oid)))

						errors = append(errors, MotadataStringMap{
							consts.Error:     "Invalid SNMP OID Values",
							consts.ErrorCode: consts.ErrorCodeInvalidOIDValue,
							OID:              token,
							consts.Message:   fmt.Sprintf("Invalid SNMP OID Values, can not evaluate expression %s", oid),
						})

						break
					}
				}
			}
		}

		if validOID {

			expr, err := eval.ParseString(oid.ToString(), consts.BlankString)

			if err != nil {
				errors = append(errors, MotadataStringMap{
					consts.Error:     err.Error(),
					consts.ErrorCode: consts.ErrorCodeInternalError,
					consts.Message:   err.Error(),
				})

				snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Process Composite OIDs :Error for valid OID %s", ToJSON(errors))))
			}
			if expr != nil {

				result, err := expr.EvalToInterface(nil)

				if err != nil {

					errors = append(errors, MotadataStringMap{
						consts.Error:     err.Error(),
						consts.ErrorCode: consts.ErrorCodeInternalError,
						consts.Message:   err.Error(),
					})

					snmpClient.logger.Trace(MotadataString(fmt.Sprintf("Process Composite OIDs :Error for valid OID  Expr %s", ToJSON(errors))))
				}

				if result != nil {

					value = ToINT(result)

				}

			}
		}

	} else {

		oids := make(MotadataMap)

		oids[oid.ToString()] = oidName

		rows := snmpClient.getTable(oids, false, false, false, nil)

		if IsNotEmptyMapSlice(rows) {

			value = DoSummationMetric(rows, oidName)
		}
	}

	return value, errors
}

func DoSummationMetric(rows []MotadataMap, name MotadataString) MotadataINT {

	result := MotadataINT(0)

	for index := range rows {

		row := rows[index]

		if row.IsNotEmpty() {

			result += row.GetINTValue(name.ToString())

		}
	}

	if result > 0 {

		result = result / MotadataINT(len(rows))
	}

	return result

}

func Discover(context MotadataMap, snmpClient *SNMPClient, errors []MotadataStringMap) []MotadataStringMap {

	ip := consts.LocalIP

	if context.Contains(consts.ObjectIP) {

		ip = context.GetStringValue(consts.ObjectIP)

	}

	discoveredObject := make(MotadataMap)

	metrics, _ := snmpClient.GetScalarMetrics(getDiscoveryOIDs(), nil, nil, nil, false)

	if metrics.IsNotEmpty() && metrics[consts.ObjectSystemOID] != nil {

		discoveredObject[consts.ObjectIP] = ip

		discoveredObject[consts.ObjectSystemOID] = metrics[consts.ObjectSystemOID]

		context[consts.Status] = consts.StatusSucceed

		if context[consts.InterfaceDiscovery] == nil || context[consts.InterfaceDiscovery] == consts.Yes {

			var networkInterfaces []MotadataMap

			var errs []MotadataStringMap

			if context.Contains(consts.DiscoverDownInterfaceStatus) {

				networkInterfaces, errs = GetInterfaceMetrics(nil, true, snmpClient, errors, context.GetMotadataStringValue(consts.DiscoverDownInterfaceStatus))

			} else {

				networkInterfaces, errs = GetInterfaceMetrics(nil, true, snmpClient, errors, consts.No)
			}

			if len(errs) > 0 {

				errors = append(errors, errs...)
			}
			for _, networkInterface := range networkInterfaces {

				networkInterface[consts.ObjectType] = consts.Interface

				networkInterface[consts.ObjectName] = networkInterface[consts.Interface]

				if networkInterface[consts.Status] != nil {

					networkInterface[consts.Status] = GetInterfaceStatus(networkInterface.GetMotadataStringValue(consts.Status))

					UpdateInterfaceDetails(networkInterface)
				}
			}

			discoveredObject[consts.Objects] = networkInterfaces
		}

		if metrics[consts.ObjectName] != nil {

			discoveredObject[consts.ObjectName] = metrics[consts.ObjectName]

			discoveredObject[consts.ObjectHost] = metrics[consts.ObjectName]

		}

		// If object host is present in context then priority will be given to that host name
		if context[consts.ObjectHost] != nil && len(context.GetStringValue(consts.ObjectHost)) > 0 {

			discoveredObject[consts.ObjectHost] = context[consts.ObjectHost]
		}

		context[consts.Objects] = []MotadataMap{discoveredObject}

	} else {

		errors = append(errors, MotadataStringMap{
			consts.ErrorCode: consts.ErrorCodeSNMPAgentSystemOIDNotFound,
			consts.Message:   fmt.Sprint("Failed to retrieve System OID. Please contact your device vendor"),
		})

		snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Error for Fetch data :Error code snmp agent oid not found :%s", ToJSON(errors))))
	}

	return errors
}

func UpdateInterfaceDetails(networkInterface MotadataMap) {

	interfaceName := MotadataString(consts.BlankString)

	name := MotadataString(consts.BlankString)

	if networkInterface[consts.InterfaceName] != nil {

		interfaceName = networkInterface.GetMotadataStringValue(consts.InterfaceName)

		name = interfaceName

	} else if networkInterface[consts.InterfaceDescription] != nil {

		interfaceName = networkInterface.GetMotadataStringValue(consts.InterfaceDescription)

	} else if networkInterface[consts.Interface] != nil {

		interfaceName = "Interface " + networkInterface.GetMotadataStringValue(consts.Interface)
	}

	if interfaceName.IsNotEmpty() {

		networkInterface[consts.InterfaceName] = interfaceName
	}

	networkInterface[consts.InterfaceIndex] = networkInterface.GetMotadataStringValue(consts.Interface)

	if name.IsNotEmpty() {

		networkInterface[consts.Interface] = name + "-" + networkInterface.GetMotadataStringValue(consts.Interface)

	} else {

		networkInterface[consts.Interface] = "Interface-" + networkInterface.GetMotadataStringValue(consts.Interface)
	}

}

func GetInterfaceStatus(status MotadataString) string {

	//BUG 24773 State has been changed to Up and down for discovery.
	switch status {
	case "1":
		return consts.StatusUp
	case "2":
		return consts.StatusDown
	case "3":
		return consts.StatusDown
	case consts.SNMPInterfaceStatusUp:
		return consts.StatusUp
	case consts.SNMPInterfaceStatusDown:
		return consts.StatusDown
	case "testing":
		return consts.StatusDown
	default:
		return consts.StatusDown
	}
}

func DoDiscoveryProbes(context MotadataMap, snmpClient *SNMPClient, logger *Logger) (result MotadataMap) {

	result = make(MotadataMap)

	var credentialProfiles []interface{}

	credentialProfiles = context.GetSliceValue(consts.DiscoveryCredentialProfiles)

	for _, credentialProfile := range credentialProfiles {

		profile := ToMap(credentialProfile)

		profile[consts.ObjectIP] = context[consts.ObjectIP]

		profile[consts.Port] = context[consts.Port]

		profile[consts.Timeout] = snmpClient.setTimeout(context).timeout.ToInt()

		snmpClient.SetContext(profile, logger)

		if snmpClient.Init() {

			result[consts.ObjectCredentialProfile] = profile[consts.Id]

			result[consts.CredentialProfileName] = profile[consts.CredentialProfileName]

			break
		}
	}

	return result
}

func SetupCleanupRoutine(snmpClient *SNMPClient, responses chan<- MotadataMap, response MotadataMap, requestType MotadataString) {

	if r := recover(); r != nil {

		err := make(MotadataStringMap)

		err[consts.Message] = fmt.Sprintf("%v", r)

		err[consts.ErrorCode] = consts.ErrorCodeInternalError

		err[consts.Error] = fmt.Sprintf("%v", r)

		response[consts.Errors] = append(response.GetStringMapSliceValue(consts.Errors), err)

		stackTraceBytes := make([]byte, consts.StackTraceSizeInBytes)

		PanicLogger.Fatal(MotadataString(fmt.Sprintf("serious error %v occurred in the plugin %v\nStack Trace ==> %v\n", r, RemoveSensitiveFields(response).ToJSON(), string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))))
	}

	if snmpClient.connected {

		snmpClient.Destroy()
	}

	if responses != nil {

		if requestType == consts.EventPoll {

			AddStatusField(response)
		}

		responses <- response

	}
}

func ResetErrors(snmpClient *SNMPClient) {

	for index := len(snmpClient.GetErrors()) - 1; index >= 0; index-- {

		if snmpClient.GetErrors()[index][consts.ErrorCode] == consts.ErrorCodeInternalError && snmpClient.GetErrors()[index][consts.Message] == "Unknown Error" {

			snmpClient.ResetErrors(index)

		}
	}
}

func DoTransformation(snmpClient *SNMPClient, context MotadataMap, oid string, result interface{}, ipv6 bool) interface{} {

	converters := context.GetMapValue(OIDGroupConverters)

	computations := context.GetMapValue(OIDGroupComputations)

	regexEvaluations := context.GetMapValue(OIDGroupRegex)

	if converters.IsNotEmpty() && converters.Contains(oid) && converters.GetMapValue(oid).IsNotEmpty() {

		for key, value := range converters.GetMapValue(oid) {

			if key == MotadataString(fmt.Sprint(result)).ToString() {
				result = value
			}
		}
	}

	if computations.IsNotEmpty() && computations.Contains(oid) {

		expression, err := eval.ParseString(MotadataString(computations.GetStringValue(oid)).Replace(ComputationValue, MotadataString(fmt.Sprint(result)), -1).ToString(), consts.BlankString)

		if err != nil {

			snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
				consts.Error:     err.Error(),
				consts.ErrorCode: consts.ErrorCodeInternalError,
				consts.Message:   err.Error(),
			})

			snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Error %v occurred while tranformating the result for %v", ToJSON(snmpClient.errors), context.GetStringValue(consts.ObjectIP))))

		}
		if expression != nil {

			value, err := expression.EvalToInterface(nil)

			if err != nil {

				snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
					consts.Error:     err.Error(),
					consts.ErrorCode: consts.ErrorCodeInternalError,
					consts.Message:   err.Error(),
				})

				snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Error %v occurred while tranformating the result for %v", ToJSON(snmpClient.errors), context.GetStringValue(consts.ObjectIP))))
			}

			result = ToINT(value)

		}
	}

	if regexEvaluations.IsNotEmpty() && regexEvaluations.Contains(oid) {

		pattern, err := regexp.Compile(regexEvaluations.GetStringValue(oid))

		if err == nil {

			matches := pattern.FindStringSubmatch(ToString(result))

			if len(matches) > 1 {

				result = matches[1]
			}

		} else {

			snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
				consts.Error:     err.Error(),
				consts.ErrorCode: consts.ErrorCodeInternalError,
				consts.Message:   err.Error(),
			})

			snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Error %v occurred while tranformating the result for %v", ToJSON(snmpClient.errors), context.GetStringValue(consts.ObjectIP))))
		}
	}

	// to extract ipv4 or ipv6 address from oid key
	if ipv6 && IsNotEmptyStringSlice(ToMotadataString(result).Split(oid)) {

		tokens := ToMotadataString(result).Split(oid)[0]

		// ipv4 address
		if tokens.HasPrefix(".1.4") && len(tokens.Split(consts.DotSeparator)) >= 4 {

			tokens = tokens[5:]

			values := tokens.Split(consts.DotSeparator)

			result = fmt.Sprintf("%v.%v.%v.%v", values[0], values[1], values[2], values[3])

		} else if tokens.HasPrefix(".2.16") || tokens.HasPrefix(".4.20") { // ipv6 addresses

			tokens = tokens[6:]

			if len(tokens.Split(consts.DotSeparator)) == 16 || len(tokens.Split(consts.DotSeparator)) == 20 {

				bytes := tokens.Split(consts.DotSeparator)

				result = BytesToIPv6Address(bytes)
			}
		} else {

			snmpClient.logger.Debug(MotadataString(fmt.Sprintf("Undefined address type %s : ", oid)))
		}
	}

	return result
}

func DiscoverAccessPoints(client *SNMPClient, oids MotadataMap) []MotadataMap {

	var accessPoints []MotadataMap

	tables := client.GetTables(oids, false, false, false, nil)

	if tables.IsNotEmpty() && tables.Contains(consts.WirelessAccessPoint) {

		for _, row := range tables.GetMapSliceValue(consts.WirelessAccessPoint) {

			accessPoint := make(MotadataMap)

			if row[consts.WirelessAccessPoint] != nil {

				accessPoint[consts.WirelessAccessPoint] = row.GetMotadataStringValue(consts.WirelessAccessPoint)

				accessPoint[consts.ObjectName] = row.GetMotadataStringValue(consts.WirelessAccessPoint)
			}

			if row[consts.WirelessAccessPointMACAddress] != nil {

				accessPoint[consts.WirelessAccessPointMACAddress] = StringSliceToMACAddress(row[consts.WirelessAccessPointMACAddress])
			}

			if row[consts.WirelessAccessPointOperationalStatus] != nil && row.GetIntValue(consts.WirelessAccessPointOperationalStatus)-1 >= 0 && len(getOperationStatuses()) > row.GetIntValue(consts.WirelessAccessPointOperationalStatus)-1 {

				accessPoint[consts.WirelessAccessPointOperationalStatus] = getOperationStatuses()[row.GetIntValue(consts.WirelessAccessPointOperationalStatus)-1]

				if accessPoint[consts.WirelessAccessPointOperationalStatus] == StatusAssociated {

					accessPoint[consts.Status] = consts.StatusUp

				} else if accessPoint[consts.WirelessAccessPointOperationalStatus] == StatusDownloading {

					accessPoint[consts.Status] = consts.StatusMaintenance

				} else {

					accessPoint[consts.Status] = consts.StatusDown

				}
			}

			if row.Contains(consts.WirelessAccessPointStatus) && row.GetIntValue(consts.WirelessAccessPointStatus)-1 >= 0 && row.GetIntValue(consts.WirelessAccessPointStatus)-1 < len(getAccessPointStatus()) {

				accessPoint[consts.Status] = getAccessPointStatus()[row.GetIntValue(consts.WirelessAccessPointStatus)-1]
			}

			accessPoint[consts.ObjectType] = "Access Point"

			//MOTADATA-1902
			if accessPoint.Contains(consts.Status) {

				accessPoints = append(accessPoints, accessPoint)
			}
		}

	}

	return accessPoints
}

func processCompositeTabularOID(oids MotadataMap, snmpClient *SNMPClient, errors []MotadataStringMap, converters MotadataMap) MotadataMap {

	rows := MotadataMap{}

	//oid -> .*******.********.3.1.5 - .*******.********.3.1.6

	oid := oids.GetKeys()[0]

	tokens := Pattern.Split(oid, -1)

	compositeOIDs := MotadataMap{}

	for _, token := range tokens {

		token = MotadataString(token).Strip().ToString()

		if token == consts.DotSeparator {

			continue

		} else if len(token) > 0 && strings.HasPrefix(token, consts.DotSeparator) {

			//key and value would be same for the map.
			compositeOIDs[token] = token

		}

	}

	// compositeOIDS(key,value) {.*******.********.3.1.5 -> .*******.********.3.1.5}
	//This row will get the data of the above composite key map.
	/*
		2 = .*******.********.3.1.6 -> 1123284
		0 = .*******.********.3.1.5 -> 11764568
		1 = .*******.********.3.1.5.index -> 40
		3 = .*******.********.3.1.6.index -> 40
	*/
	for _, row := range snmpClient.getTable(compositeOIDs, true, false, false, converters) {

		expression := oid

		//This for loop is for converting the oid into value
		for _, token := range tokens {

			token = MotadataString(token).Strip().ToString()

			if token == consts.DotSeparator {

				expression = strings.Replace(expression, token, consts.BlankString, 1)

			} else if len(token) > 0 && strings.HasPrefix(token, consts.DotSeparator) {

				if !row.GetMotadataStringValue(token).IsNotEmpty() {

					errors = append(errors, MotadataStringMap{
						consts.Error:     fmt.Sprintf(consts.ErrorMessageBadResponse, "from "+token+" oid"),
						consts.ErrorCode: consts.ErrorCodeBadResponse,
						consts.Message:   fmt.Sprintf(consts.ErrorMessageBadResponse, "from "+token+" oid"),
					})

					snmpClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageBadResponse, "from "+token+" oid in "+snmpClient.target.ToString())))

					return MotadataMap{}

				}

				//.*******.********.3.1.5 - .*******.********.3.1.6 => 3850716 - 1431676
				expression = MotadataString(expression).Replace(MotadataString(token), row.GetMotadataStringValue(token), 1).ToString()
			}
		}

		expr, err := eval.ParseString(expression, consts.BlankString)

		if err != nil {
			errors = append(errors, MotadataStringMap{
				consts.Error:     err.Error(),
				consts.ErrorCode: consts.ErrorCodeInternalError,
				consts.Message:   err.Error(),
			})

			snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Process Composite OIDs :Error for valid OID %s", ToJSON(errors))))

			return MotadataMap{}
		}

		if expr != nil {

			value, err := expr.EvalToInterface(nil)

			if err != nil {

				errors = append(errors, MotadataStringMap{
					consts.Error:     err.Error(),
					consts.ErrorCode: consts.ErrorCodeInternalError,
					consts.Message:   err.Error(),
				})

				snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Process Composite OIDs :Error for valid OID  Expr %s", ToJSON(errors))))

				return MotadataMap{}
			}

			for tabularOID, oidValue := range row {

				if MotadataString(tabularOID).HasSuffix(".index") && value != nil {

					rows[ToString(oidValue)] = MotadataMap{oids.GetStringValue(oid): DoTransformation(snmpClient, converters, oid, ToInt(value), false)}

					break
				}

			}

		}

	}

	if DebugEnabled() {

		snmpClient.logger.Debug(MotadataString(fmt.Sprintf("composite oid %s result of %s:%d is %s", oids, snmpClient.target, snmpClient.port, rows.ToJSON())))

	}

	return rows
}

func getInterfaceIPAddresses(snmpClient *SNMPClient) (table []MotadataMap, found bool) {

	oid := getInterfaceIPv6AddressOIDs().GetMapValue(consts.InterfaceIPAddress).GetKeys()[0]

	interfaceIndexes := make(map[int]int)

	var err error

	rows := make(map[int]MotadataMap)

	variableBindings := snmpClient.Walk(oid, err)

	if variableBindings != nil && len(variableBindings) > 0 {

		oidIndex := getInterfaceIPv6AddressOIDs().GetMapValue(consts.InterfaceIPAddress).GetStringValue(oid)

		for index := range variableBindings {

			ipAddress := ToMotadataString(DoTransformation(snmpClient, nil, oid, variableBindings[index].Name, true))

			valid := validInterface(ipAddress)

			if _, found := interfaceIndexes[ToInt(variableBindings[index].Value)]; valid && !found {

				rows[index] = make(MotadataMap)

				if variableBindings[index].Value != nil {

					interfaceIndexes[ToInt(variableBindings[index].Value)] = index

					rows[index][oidIndex] = variableBindings[index].Name

					rows[index][oidIndex] = ipAddress

					rows[index][oidIndex+".index"] = variableBindings[index].Value

					rows[index][oidIndex+".oid"] = variableBindings[index].Name

				} else {

					snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
						consts.Error:     "Invalid SNMP OID Values",
						consts.ErrorCode: consts.ErrorCodeInvalidOIDValue,
						OID:              variableBindings[index].Name,
						consts.Message:   "Invalid SNMP OID Values",
					})

					snmpClient.logger.Warn(MotadataString(fmt.Sprintf("Error %s occured while processing tabuler result %s for %s", ToJSON(snmpClient.errors), oid, snmpClient.target)))
				}

			} else if valid && IsNotEmptyStringSlice(ToMotadataString(variableBindings[index].Name).Split(oid)) && ToMotadataString(variableBindings[index].Name).Split(oid)[0].HasPrefix(".4.20") { // skipping ipv6 address if we already got its ipv4 address

				rowIndex := interfaceIndexes[ToInt(variableBindings[index].Value)]

				rows[rowIndex][oidIndex] = ToString(rows[rowIndex][oidIndex]) + consts.IPVersionSeparator + ToString(ipAddress)
			}
		}
	}

	for row := range rows {

		table = append(table, rows[row])
	}

	if IsNotEmptyMapSlice(table) {

		found = true

		snmpClient.logger.Debug(MotadataString(fmt.Sprintf("tabuler result %s for %s", table, snmpClient.target)))

	} else {

		snmpClient.logger.Trace(MotadataString(fmt.Sprintf("for oids %s tabuler result not found on %s", oid, snmpClient.target)))
	}

	return
}

func (snmpClient *SNMPClient) Walk(oid string, err error) (variableBindings []g.SnmpPDU) {

	if snmpClient.bulkWalk == true {

		variableBindings, err = snmpClient.params.BulkWalkAll(strings.TrimSpace(oid))

	} else {

		variableBindings, err = snmpClient.params.WalkAll(strings.TrimSpace(oid))
	}

	//version v1

	if err == nil && variableBindings == nil {

		snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
			consts.Error:     "Unknown Error",
			consts.ErrorCode: consts.ErrorCodeInternalError,
			consts.Message:   "Unknown Error",
			OID:              oid,
		})
		//Unknown error occured while getting tabuler oid .*******.2.1.14.10.1.1 for 10.235.24.9
		snmpClient.logger.Trace(MotadataString(fmt.Sprintf("Unknown error occured while getting tabuler oid %s for %s", oid, snmpClient.target)))
	} else /*for v2c/v3 */ if err != nil {

		snmpClient.errors = append(snmpClient.errors, MotadataStringMap{
			consts.Error:     err.Error(),
			consts.ErrorCode: consts.ErrorCodeInternalError,
			consts.Message:   err.Error(),
			OID:              oid,
		})

		snmpClient.logger.Trace(MotadataString(fmt.Sprintf("Error %s occured while getting tabuler oid %s for %s", err.Error(), oid, snmpClient.target)))

	}

	return
}

func validInterface(ipAddress MotadataString) bool {

	if !ipAddress.IsNotEmpty() || ipAddress.HasPrefix("0") {

		return false
	}

	ip := net.ParseIP(ipAddress.ToString())

	if ip != nil && (ip.IsLoopback() || ip.IsUnspecified() || ip.IsMulticast() || ip.IsInterfaceLocalMulticast() || ip.IsLinkLocalMulticast()) {

		return false
	}

	return true
}

func BuildSNMPPDUIntVariable(oid string, value int) g.SnmpPDU {
	return g.SnmpPDU{
		Name:  oid,
		Value: value,
		Type:  g.Integer,
	}
}

func BuildSNMPPDUStringVariable(oid string, value string) g.SnmpPDU {
	return g.SnmpPDU{
		Name:  oid,
		Value: value,
		Type:  g.OctetString,
	}
}

func BuildSNMPPDUTimeTickVariable(oid string, value uint32) g.SnmpPDU {
	return g.SnmpPDU{
		Name:  oid,
		Value: value,
		Type:  g.TimeTicks,
	}
}

func BuildSNMPPDUIPVariable(oid string, value string) (pdu g.SnmpPDU, err error) {

	var ip = net.ParseIP(value)

	var bytes []byte

	if ip != nil {

		//ipv6 case
		if ip.To4() == nil && ip.To16() != nil && consts.IPv6Pattern.MatchString(ip.To16().String()) {

			bytes = ip.To16()

		} else if ip.To4() != nil && consts.IPPattern.MatchString(ip.To4().String()) {

			//ipv4 case
			bytes = ip.To4()

		}
	}

	if bytes != nil {

		pdu = g.SnmpPDU{
			Name:  oid,
			Value: bytes,
			Type:  g.OctetString,
		}

	} else {

		// Invalid ip address
		err = errors.New(fmt.Sprintf("invalid ip address: %s", value))
	}

	return
}

// method to get common IPSLA metrics
func GetIPSLAMetrics(snmpClient *SNMPClient, index, ip, objectName, wanProbeType string, errors []MotadataStringMap) (MotadataMap, []MotadataStringMap) {

	metrics := make(MotadataMap)

	metrics[consts.Status] = consts.StatusDown // WAN Link will be considered down in case of failed polling

	metrics[consts.IPSLA] = objectName

	metrics[consts.IPSLAOperationType] = wanProbeType

	scalarMetrics, _ := snmpClient.GetScalarMetrics(getIPSLAOIDs(index), nil, nil, nil, false)

	if scalarMetrics.IsNotEmpty() {

		if scalarMetrics.Contains(consts.AdminStatus) && scalarMetrics[consts.AdminStatus] != nil {

			metrics[consts.AdminStatus] = getIPSLAAdminStatuses()[scalarMetrics.GetIntValue(consts.AdminStatus)]
		}

		if scalarMetrics.Contains(consts.RTTStatus) && scalarMetrics[consts.RTTStatus] != nil {

			metrics[consts.RTTStatus] = getIPSLARTTCompletionStatuses()[scalarMetrics.GetIntValue(consts.RTTStatus)]

			if scalarMetrics.GetINTValue(consts.RTTStatus) == consts.Ok || scalarMetrics.GetINTValue(consts.RTTStatus) == consts.EnableOk {

				metrics[consts.Status] = consts.StatusUp
			}
		}

		if scalarMetrics.Contains(consts.Owner) && scalarMetrics[consts.Owner] != nil {

			metrics[consts.Owner] = scalarMetrics.GetStringValue(consts.Owner)
		}

		if metrics.GetStringValue(consts.Status) == consts.StatusUp {

			if scalarMetrics.Contains(consts.Latency) && scalarMetrics[consts.Latency] != nil {

				metrics[consts.Latency] = scalarMetrics.GetIntValue(consts.Latency)

			}

		}

	} else {

		errors = append(errors, MotadataStringMap{
			consts.ErrorCode: consts.ErrorCodeIPSLANotFound,
			consts.Message:   fmt.Sprintf("No IPSLA Configured on %s : %s", ip, index),
		})

		snmpClient.logger.Warn(MotadataString(fmt.Sprintf("No IPSLA Configured on %s : %s", ip, index)))

	}

	ResetErrors(snmpClient)

	// in case when device is invalid
	for index := len(snmpClient.GetErrors()) - 1; index >= 0; index-- {

		if snmpClient.GetErrors()[index][consts.ErrorCode] == consts.ErrorCodeInvalidOIDValue && snmpClient.GetErrors()[index][consts.Message] == "Invalid SNMP OID Value" {

			snmpClient.ResetErrors(index)

		}
	}

	return metrics, errors
}
