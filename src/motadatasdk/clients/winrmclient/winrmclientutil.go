/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package winrmclient

import (
	"fmt"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"regexp"
	"runtime"
)

func ExtractMetricValue(result MotadataMap, value1 MotadataString, value2 MotadataString, key MotadataString) {

	numberPattern, _ := regexp.MatchString(consts.NumberRegexPattern, value1.TrimSpace().ToString())

	decimalPattern, _ := regexp.MatchString(consts.DecimalRegexPattern, value1.TrimSpace().ToString())

	if !numberPattern && !decimalPattern {

		result[key.ToString()] = value2.ToFloat64()

	} else {

		result[key.ToString()] = value1.ToFloat64()

	}
}

func (winRMClient *WinRMClient) Discover(context MotadataMap, host bool, logger *Logger) (result MotadataMap) {

	var credentialProfiles []interface{}

	credentialProfiles = context.GetSliceValue(consts.DiscoveryCredentialProfiles)

	result = make(MotadataMap)

	if len(credentialProfiles) > 0 {

		for _, credentialProfile := range credentialProfiles {

			profile := ToMap(credentialProfile)

			if host {

				profile[consts.ObjectHost] = context[consts.ObjectHost]
			}

			profile[consts.ObjectIP] = context[consts.ObjectIP]

			profile[consts.Port] = context[consts.Port]

			profile[consts.Timeout] = winRMClient.setTimeout(context).timeout

			winRMClient.SetContext(profile, host, logger)

			if winRMClient.Init() {

				result[consts.ObjectCredentialProfile] = profile[consts.Id]

				result[consts.CredentialProfileName] = profile[consts.CredentialProfileName]

				break
			}

		}

	} else {

		winRMClient.SetContext(context, host, logger)

	}

	return
}

func SetupCleanupRoutine(winRMClient *WinRMClient, responses chan<- MotadataMap, response MotadataMap, requestType MotadataString) {

	if r := recover(); r != nil {

		err := make(MotadataStringMap)

		err[consts.Message] = fmt.Sprintf("%v", r)

		err[consts.ErrorCode] = consts.ErrorCodeInternalError

		err[consts.Error] = fmt.Sprintf("%v", r)

		response[consts.Errors] = append(response.GetStringMapSliceValue(consts.Errors), err)

		stackTraceBytes := make([]byte, consts.StackTraceSizeInBytes)

		PanicLogger.Fatal(MotadataString(fmt.Sprintf("serious error %v occurred in the plugin %v\nStack Trace ==> %v\n", r, RemoveSensitiveFields(response).ToJSON(), string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))))
	}

	winRMClient.Destroy()

	if responses != nil {

		if requestType == consts.EventPoll {

			AddStatusField(response)
		}

		responses <- response

	}

}

func BuildWinRMDiscoveryResult(context MotadataMap, object MotadataMap, output MotadataString) {

	objectName := MotadataString(consts.BlankString)

	objectHost := MotadataString(consts.BlankString)

	if context[consts.ObjectHost] == nil {

		tokens := output.Split(consts.NewLineRegexPattern)

		for token := range tokens {

			if tokens[token].Contains("hostname") {

				objectHost = tokens[token-1].TrimSpace()

				objectName = tokens[token+1].TrimSpace()
			}
		}
		object[consts.ObjectHost] = objectName + "." + objectHost

		object[consts.ObjectName] = objectName
	}
}

func ExecuteInlineCommand(context MotadataMap, command MotadataString, host bool, commandIndex int, responses chan MotadataMap, logger *Logger) {

	client := WinRMClient{}

	client.SetContext(context, host, logger)

	defer client.Destroy()

	response := make(MotadataMap)

	if client.Init() {

		response[Output] = client.ExecuteCommand(command)

		response[Index] = commandIndex
	}

	if len(client.GetErrors()) > 0 {

		client.logger.Debug(MotadataString(fmt.Sprintf("ERROR : %s for %s", client.GetErrors(), context.GetStringValue(consts.ObjectIP))))

		response[consts.Errors] = client.GetErrors()
	}

	if response != nil {

		responses <- response
	}
}

func GetHostName(winRMClient *WinRMClient) MotadataString {

	target := winRMClient.ExecuteCommand(`hostname`).Strip()

	return target
}
