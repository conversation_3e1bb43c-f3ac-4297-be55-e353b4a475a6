/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package winrmclient

import (
	bytes2 "bytes"
	"fmt"
	"github.com/masterzen/winrm"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"os/exec"
	"regexp"
	"time"
)

const (
	Command = MotadataString("powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);@@@ | Format-List\"")

	Index = "command.index"

	Output = "command.output"
)

type WinRMClient struct {
	client *winrm.Client

	userName MotadataString

	password MotadataString

	target MotadataString

	port MotadataUINT16

	timeout MotadataUINT

	agent bool

	shell *winrm.Shell

	errors []MotadataStringMap

	logger *Logger
}

func (winRMClient *WinRMClient) GetErrors() []MotadataStringMap {

	return winRMClient.errors
}

func (winRMClient *WinRMClient) Destroy() {

	if winRMClient.shell != nil {

		_ = winRMClient.shell.Close()

		winRMClient.logger.Debug(MotadataString(fmt.Sprintf(consts.InfoMessageConnectionDestroyed, winRMClient.target, winRMClient.port.ToString())))

	}
}

func (winRMClient *WinRMClient) setLogger(logger *Logger) *WinRMClient {

	winRMClient.logger = logger

	return winRMClient
}

func (winRMClient *WinRMClient) IsAgent() bool {

	return winRMClient.agent
}

func (winRMClient *WinRMClient) setTarget(context MotadataMap, host bool) *WinRMClient {

	if host {

		winRMClient.target = context.GetMotadataStringValue(consts.ObjectHost)

	} else {

		if context.Contains(consts.ObjectIP) {

			winRMClient.target = context.GetMotadataStringValue(consts.ObjectIP)

		} else {

			winRMClient.target = consts.LocalIP
		}
	}

	return winRMClient

}

func (winRMClient *WinRMClient) setPort(context MotadataMap) *WinRMClient {

	if context.Contains(consts.Port) {

		winRMClient.port = context.GetUINT16Value(consts.Port)

	} else {

		winRMClient.port = MotadataUINT16(5985)
	}

	return winRMClient

}

func (winRMClient *WinRMClient) setUsername(context MotadataMap) *WinRMClient {

	if context.Contains(consts.UserName) {

		winRMClient.userName = context.GetMotadataStringValue(consts.UserName)
	}

	return winRMClient
}

func (winRMClient *WinRMClient) setPassword(context MotadataMap) *WinRMClient {

	if context.Contains(consts.Password) {

		winRMClient.password = context.GetMotadataStringValue(consts.Password)

	}

	return winRMClient
}

func (winRMClient *WinRMClient) setAgent(context MotadataMap) *WinRMClient {

	if context.Contains(consts.ObjectDiscoveryMethod) && context.GetStringValue(consts.ObjectDiscoveryMethod) == consts.ObjectDiscoveryMethodAgent {

		winRMClient.agent = true

	} else {

		winRMClient.agent = false
	}

	return winRMClient
}

func (winRMClient *WinRMClient) setTimeout(context MotadataMap) *WinRMClient {

	if context.Contains(consts.Timeout) {

		winRMClient.timeout = context.GetUINTValue(consts.Timeout)

	} else {

		winRMClient.timeout = MotadataUINT(60)
	}

	return winRMClient

}

func (winRMClient *WinRMClient) SetContext(context MotadataMap, host bool, logger *Logger) *WinRMClient {

	return winRMClient.setTarget(context, host).setPort(context).setUsername(context).setPassword(
		context).setAgent(context).setTimeout(context).setLogger(logger)

}

func (winRMClient *WinRMClient) ExecuteCommand(command MotadataString) MotadataString {

	output := consts.BlankString

	err := consts.BlankString

	if winRMClient.agent {

		cmd := exec.Command("powershell", "-command", "$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);"+command.ToString()+" | Format-List")

		var buffer bytes2.Buffer

		cmd.Stdout = &buffer

		err := cmd.Run()

		if len(buffer.String()) > 0 {

			output = ToString(buffer.String())
		}

		if err != nil {

			winRMClient.errors = append(winRMClient.errors, MotadataStringMap{
				consts.Error:     err.Error(),
				consts.ErrorCode: consts.ErrorCodeCommandExecutionFailed,
				consts.Message:   "failed to execute command [" + command.ToString() + "] on target " + winRMClient.target.ToString(),
			})

			winRMClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageCommandExecutionFailed, command, winRMClient.target, err.Error())))

		}

	} else {

		output, err, _, _ = winRMClient.client.RunWithString(Command.ReplaceAll("@@@", command).ToString(), consts.BlankString)

		if len(err) > 0 {

			winRMClient.errors = append(winRMClient.errors, MotadataStringMap{
				consts.Error:     err,
				consts.ErrorCode: consts.ErrorCodeCommandExecutionFailed,
				consts.Message:   "failed to execute command [" + command.ToString() + "] on target " + winRMClient.target.ToString(),
			})

			winRMClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageCommandExecutionFailed, command, winRMClient.target, err)))

		}
	}

	return MotadataString(output)
}

func (winRMClient *WinRMClient) Init() (result bool) {

	result = false

	if winRMClient.agent {

		result = true

	} else {

		errorCode := consts.ErrorCodeConnectionFailed

		errorMessage := fmt.Sprintf(consts.ErrorMessageConnectionFailed, "WinRM", winRMClient.target, winRMClient.port)

		var endpoint *winrm.Endpoint

		if regexp.MustCompile(consts.IPAddressRegexPattern).MatchString(winRMClient.target.ToString()) {

			endpoint = &winrm.Endpoint{Host: winRMClient.target.ToString(), Port: winRMClient.port.ToInt(), HTTPS: false, Insecure: false, CACert: nil, Cert: nil, Key: nil, Timeout: time.Duration(winRMClient.timeout) * time.Second}

		} else {

			endpoint = &winrm.Endpoint{Host: "[" + winRMClient.target.ToString() + "]", Port: winRMClient.port.ToInt(), HTTPS: false, Insecure: false, CACert: nil, Cert: nil, Key: nil, Timeout: time.Duration(winRMClient.timeout) * time.Second}
		}

		if winRMClient.userName.Contains("\\") {

			params := winrm.Parameters{TransportDecorator: func() winrm.Transporter { return &winrm.ClientNTLM{} }}

			winRMClient.client, _ = winrm.NewClientWithParameters(endpoint, winRMClient.userName.ToString(), winRMClient.password.ToString(), &params)

		} else {

			winRMClient.client, _ = winrm.NewClient(endpoint, winRMClient.userName.ToString(), winRMClient.password.ToString())

		}

		var err error

		winRMClient.shell, err = winRMClient.client.CreateShell()

		if winRMClient.shell != nil {

			result = true

			winRMClient.logger.Debug(MotadataString(fmt.Sprintf(consts.InfoMessageConnectionEstablished, winRMClient.target, winRMClient.port)))

		} else if err != nil {

			winRMClient.logger.Warn(MotadataString(fmt.Sprintf("error %v occurred for %v host...", err.Error(), winRMClient.target)))

			winRMClient.logger.Debug(MotadataString(fmt.Sprintf(consts.ErrorMessageConnectionFailed, "WinRM", winRMClient.target, winRMClient.port)))

			if MotadataString(err.Error()).Contains("connection refused") || MotadataString(err.Error()).Contains("invalid port") {

				errorCode = consts.ErrorCodeInvalidPort

				errorMessage = fmt.Sprintf(consts.ErrorMessageInvalidPort, winRMClient.port)

			} else if MotadataString(err.Error()).Contains("i/o timeout") {

				if IsOpened(winRMClient.port, winRMClient.target) {

					errorCode = consts.ErrorCodeTimeout

					errorMessage = fmt.Sprintf(consts.ErrorMessageConnectionTimeout, "WinRM", winRMClient.target, winRMClient.port)

				} else {

					errorCode = consts.ErrorCodeInvalidPort

					errorMessage = fmt.Sprintf(consts.ErrorMessageInvalidPort, winRMClient.port)

				}

			} else if MotadataString(err.Error()).Contains("http response error: 401") {

				errorCode = consts.ErrorCodeInvalidCredentials

				errorMessage = fmt.Sprintf(consts.ErrorMessageInvalidCredentials, winRMClient.target, winRMClient.port)

			} else {

				if !IsOpened(winRMClient.port, winRMClient.target) {

					errorCode = consts.ErrorCodeInvalidPort

					errorMessage = fmt.Sprintf(consts.ErrorMessageInvalidPort, winRMClient.port)

				}

			}

			winRMClient.logger.Warn(MotadataString(errorMessage))

			winRMClient.errors = append(winRMClient.errors, MotadataStringMap{
				consts.Error:     err.Error(),
				consts.ErrorCode: errorCode,
				consts.Message:   errorMessage,
			})

		}
	}

	return
}
