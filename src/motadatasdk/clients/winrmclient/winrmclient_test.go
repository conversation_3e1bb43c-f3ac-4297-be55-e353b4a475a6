/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package winrmclient

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

var (
	bytes []byte

	loggerObj = NewLogger("Test WinRMClient", consts.BlankString)
)

func TestMain(m *testing.M) {

	bytes, _ = os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(consts.CurrentDir)))) + consts.PathSeparator + consts.ConfigDirectory + consts.PathSeparator + "context.json")

	m.Run()
}

func TestWinRMClientInvalidHost(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{consts.Windows, "valid"})

	context[consts.Timeout] = float64(consts.DefaultTimeout)

	context[consts.ObjectIP] = consts.InvalidHost

	winRMClient.SetContext(context, false, &loggerObj)

	assertions.False(winRMClient.Init())

	for _, err := range winRMClient.GetErrors() {

		assertions.Equal(err[consts.ErrorCode], consts.ErrorCodeInvalidPort)

	}

	winRMClient.Destroy()
}

func TestWinRMClientInvalidPort(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{consts.Windows, "valid"})

	context[consts.Port] = consts.InvalidPort

	winRMClient.SetContext(context, false, &loggerObj)

	assertions.False(winRMClient.Init())

	for _, errs := range winRMClient.GetErrors() {

		assertions.Equal(errs[consts.ErrorCode], consts.ErrorCodeInvalidPort)

	}

	winRMClient.Destroy()

}

func TestWinRMClientInvalidUsername(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{consts.Windows, "valid"})

	context[consts.UserName] = consts.InvalidUserName

	winRMClient.SetContext(context, false, &loggerObj)

	assertions.False(winRMClient.Init())

	for _, err := range winRMClient.GetErrors() {

		assertions.Equal(err[consts.ErrorCode], consts.ErrorCodeInvalidCredentials)

	}
	winRMClient.Destroy()

}

func TestWinRMClient(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	winRMClient.SetContext(GetContext(contexts, MotadataStringList{consts.Windows, "valid"}), false, &loggerObj)

	assertions.True(winRMClient.Init())

	assertions.True(len(winRMClient.GetErrors()) == 0)

	winRMClient.Destroy()
}

func TestWinRMClientInvalidCommand(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	winRMClient.SetContext(GetContext(contexts, MotadataStringList{consts.Windows, "valid"}), false, &loggerObj)

	assertions.True(winRMClient.Init())

	assertions.True(len(winRMClient.GetErrors()) == 0)

	output := winRMClient.ExecuteCommand("Get-WmiObject Win32_Computershhystem")

	assertions.True(len(output) == 0)

	for _, err := range winRMClient.GetErrors() {

		assertions.Equal(err[consts.ErrorCode], consts.ErrorCodeCommandExecutionFailed)
	}

	winRMClient.Destroy()

}

func TestWinRMClientInvalidDomainName(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{"exchange", "cas", "2016"})

	context[consts.ObjectHost] = consts.InvalidHost

	context[consts.Timeout] = float64(consts.DefaultTimeout)

	winRMClient.SetContext(context, true, &loggerObj)

	assertions.False(winRMClient.Init())

	assertions.True(len(winRMClient.GetErrors()) == 1)

	for _, err := range winRMClient.GetErrors() {

		assertions.Equal(err[consts.ErrorCode], consts.ErrorCodeInvalidPort)
	}

	winRMClient.Destroy()

}

func TestWinRMClientInvalidPassword(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{"exchange", "cas", "2016.new"})

	context[consts.Password] = consts.InvalidPassword

	winRMClient.SetContext(context, false, &loggerObj)

	assertions.False(winRMClient.Init())

	assertions.True(len(winRMClient.GetErrors()) == 1)

	for _, err := range winRMClient.GetErrors() {

		assertions.Equal(err[consts.ErrorCode], consts.ErrorCodeInvalidCredentials)
	}

	winRMClient.Destroy()

}

func TestWinRMClientDomainNameVersion2016(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	winRMClient.SetContext(GetContext(contexts, MotadataStringList{"exchange", "cas", "2016"}), false, &loggerObj)

	assertions.True(winRMClient.Init())

	assertions.True(len(winRMClient.GetErrors()) == 0)

	winRMClient.Destroy()

}

func TestWinRMClientDomainUser(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	winRMClient.SetContext(GetContext(contexts, MotadataStringList{"windows.cluster", "valid.user.2012"}), false, &loggerObj)

	assertions.True(winRMClient.Init())

	assertions.True(len(winRMClient.GetErrors()) == 0)

	winRMClient.Destroy()

}

func TestWinRMClientAgent(t *testing.T) {

	if strings.Compare(consts.PathSeparator, consts.WindowsPathSeparator) != 0 {

		t.Skipf("Skipped due to non windows environment...")
	}

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{consts.Windows, "valid"})

	context[consts.ObjectDiscoveryMethod] = consts.ObjectDiscoveryMethodAgent

	winRMClient.SetContext(context, false, &loggerObj).setAgent(context)

	assertions.True(winRMClient.Init())

	assertions.True(len(winRMClient.GetErrors()) == 0)

	commandOutput := winRMClient.ExecuteCommand("Get-WmiObject Win32_Computersystem")

	assertions.NotNil(commandOutput)

	assertions.True(len(commandOutput) > 0)

	winRMClient.Destroy()
}

func TestWinRMClientDiscoverInvalidAgent(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	winRMClient.SetContext(GetContext(contexts, MotadataStringList{consts.Windows, "invalid.agent"}), false, &loggerObj)

	assertions.True(winRMClient.Init())

	assertions.True(winRMClient.IsAgent())

	output := winRMClient.ExecuteCommand("Get-WmiObject Win32_Computershhystem")

	assertions.True(len(output) == 0)

	assertions.NotEmpty(len(winRMClient.GetErrors()))

	assertions.True(len(winRMClient.GetErrors()) > 0)

	winRMClient.Destroy()
}

func TestWinRMClientDiscoverHost(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{consts.Windows, "valid.discovery"})

	winRMClient.SetContext(context, false, &loggerObj)

	result := winRMClient.Discover(context, false, &loggerObj)

	assertions.True(result.IsNotEmpty())

	assertions.True(result.Contains(consts.ObjectCredentialProfile) && result.GetMotadataStringValue(consts.ObjectCredentialProfile).IsNotEmpty())

	assertions.True(result.Contains(consts.CredentialProfileName) && result.GetMotadataStringValue(consts.CredentialProfileName).IsNotEmpty())

	context[consts.ObjectCredentialProfile] = result[consts.ObjectCredentialProfile]

	context[consts.CredentialProfileName] = result[consts.CredentialProfileName]

	output := winRMClient.ExecuteCommand("Get-WmiObject Win32_Computersystem | select-object Domain; echo \"hostname\";hostname")

	object := MotadataMap{consts.ObjectIP: context.GetStringValue(consts.ObjectIP)}

	BuildWinRMDiscoveryResult(context, object, output)

	assertions.Greater(len(object), 0)

	assertions.Equal(len(winRMClient.errors), 0)
}

func TestWinRMClientExecuteInlineCommand(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{consts.Windows, "valid.discovery"})

	winRMClient.SetContext(context, false, &loggerObj)

	result := winRMClient.Discover(context, false, &loggerObj)

	assertions.True(result.IsNotEmpty())

	context[consts.UserName] = winRMClient.userName

	context[consts.Password] = winRMClient.password

	hostName := GetHostName(winRMClient)

	assertions.Equal(len(winRMClient.errors), 0)

	assertions.Greater(len(hostName), 0)

	responses := make(chan MotadataMap)

	go ExecuteInlineCommand(context, "hostname", false, 0, responses, &loggerObj)

	command := ""

	for response := range responses {

		if response != nil && response.Contains(Index) {

			command = response.GetMotadataStringValue(Output).ToString()

		}
		close(responses)
	}

	assertions.Greater(len(command), 0)

	assertions.Equal(hostName.ToString(), command)
}

func TestWinRMClientExtractMetricValue(t *testing.T) {

	winRMClient := &WinRMClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{consts.Windows, "valid.2012"})

	winRMClient.SetContext(context, false, &loggerObj)

	assertions.True(winRMClient.Init())

	output := winRMClient.ExecuteCommand("(Get-Counter '\\Processor(*)\\% Processor Time','\\Processor(*)\\% User Time','\\Processor(*)\\% Interrupt Time','\\Processor(*)\\% Idle Time' -ErrorAction SilentlyContinue).countersamples | Format-List  -Property Path,Cookedvalue")

	assertions.True(output.IsNotEmpty())

	tokens := output.ReplaceAll("CookedValue :", consts.BlankString).Split(consts.NewLineRegexPattern)

	cores := make(MotadataMap)

	for index := range tokens {

		if tokens[index].HasPrefix("Path") && tokens[index].Contains("(") && tokens[index].Contains(")") {

			token := tokens[index].Split("(")[1].Split(")")[0]

			if !token.Contains("_total") {

				if tokens[index].Contains("processor time") {

					cores[token.ToString()] = make(MotadataMap)

					ExtractMetricValue(cores.GetMapValue(token.ToString()), tokens[index+1].TrimSpace(), tokens[index+2].TrimSpace(), consts.CPUCoreUsedPercent)

				}

			}

		}
	}

	assertions.True(cores.IsNotEmpty())
}
