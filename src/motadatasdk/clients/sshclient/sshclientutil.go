/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  26-May-2025     Sankalp             MOTADATA-6185 : Discovery support for IBM AS 400
 *  14-Jul-2025     Ashish             MOTADATA-6710 : Replace 'Program Files' with 'PROGRA~1' in path for SCP
 *  14-July-2025     Nikunj Patel      MOTADATA-6779 : Added support for dynamically deciding new line separator.
 */

package sshclient

import (
	"fmt"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"motadatasdk/utils"
	"regexp"
	"runtime"
	"strings"
)

var (
	uptimePattern1 = regexp.MustCompile(".\\s+\\w+\\s+(\\d+)\\s+day(s*),\\s+(\\d+):(\\d+).")

	uptimePattern2 = regexp.MustCompile(".\\s+\\w+\\s+(\\d+):(\\d+),.")

	uptimePattern3 = regexp.MustCompile(".\\s+\\w+\\s+(\\d+)\\s+day(s*),\\s(\\d+)\\s+min(s*),.")

	uptimePattern4 = regexp.MustCompile(".\\s+\\w+\\s+(\\d+)+\\s+hr(s*)+,.")

	uptimePattern5 = regexp.MustCompile(".\\s+\\w+\\s+(\\d+)+\\s+min(s*)+,.")

	spacePattern = regexp.MustCompile(`\s+`)

	junkPrompts = MotadataStringMap{
		"No Command": consts.BlankString,
		"LF":         consts.BlankString,
	}

	// CLIPromptCommandMappings - This may get change when we get new prompt while executing command
	// In the future, we may require dynamic approach to read the prompt
	promptCommandMappings = MotadataMap{
		"y followed by LF":          "y",
		"n followed by LF":          "n",
		"yes followed by LF":        "yes",
		"no followed by LF":         "no",
		"y":                         "y",
		"n":                         "n",
		"y followed by CR and LF":   "y\r",
		"yes followed by CR and LF": "yes\r",
		"CR and LF":                 "\r",
		"CR and Space":              "\r" + " ",
		"Space":                     " ",
		"LF followed by LF":         "",
		"yes":                       "yes",
		"no":                        "no",
	}
)

const (
	//All configuration command macros, need to replace with proper parameter values

	EnterMacro = "&[Enter]"

	FilePathMacro = "&[TransferFilePath]"

	TransferProtocolServerAddressMacro = "&[TransferProtocolServerAddress]"

	TransferProtocolServerUserMacro = "&[TransferProtocolServerUser]"

	TransferProtocolServerPasswordMacro = "&[TransferProtocolServerPassword]"

	TransferFileNameMacro = "&[TransferFileName]"

	ConfigModePasswordMacro = "&[ConfigModePassword]"

	VRFNameMacro = "&[VRFName]"

	LocalFileNameMacro = "&[LocalFileName]"

	FirmwareDeviceBackupFileName = "&[FirmwareDeviceBackupFileName]"

	FirmwareDeviceBackupFilePath = "&[FirmwareDeviceBackupFilePath]"

	ConfigFirmwareUpgradeTransferFileName = "&[ConfigFirmwareUpgradeTransferFileName]"
)

func SetupCleanupRoutine(sshClient *SSHClient, responses chan<- MotadataMap, response MotadataMap, requestType MotadataString) {

	if r := recover(); r != nil {

		err := make(MotadataStringMap)

		err[consts.Message] = fmt.Sprintf("%v", r)

		err[consts.ErrorCode] = consts.ErrorCodeInternalError

		err[consts.Error] = fmt.Sprintf("%v", r)

		response[consts.Errors] = append(response.GetStringMapSliceValue(consts.Errors), err)

		stackTraceBytes := make([]byte, consts.StackTraceSizeInBytes)

		PanicLogger.Fatal(MotadataString(fmt.Sprintf("serious error %v occurred in the plugin %v\nStack Trace ==> %v\n", r, RemoveSensitiveFields(response).ToJSON(), string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))))
	}

	sshClient.Destroy()

	if responses != nil {

		if requestType == consts.EventPoll {

			AddStatusField(response)
		}

		responses <- response

	}
}

func BuildLinuxUnixDiscoveryResult(result MotadataMap, ip interface{}, sshClient *SSHClient) {

	var objects []MotadataMap

	if output, valid := sshClient.ExecuteCommand("uname -a | awk '{print $1\"" + " " + "\"$2'}"); len(output) > 0 {

		if !(output.Contains("uname -a")) && valid && len(output.Strip().Split("\n")) == 1 {

			tokens := output.Strip().Split(consts.SpaceSeparator)

			result[consts.Status] = consts.StatusSucceed

			objects = append(objects, MotadataMap{consts.ObjectIP: ip})

			result[consts.Objects] = objects

			if len(tokens) > 1 {

				objects[0][consts.ObjectName] = tokens[1].Strip()

				if !result.Contains(consts.ObjectHost) {

					objects[0][consts.ObjectHost] = objects[0][consts.ObjectName]
				}

				objectType := tokens[0].Strip().ToLower()

				if objectType.Contains("sunos") {

					objects[0][consts.ObjectType] = consts.ObjectTypeSolaris

				} else if objectType.Contains("hp-ux") {

					objects[0][consts.ObjectType] = consts.ObjectTypeHPUX

				} else if objectType.Contains("aix") {

					objects[0][consts.ObjectType] = consts.ObjectTypeIBMAIX

				} else if objectType.Contains("os400") {

					objects[0][consts.ObjectType] = consts.ObjectTypeIBMAS400

				} else if objectType.Contains(consts.Linux) {

					objects[0][consts.ObjectType] = consts.ObjectTypeLinux

					objects[0][consts.ObjectContext] = MotadataMap{consts.SSBinPath: "/usr/sbin/ss"}

					if output, valid := sshClient.ExecuteCommand("cat /etc/*-release"); valid {

						output = output.Strip().ToLower()

						id := MotadataString(consts.BlankString)

						prettyName := MotadataString(consts.BlankString)

						homeURL := MotadataString(consts.BlankString)

						supportURL := MotadataString(consts.BlankString)

						if output.Contains("\nid=") {

							id = ExtractMetricValue(output, "\nid=")
						}

						if output.Contains("\npretty_name=") {

							prettyName = ExtractMetricValue(output, "\npretty_name=")
						}

						if output.Contains("\nhome_url=") {

							homeURL = ExtractMetricValue(output, "\nhome_url=")
						}

						if output.Contains("\nsupport_url=") {

							supportURL = ExtractMetricValue(output, "\nsupport_url=")
						}

						if id.Contains("ubuntu") && prettyName.Contains("ubuntu") &&
							homeURL.Contains("ubuntu") && supportURL.Contains("ubuntu") {

							ToMap(objects[0][consts.ObjectContext])[consts.SSBinPath] = "/bin/ss"
						}
					}
				}
			}

		} else {

			sshClient.errors = append(sshClient.errors, MotadataStringMap{
				consts.Error:     output.ToString(),
				consts.ErrorCode: consts.ErrorCodeInvalidLinuxORUnixHost,
				consts.Message:   "Connection Error: Unable to Connect to Linux/Unix Host.",
			})
		}
	}
}

func ExtractMetricValue(output, delimiter MotadataString) MotadataString {

	return output.SplitN(delimiter.ToString(), 2)[1].Strip().Split("\n")[0].Strip()
}

func GetProcessMetrics(context, processes MotadataMap, sshClient *SSHClient, logicalProcessors MotadataFloat64) MotadataString {

	processCommands := make(MotadataMap)

	processCommand := MotadataString(consts.BlankString)

	for _, object := range context.GetSliceValue(consts.Objects) {

		commandLine := MotadataString(ToMap(object).GetStringValue(consts.ObjectName)).Split(consts.Separator)

		processCommand = processCommand + commandLine[0] + consts.Separator

		if len(commandLine) > 1 {

			processCommands[ToMap(object).GetStringValue(consts.ObjectName)] = commandLine[1]

		} else {

			processCommands[ToMap(object).GetStringValue(consts.ObjectName)] = consts.BlankString

		}
	}

	if output, valid := sshClient.ExecuteCommand("ps -eo fname,pid,user,pcpu,pmem,vsz,rss,etime,nlwp,args | egrep -e '" + processCommand.Trim("|") + "'"); valid && len(output) > 0 {

		processCommand = setProcessMetrics(output.Split("\n"), processes, processCommands, sshClient, logicalProcessors)

	} else {

		processCommand = consts.BlankString
	}

	return processCommand

}

func setProcessMetrics(tokens []MotadataString, processes, processCommands MotadataMap, sshClient *SSHClient, logicalProcessors MotadataFloat64) MotadataString {

	processCommand := MotadataString(consts.BlankString)

	var commandBuilder strings.Builder

	for _, token := range tokens {

		tokens := MotadataString(spacePattern.ReplaceAllString(token.ToString(), consts.SpaceSeparator)).SplitN(consts.SpaceSeparator, 10)

		if len(tokens) > 9 {

			process := make(MotadataMap)

			value := tokens[0].ToString() + "|" + tokens[9].ToString()

			if processCommands[value] != nil {

				process[consts.Status] = consts.StatusUp

				process[consts.Process] = value

				process[consts.ProcessId] = tokens[1].ToINT()

				commandBuilder.Write([]byte("echo processID:" + tokens[1] + ";sudo lsof -p " + tokens[1] + " | wc -l;"))

				process[consts.ProcessUser] = tokens[2].TrimSpace()

				process[consts.ProcessCPUPercent] = tokens[3].ToFloat64() / logicalProcessors

				process[consts.ProcessMemoryPercent] = tokens[4].ToFloat64()

				process[consts.ProcessVirtualMemoryBytes] = MotadataKB(tokens[5].ToFloat64()).ToBytes()

				process[consts.ProcessMemoryUsedBytes] = MotadataKB(tokens[6].ToFloat64()).ToBytes()

				setProcessUptimeMetrics(process, tokens[7].TrimSpace())

				process[consts.ProcessThreads] = tokens[8].ToINT()

				if len(processCommands.GetStringValue(tokens[0].ToString()+"|"+tokens[9].ToString())) > 0 {

					if processCommands.GetStringValue(tokens[0].ToString()+"|"+tokens[9].ToString()) == tokens[9].ToString() {

						process[consts.ProcessCommand] = tokens[9].TrimSpace()
					}

				} else {

					process[consts.ProcessCommand] = tokens[9]
				}

			}

			if process.IsNotEmpty() {

				processCommand = processCommand + (process.GetMotadataStringValue(consts.ProcessId)) + "|"

				processes[(process.GetStringValue(consts.ProcessId))] = process

			}
		}
	}

	//command to get the process handles by pid
	output, valid := sshClient.ExecuteCommand(ToMotadataString(commandBuilder.String()))

	if valid {

		tokens := output.Split(consts.NewLineSeparator)

		processId := MotadataString("")

		for index := range tokens {

			if tokens[index].Contains("processID") && len(tokens[index].Split(consts.ColonSeparator)) >= 2 {

				processId = tokens[index].Split(consts.ColonSeparator)[1]
			}

			process := processes.GetMapValue(processId.ToString())

			if processId.IsNotEmpty() && tokens[index].IsDigit() && process != nil {

				if (tokens[index].ToINT() - 1) > 0 {

					process[consts.ProcessHandles] = tokens[index].ToINT() - 1

				} else {

					process[consts.ProcessHandles] = 0
				}
			}
		}
	}

	return processCommand.Trim("|")

}

func setProcessUptimeMetrics(process MotadataMap, token MotadataString) {

	days := MotadataINT(0)

	hours := MotadataINT(0)

	minutes := MotadataINT(0)

	seconds := MotadataINT(0)

	if token.Contains("-") {

		tokens := token.Split("-")

		if tokens != nil && len(token) > 0 {

			days = tokens[0].ToINT()

			if len(token) >= 2 {

				token = tokens[1]

			}

		}
	}

	tokens := token.Split(consts.ColonSeparator)

	if len(tokens) == 3 {

		hours = tokens[0].ToINT()

		minutes = tokens[1].ToINT()

		seconds = tokens[2].ToINT()

	} else if len(tokens) == 2 {

		minutes = tokens[0].ToINT()

		seconds = tokens[1].ToINT()

	} else if len(tokens) == 1 {

		seconds = tokens[0].ToINT()
	}

	process[consts.ProcessStartedTimeSeconds] = ((days*24+hours)*60+minutes)*60 + seconds

	process[consts.ProcessStartedTime] = MotadataTime(process.GetINTValue(consts.ProcessStartedTimeSeconds)).ToString()

}

func DiscoverProcesses(context MotadataMap, client *SSHClient) {

	if output, valid := client.ExecuteCommand("ps -eo fname,args | awk '{out=$1\" \"; for(i=2;i<=NF;i++){out=out$i\" \"}; print out}'"); valid {

		objects := MotadataMap{consts.Objects: []MotadataMap{}}

		context[consts.Status] = consts.StatusSucceed

		for _, token := range output.Strip().Split("\n") {

			if !token.ReplaceAll(consts.SpaceSeparator, consts.BlankString).Contains("COMMANDCOMMAND") {

				tokens := token.Strip().SplitN(consts.SpaceSeparator, 2)

				processName := MotadataString(consts.BlankString)

				processCommand := MotadataString(consts.BlankString)

				if len(tokens) == 1 {

					processName = tokens[0].Strip()

				} else if len(tokens) > 1 && !tokens[1].Strip().Contains("ps -eo fname,args") &&
					!tokens[1].Strip().Contains("awk {out=$1\" \"; for(i=2;i<=NF;i++){out=out$i\" \"}; print out}") {

					processName = tokens[0].Strip()

					processCommand = tokens[1].Strip()
				}

				if processName.IsNotEmpty() {
					utils.AddProcess(context, processName, processCommand, objects)
				}
			}
		}

		context[consts.Objects] = objects[consts.Objects]
	}
}

func SetProcessNetworkConnectionMetrics(metrics MotadataMap, tokens []MotadataString, ip MotadataString, processes MotadataMap, solaris bool) {

	connection := true

	localAddress := MotadataStringList{"127.0.0.1", "0.0.0.0", "*", "::1", "::", consts.LocalHost}

	port := MotadataString("any")

	var networkConnections []MotadataMap

	for _, token := range tokens {

		tokens := token.Split(consts.SpaceSeparator)

		processId := MotadataINT(0)

		separator := MotadataString(consts.BlankString)
		//BUG #24623 added len filter topic due to no filteration the data was having error [index out of bound]

		if len(tokens) > 2 {

			if solaris {

				processId = tokens[2].ToINT()

				separator = "."

			} else {

				processId = tokens[2].Split("/")[0].ToINT()

				separator = ":"

			}
		}

		if len(tokens) > 2 && processes[processId.ToNativeString()] != nil {

			networkConnection := make(MotadataMap)

			sourcePort := tokens[0].Split(separator.ToString())[len(tokens[0].Split(separator.ToString()))-1]

			sourceIp := tokens[0].Split(separator.ToString() + sourcePort.ToString())[0]

			destinationPort := tokens[1].Split(separator.ToString())[len(tokens[1].Split(separator.ToString()))-1]

			destinationIp := tokens[1].Split(separator.ToString() + destinationPort.ToString())[0]

			if localAddress.Contains(sourceIp) {

				networkConnection[consts.SourceIP] = ip

			} else {

				networkConnection[consts.SourceIP] = sourceIp
			}

			if localAddress.Contains(destinationIp) {

				networkConnection[consts.DestinationIP] = ip

			} else {

				networkConnection[consts.DestinationIP] = destinationIp
			}

			if sourcePort == port {

				networkConnection[consts.SourcePort] = port

			} else {

				networkConnection[consts.SourcePort] = sourcePort

			}

			if destinationPort == port {

				networkConnection[consts.DestinationPort] = port

			} else {

				networkConnection[consts.DestinationPort] = destinationPort

			}

			networkConnection[consts.Process] = processes.GetMapValue(processId.ToNativeString())[consts.Process]

			if connection {

				connection = false

				networkConnections = append(networkConnections, networkConnection)
			}
		}
	}

	if IsNotEmptyMapSlice(networkConnections) {

		metrics[consts.NetworkConnection] = networkConnections
	}

}

func SetUptimeMetrics(token MotadataString, metrics MotadataMap) MotadataMap {

	if token.IsNotEmpty() {

		if uptimePattern1.MatchString(token.ToString()) {

			matches := uptimePattern1.FindAllStringSubmatch(token.ToString(), -1)[0]

			if len(matches) > 3 {

				metrics[consts.StartedTimeSeconds] = MotadataString(matches[1]).Split("\\s+")[0].ToINT()*24*60*60 + MotadataString(matches[3]).Split("\\s+")[0].ToINT()*60*60 + MotadataString(matches[4]).Split("\\s+")[0].ToINT()*60

				if metrics[consts.StartedTimeSeconds] != nil {

					metrics[consts.StartedTime] = MotadataTime(metrics.GetINTValue(consts.StartedTimeSeconds)).ToString()
				}
			}

		} else {

			if uptimePattern2.MatchString(token.ToString()) {

				matches := uptimePattern2.FindAllStringSubmatch(token.ToString(), -1)[0]

				if len(matches) > 2 {

					metrics[consts.StartedTimeSeconds] = MotadataString(matches[1]).Split("\\s+")[0].ToINT()*60*60 + MotadataString(matches[2]).Split("\\s+")[0].ToINT()*60

					if metrics[consts.StartedTimeSeconds] != nil {

						metrics[consts.StartedTime] = MotadataTime(metrics.GetINTValue(consts.StartedTimeSeconds)).ToString()

					}

				}

			} else {

				if uptimePattern3.MatchString(token.ToString()) {

					matches := uptimePattern3.FindAllStringSubmatch(token.ToString(), -1)[0]

					if len(matches) > 2 {

						metrics[consts.StartedTimeSeconds] = MotadataString(matches[1]).Split("\\s+")[0].ToINT()*24*60*60 + MotadataString(matches[3]).Split("\\s+")[0].ToINT()*60

						if metrics[consts.StartedTimeSeconds] != nil {

							metrics[consts.StartedTime] = MotadataTime(metrics.GetINTValue(consts.StartedTimeSeconds)).ToString()

						}
					}
				} else {

					if uptimePattern4.MatchString(token.ToString()) {

						matches := uptimePattern4.FindAllStringSubmatch(token.ToString(), -1)[0]

						if len(matches) > 0 {

							metrics[consts.StartedTimeSeconds] = MotadataString(matches[1]).Split("\\s+")[0].ToINT() * 60 * 60

							if metrics[consts.StartedTimeSeconds] != nil {

								metrics[consts.StartedTime] = MotadataTime(metrics.GetINTValue(consts.StartedTimeSeconds)).ToString()
							}
						}

					} else {

						if uptimePattern5.MatchString(token.ToString()) {

							matches := uptimePattern5.FindAllStringSubmatch(token.ToString(), -1)[0]

							if len(matches) > 0 {

								metrics[consts.StartedTimeSeconds] = MotadataString(matches[1]).Split("\\s+")[0].ToINT() * 60

								if metrics[consts.StartedTimeSeconds] != nil {

									metrics[consts.StartedTime] = MotadataTime(metrics.GetINTValue(consts.StartedTimeSeconds)).ToString()

								}
							}
						}
					}
				}
			}
		}
	}

	return metrics
}

func ValidateMetricValue(output, delimiter1, delimiter2 MotadataString) (result MotadataString, valid bool) {

	result = output.SplitWithEmptyEntries(delimiter1.ToString())[0].SplitWithEmptyEntries(delimiter2.ToString())[1].Strip()

	if result.IsNotEmpty() && !result.Contains("command not found") {

		valid = true
	}

	return
}

func (sshClient *SSHClient) Discover(context MotadataMap, logger *Logger) (result MotadataMap) {

	result = make(MotadataMap)

	var credentialProfiles []interface{}

	credentialProfiles = context.GetSliceValue(consts.DiscoveryCredentialProfiles)

	if len(credentialProfiles) > 0 {

		for _, credentialProfile := range credentialProfiles {

			profile := ToMap(credentialProfile)

			profile[consts.ObjectIP] = context[consts.ObjectIP]

			profile[consts.Port] = context[consts.Port]

			profile[consts.Timeout] = sshClient.setTimeout(context).timeout

			sshClient.SetContext(profile, logger)

			if sshClient.Init() {

				result[consts.ObjectCredentialProfile] = profile[consts.Id]

				result[consts.CredentialProfileName] = profile[consts.CredentialProfileName]

				break
			}
		}

	} else {

		sshClient.SetContext(context, logger)

	}

	return
}

func GetFilePermissionMode(value MotadataString) (fileMode MotadataString) {

	if value == "rwx" {

		fileMode = "read write execute"

	} else if value == "rw" {

		fileMode = "read write"

	} else if value == "w" {

		fileMode = "write"

	} else if value == "r" {

		fileMode = "read"

	} else if value == "rx" {

		fileMode = "read execute"
	}

	return
}

func GetMetricValue(index int, indexDiff int, tokens MotadataStringList) MotadataFloat64 {

	if index != -1 && len(tokens) > (index+indexDiff) && MotadataString(tokens[index+indexDiff]).IsDigit() {

		return MotadataString(tokens[index+indexDiff]).ToFloat64()
	}

	return -1
}

// ExecuteCLICommandGroups
/* Method will execute all the command provided in the template
// Changed method to generate prompt dynamically using index like EXOS-VM.1, EXOS-VM.2, EXOS-VM.3
*/
func ExecuteCLICommandGroups(sshClient *SSHClient, commandGroups []interface{}, context MotadataMap, fileName, request, backupEndPrompt MotadataString, backupResult MotadataMap, delayTime MotadataUINT) bool {

	sshClient.logger.Debug(MotadataString(fmt.Sprintf("Default Template delay time : %d", delayTime)))

	for index, group := range commandGroups {

		commandGroup := ToMap(group)

		prompt := backupEndPrompt

		if context.Contains(consts.PromptIndex) && context.GetIntValue(consts.PromptIndex) > 0 {

			prompt = MotadataString(fmt.Sprintf("%s%s%d", backupEndPrompt, ".", context.GetIntValue(consts.PromptIndex)+(index+1)))
		}

		result, executed := sshClient.executeCLICommandGroup(commandGroup, context, context.GetStringValue(consts.FileTransferProtocol), fileName, request, prompt, delayTime)

		if valid, value := sshClient.valid(request.ToString(), result, commandGroup, backupResult, context, executed); valid {

			return value
		}
	}

	return true
}

// ExecuteNetworkCommand
/* Method will execute single command with given command timeout and delay time
- In case of direct backup we will pass backup end prompt as an expected pattern to handle the
  scenario when file content it's self contain prompt
- For the direct backup, we are extracting result to exclude command which we have entered and prompt.
- Sending prompt command to enter user input
*/
func (sshClient *SSHClient) executeCLICommandGroup(commandGroup, context MotadataMap, protocol string, file, request, backupEndPrompt MotadataString, delayTime MotadataUINT) (result MotadataString, qualified bool) {

	sshClient.printOutput = true

	if protocol == consts.FileTransferProtocolDirectCopy && commandGroup.GetStringValue(consts.ConfigTemplateOperationCommandResponseRequired) == consts.Yes {

		sshClient.printOutput = false
	}

	qualified = true

	command := commandGroup.GetMotadataStringValue(consts.ConfigTemplateOperationCommand)

	timeout := commandGroup.GetUINTValue(consts.ConfigTemplateOperationCommandTimeout)

	prompt := commandGroup.GetMotadataStringValue(consts.ConfigTemplateOperationPrompt)

	if commandGroup.GetMotadataStringValue(consts.ConfigTemplateOperationCommandResponseRequired) == consts.Yes {

		prompt = backupEndPrompt

	}

	promptCommand := commandGroup.GetMotadataStringValue(consts.ConfigTemplateOperationPromptCommand)

	command = sshClient.replacePlaceholders(command, file, context, protocol)

	result, err := sshClient.SendCommand(command, prompt, delayTime, timeout)

	if err != nil {

		context[consts.Errors] = append(context.GetStringMapSliceValue(consts.Errors), MotadataStringMap{
			consts.Message:   err.Error(),
			consts.ErrorCode: consts.ErrorCodeFailExecutingCommand,
			consts.Error:     fmt.Sprintf(consts.ErrorMessageConfigOutputError, command.ReplaceAll(sshClient.GetNewLineSeparator(), consts.BlankString), sshClient.target, result),
		})

		sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageCommandExecutionFail, request, context.GetStringValue(consts.ObjectIP))))

		qualified = false

	} else {

		if protocol == consts.FileTransferProtocolDirectCopy && commandGroup.GetStringValue(consts.ConfigTemplateOperationCommandResponseRequired) == consts.Yes {

			sshClient.logger.Info(MotadataString(fmt.Sprintf(consts.InfoMessageExtractingBackupResult, sshClient.target)))

			result = getCLICommandResult(result, command, backupEndPrompt, commandGroup)

		}

		//execute prompt command

		if promptCommand.IsNotEmpty() && !junkPrompts.Contains(promptCommand.ToString()) {

			promptCommand = replacePlaceholder(promptCommand)

			_, err := sshClient.SendCommand(promptCommand, context.GetMotadataStringValue(consts.Prompt), consts.DefaultCommandDelayTime, timeout)

			if err != nil {

				context[consts.Errors] = append(context.GetStringMapSliceValue(consts.Errors), MotadataStringMap{
					consts.Message:   err.Error(),
					consts.ErrorCode: consts.ErrorCodeFailExecutingCommand,
					consts.Error:     fmt.Sprintf(consts.ErrorMessageConfigOutputPromptCommandError, promptCommand.ReplaceAll(sshClient.GetNewLineSeparator(), consts.BlankString), sshClient.target, result),
				})

				qualified = false

				sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessagePromptCommandExecutionFail, request, context.GetStringValue(consts.ObjectIP))))
			}
		}

		// Added code to have generic code to fetch the value using regex.
		// It will be used in firmware as well as in Hardware where we require to fetch the details using regex.

		resultPattern := ""

		if commandGroup.Contains(consts.Attributes) && len(commandGroup.GetSliceValue(consts.Attributes)) > 0 {

			for _, attribute := range commandGroup.GetSliceValue(consts.Attributes) {

				attribute := ToMap(attribute)

				re, err := regexp.Compile(attribute.GetStringValue(consts.ResultPattern))

				if err != nil {

					qualified = false

					context[consts.Errors] = append(context.GetStringMapSliceValue(consts.Errors), MotadataStringMap{
						consts.Error:     fmt.Sprintf(consts.ErrorMessageCompilingRegex, request, commandGroup.GetStringValue(consts.ResultPattern), consts.BlankString),
						consts.Message:   err.Error(),
						consts.ErrorCode: consts.ErrorCodeUnableToCompileRegex,
					})

					sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageCompilingRegex, request, commandGroup.GetStringValue(consts.ResultPattern), context.GetStringValue(consts.ObjectIP))))

				} else {

					res := re.FindStringSubmatch(result.ToString())

					if res == nil || len(res) <= 0 {

						qualified = false

						context[consts.Errors] = append(context.GetStringMapSliceValue(consts.Errors), MotadataStringMap{
							consts.Error:     fmt.Sprintf(consts.ErrorUnableToFindResultPattern, commandGroup.GetStringValue(consts.ResultPattern), request),
							consts.Message:   fmt.Sprintf(consts.ErrorUnableToFindResultPattern, commandGroup.GetStringValue(consts.ResultPattern), request),
							consts.ErrorCode: consts.ErrorCodeResultPatternValueNotFound,
						})

						sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageUnableToFindResultPattern, commandGroup.GetStringValue(consts.ResultPattern), request, context.GetStringValue(consts.ObjectIP))))
					} else {

						resultPattern = res[len(res)-1]

						sshClient.attributes[attribute.GetStringValue(consts.AttributeName)] = res[len(res)-1]

						sshClient.logger.Info(MotadataString(fmt.Sprintf(consts.InfoMessageResultPatternResult, resultPattern, request, context.GetStringValue(consts.ObjectIP))))

						if attribute.Contains(consts.ExpectedValue) && attribute.GetMotadataStringValue(consts.ExpectedValue).IsNotEmpty() && resultPattern != attribute.GetStringValue(consts.ExpectedValue) {

							qualified = false

							context[consts.Errors] = append(context.GetStringMapSliceValue(consts.Errors), MotadataStringMap{
								consts.Error:     fmt.Sprintf(consts.ErrorExpectedValueDoesNotMatchResultPattern, commandGroup.GetStringValue(consts.ExpectedValue), resultPattern, request),
								consts.Message:   fmt.Sprintf(consts.ErrorExpectedValueDoesNotMatchResultPattern, commandGroup.GetStringValue(consts.ExpectedValue), resultPattern, request),
								consts.ErrorCode: consts.ErrorCodeExpectedValueIsNotEqualToResultPattern,
							})

							sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageExpectedValueDoesNotMatchResultPattern, commandGroup.GetStringValue(consts.ExpectedValue), resultPattern, request, context.GetStringValue(consts.ObjectIP))))

							return result, qualified
						}
					}
				}
			}
		}

		if qualified {

			sshClient.logger.Debug(MotadataString(fmt.Sprintf(consts.InfoMessageExecutedCommandMessage, command.ReplaceAll(consts.NewLineSeparator, consts.BlankString), context.GetStringValue(consts.ObjectIP))))
		}
	}

	return result, qualified
}

// ExtractBackupResult
/* Method is extracting result to exclude command which we have entered and prompt to not be the part of file content in
 case of direct protocol
- This method will get enhanced in future as per the user feedback ( i.e. backup file start and end pattern)
*/
func getCLICommandResult(result, command, endPrompt MotadataString, commandGroup MotadataMap) MotadataString {

	result = result.ReplaceAll(command, consts.BlankString) // this will remove command from result

	result = result.ReplaceAll(endPrompt, consts.BlankString) // this will remove endPrompt from result

	// This code will change in future based on user requirement.
	prompt := commandGroup.GetMotadataStringValue(consts.ConfigTemplateOperationBackupStartPrompt)

	if prompt.IsNotEmpty() {

		if result.Contains(prompt.ToString()) {

			index := strings.Index(result.ToString(), prompt.ToString())

			if index != -1 {

				result = result[index+1:]

			}
		}

	}

	result.TrimSpace()

	return result
}

// replacePlaceholders
/* Method will replace the macros with its appropriate value in the command
 */
func (sshClient *SSHClient) replacePlaceholders(command, file MotadataString, context MotadataMap, protocol string) MotadataString {

	formattedCommand := command.ReplaceAll(EnterMacro, sshClient.GetNewLineSeparator())

	formattedCommand = formattedCommand.ReplaceAll(ConfigModePasswordMacro, context.GetMotadataStringValue(consts.ConfigPassword))

	formattedCommand = formattedCommand.ReplaceAll(VRFNameMacro, context.GetMotadataStringValue(consts.VRFName))

	formattedCommand = formattedCommand.ReplaceAll(FilePathMacro, consts.ConfigManageDirectory)

	if protocol == consts.FileTransferProtocolSCPSFTP {
		path := replaceWithShortPath(consts.CurrentDir + consts.PathSeparator + consts.ConfigManageDirectory + consts.PathSeparator + file.ToString())
		formattedCommand = formattedCommand.ReplaceAll(TransferFileNameMacro, MotadataString(path))

	} else {

		formattedCommand = formattedCommand.ReplaceAll(TransferFileNameMacro, file)
	}

	formattedCommand = formattedCommand.ReplaceAll(LocalFileNameMacro, file)

	formattedCommand = formattedCommand.ReplaceAll(TransferProtocolServerAddressMacro, context.GetMotadataStringValue(consts.FileServerHost))

	formattedCommand = formattedCommand.ReplaceAll(TransferProtocolServerUserMacro, context.GetMotadataStringValue(consts.FileServerUserName))

	formattedCommand = formattedCommand.ReplaceAll(TransferProtocolServerPasswordMacro, context.GetMotadataStringValue(consts.FileServerPassword))

	formattedCommand = formattedCommand.ReplaceAll(FirmwareDeviceBackupFileName, MotadataString(sshClient.attributes.GetStringValue(consts.FirmwareDeviceBackupFileName)))

	formattedCommand = formattedCommand.ReplaceAll(FirmwareDeviceBackupFilePath, MotadataString(consts.PathSeparator+consts.FirmwareUpgradeImageFiles+consts.PathSeparator+sshClient.attributes.GetStringValue(consts.FirmwareDeviceBackupFileName)))

	formattedCommand = formattedCommand.ReplaceAll(ConfigFirmwareUpgradeTransferFileName, MotadataString(consts.PathSeparator+consts.FirmwareUpgradeImageFiles+consts.PathSeparator+context.GetStringValue(consts.FirmwareFileName)))

	return formattedCommand
}

// Method will replace 'Program Files' with its short version in case of windows
// and also replace '\\' with '/' to do scp in windows.
func replaceWithShortPath(path string) string {
	return strings.ReplaceAll(strings.ReplaceAll(path, "Program Files", "PROGRA~1"), "\\", "/")
}

// replacePlaceholder
// Method will replace the prompt command which user have selected in the template with its value
func replacePlaceholder(command MotadataString) MotadataString {

	if promptCommandMappings.Contains(command.ToString()) {

		command = promptCommandMappings.GetMotadataStringValue(command.ToString())
	}

	return command
}

/*
*
Method will be used to validate the relative operations and then decide whether to continue the loop or not ?
If true then it will break the loop with return value present in the second argument
*/
func (sshClient *SSHClient) valid(operation string, result MotadataString, commandGroup, backupResult, context MotadataMap, executed bool) (bool, bool) {

	groups := MotadataMap{}

	if context.Contains(consts.CommandGroups) {

		groups = context.GetMapValue(consts.CommandGroups)
	}

	if operation == consts.Reboot && !executed {

		return false, false
	} else if !executed && operation == consts.GetConfigurationRegisterInfo && groups.Contains(consts.UpdateConfigurationRegister) {

		// if "Get Configuration Register Info" operation is failed and user have added "Update Configuration Register" operation then we have to continue execution.
		return true, true

	} else if executed && operation == consts.GetFreeSpace && sshClient.attributes.Contains(consts.FirmwareFreeSpace) && sshClient.attributes.GetIntValue(consts.FirmwareFreeSpace) <= context.GetIntValue(consts.FirmwareUpgradeImageFileSize) { // if available space is less than upload file size then fail the operation.

		context[consts.Errors] = append(context.GetStringMapSliceValue(consts.Errors), MotadataStringMap{
			consts.Message:   consts.ErrorMessageInsufficientSpace,
			consts.ErrorCode: consts.ErrorCodeFailExecutingCommand,
			consts.Error:     fmt.Sprintf(consts.ErrorAvailableSpaceWithFileSize, sshClient.attributes.GetIntValue(consts.FirmwareFreeSpace), context.GetIntValue(consts.FirmwareUpgradeImageFileSize)),
		})

		sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageAvailableSpaceWithFileSize, sshClient.attributes.GetIntValue(consts.FirmwareFreeSpace), context.GetIntValue(consts.FirmwareUpgradeImageFileSize), context.GetStringValue(consts.ObjectIP))))

		return true, false

	} else if executed && commandGroup.GetStringValue(consts.ConfigTemplateOperationCommandResponseRequired) == consts.Yes {

		if context.GetStringValue(consts.FileTransferProtocol) == consts.FileTransferProtocolDirectCopy {

			backupResult[operation+consts.BackupFileContent] = result
		}

	} else if !executed && operation == consts.OperationTypeInfo { // in case of "info" operation, if all the attributes are failed then still we have allow other operation to be performed.

		return false, true

	} else if !executed {

		return true, false
	}

	return false, false
}
