/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*   Change Logs:
 *   Date             Author            Notes
 *   3-Mar-2025       <PERSON><PERSON>      MOTADATA-5172 : Changes Info logs to Debug logs.
 *	 3-Mar-2025       <PERSON><PERSON>      MOTADATA-5199 : Handle yes/no prompts with "yes" response and prioritize Keyboard Interactive auth over Password.
 *	 4-Apr-2025       Harsh Mehta       MOTADATA-5656 : Added new ciphers and MACs to support latest version Cisco devices.
 *   14-July-2025     <PERSON><PERSON><PERSON>      MOTADATA-6779 : Added support for dynamically deciding new line separator.
 */

package sshclient

import (
	"errors"
	"fmt"
	"golang.org/x/crypto/ssh"
	"io"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"net"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"time"
)

const (
	sshKey = "ssh.key"

	passphrase = "passphrase"
)

type SSHClient struct {
	client *ssh.Client

	session *ssh.Session

	proxyClient *ssh.Client

	proxyServer MotadataString

	proxyPort MotadataUINT16

	proxyUserName MotadataString

	proxyPassword MotadataString

	terminalRequired bool

	target MotadataString

	port MotadataUINT16

	agent bool

	userName MotadataString

	password MotadataString

	key MotadataString

	passphrase MotadataString

	timeout MotadataUINT

	errors []MotadataStringMap

	logger *Logger

	reader io.Reader

	writer io.WriteCloser

	logs MotadataMap

	output MotadataString

	printOutput bool

	attributes MotadataMap

	newLineSeparator MotadataString
}

// Ciphers supported by ssh server. Can be check using "ssh -Q cipher"
var ciphers = []string{
	"aes256-ctr",
	"aes128-ctr",
	"aes128-cbc",
	"3des-cbc",
	"aes192-ctr",
	"aes192-cbc",
	"aes256-cbc",
	"<EMAIL>",
	"<EMAIL>",
	"<EMAIL>",
	"aes128-gcm",
	"aes256-gcm"}

var hostKeyAlgorithms = []string{
	ssh.CertAlgoRSASHA256v01,
	ssh.CertAlgoRSASHA512v01,
	ssh.CertAlgoRSAv01,
	ssh.CertAlgoDSAv01,
	ssh.CertAlgoECDSA256v01,
	ssh.CertAlgoECDSA384v01,
	ssh.CertAlgoECDSA521v01,
	ssh.CertAlgoED25519v01,
	ssh.KeyAlgoECDSA256,
	ssh.KeyAlgoECDSA384,
	ssh.KeyAlgoECDSA521,
	ssh.KeyAlgoRSA,
	ssh.KeyAlgoDSA,
	ssh.KeyAlgoED25519,
	ssh.KeyAlgoRSASHA256,
	ssh.KeyAlgoRSASHA512,
	"x509v3-ssh-rsa",
}

var junkCharPattern, _ = regexp.Compile(consts.ASCIIJunkCharacterPattern)

// ConfigCLIErrors - All the errors which may occur at the time of command execution.
// This may get change when we get new errors while executing command
// In the future, we may require dynamic approach to read the errors
var configCLIErrors = []string{"Bad secrets", "syntax error", "Incomplete command", "Invalid input",
	"Error opening", "unknown command", "Error opening tftp", "Unrecognized command", "statement ignored",
	"Error writing", "Error verifying", "Error copying", "Error deleting",
	"Unrecognized command", "System reserved storage reached", "The source file does not exist",
	"File/Directory  does not exist", "Invalid command", "Invalid ip address", "Error parsing filename",
	"timed out", "Timed out", "Error in command", "Error computing MD5 hash", "No such file or directory",
	"TCP connection to device failed", "Got Error while reading output", "Timeout while reading output for command",
	"Command fail", "Undefined error", "Unknown action", "Error: Failed to connect to the remote host", "Unknown command or computer name",
	"unable to find computer address", "Command not found", "Invalid Command", "Configuration backup fail", "Configuration restore fail",
	"Please choose 'YES' or 'NO' first before pressing 'Enter'", "No route to host", "Permission denied", "Non-volatile configuration memory is not present",
	"Connection refused", "execution failed"}

var kexAlgorithms = []string{"diffie-hellman-group1-sha1",
	"ecdh-sha2-nistp256",
	"ecdh-sha2-nistp384",
	"ecdh-sha2-nistp521",
	"<EMAIL>"} // add key exchange algorithm support externally

var macAlgorithms = []string{
	"hmac-sha2-256",
	"hmac-sha2-512",
	"<EMAIL>",
	"<EMAIL>",
}

func (sshClient *SSHClient) GetErrors() []MotadataStringMap {

	return sshClient.errors
}

func (sshClient *SSHClient) SetContext(context MotadataMap, logger *Logger) *SSHClient {

	return sshClient.setTarget(context).setTerminalRequired(context).setPort(context).setUsername(context).setPassword(context).
		setAgent(context).setProxyServer(context).setProxyPassword(context).setProxyUsername(context).setProxyPort(context).
		setTimeout(context).setSSHKey(context).setPassphrase(context).setLogger(logger).setWorklogs(context).SetPrintOutput(true).setAttributes().
		setNewLineSeparator(context)
}

func (sshClient *SSHClient) Init() (result bool) {

	result = false

	if sshClient.logger == nil {

		logger := NewLogger("SDK/SSH", consts.BlankString)

		sshClient.logger = &logger
	}

	if sshClient.agent {

		result = true

	} else {

		config := &ssh.ClientConfig{
			Timeout:           time.Duration(sshClient.timeout.ToInt()) * time.Second,
			User:              ToString(sshClient.userName),
			HostKeyCallback:   ssh.InsecureIgnoreHostKey(),
			HostKeyAlgorithms: hostKeyAlgorithms,
		}

		config.SetDefaults()

		config.Config.KeyExchanges = append(config.Config.KeyExchanges, kexAlgorithms...)

		config.Ciphers = append(config.Ciphers, ciphers...)

		config.MACs = append(config.MACs, macAlgorithms...) // Add MAC algorithms

		flag := true

		if sshClient.key.IsNotEmpty() {

			var signer ssh.Signer

			var err error

			if sshClient.passphrase.IsNotEmpty() {

				signer, err = ssh.ParsePrivateKeyWithPassphrase([]byte(sshClient.key), []byte(sshClient.passphrase))

			} else {

				signer, err = ssh.ParsePrivateKey([]byte(sshClient.key))
			}

			config.Auth = []ssh.AuthMethod{ssh.PublicKeys(signer)}

			if err != nil {

				if MotadataString(err.Error()).HasSuffix("decryption password incorrect") {

					sshClient.errors = append(sshClient.errors, MotadataStringMap{
						consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidCredentials, sshClient.target, sshClient.port),
						consts.ErrorCode: consts.ErrorCodeInvalidCredentials,
						consts.Error:     err.Error(),
					})

				} else {

					sshClient.errors = append(sshClient.errors, MotadataStringMap{
						consts.Message:   "Invalid public/private SSH key",
						consts.ErrorCode: consts.ErrorCodeInvalidPublicOrPrivateSSHKey,
						consts.Error:     err.Error(),
					})
				}

				flag = false
			}

		} else {

			config.Auth = []ssh.AuthMethod{ssh.Password(ToString(sshClient.password))}
		}

		if flag {

			var err error

			sshClient.client, err = ssh.Dial("tcp", net.JoinHostPort(string(sshClient.target), strconv.Itoa(int(sshClient.port))), config)

			if err != nil {

				sshClient.logger.Warn(MotadataString(fmt.Sprintf("error %v occurred for %v host...", err.Error(), sshClient.target)))

				if MotadataString(err.Error()).HasSuffix("i/o timeout") {

					if IsOpened(sshClient.port, sshClient.target) {

						sshClient.errors = append(sshClient.errors, MotadataStringMap{
							consts.Message:   fmt.Sprintf(consts.ErrorMessageConnectionTimeout, "SSH", sshClient.target, sshClient.port),
							consts.ErrorCode: consts.ErrorCodeTimeout,
							consts.Error:     err.Error(),
						})

					} else {

						sshClient.errors = append(sshClient.errors, MotadataStringMap{
							consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidPort, sshClient.port),
							consts.ErrorCode: consts.ErrorCodeInvalidPort,
							consts.Error:     err.Error(),
						})
					}

				} else if MotadataString(err.Error()).HasSuffix("connection refused") {

					sshClient.errors = append(sshClient.errors, MotadataStringMap{
						consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidPort, sshClient.port),
						consts.ErrorCode: consts.ErrorCodeInvalidPort,
						consts.Error:     err.Error(),
					})

				} else if MotadataString(err.Error()).Contains("unable to authenticate") {

					sshClient.errors = append(sshClient.errors, MotadataStringMap{
						consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidCredentials, sshClient.target, sshClient.port),
						consts.ErrorCode: consts.ErrorCodeInvalidCredentials,
						consts.Error:     err.Error(),
					})

				} else {

					if IsOpened(sshClient.port, sshClient.target) {

						sshClient.errors = append(sshClient.errors, MotadataStringMap{
							consts.Message:   fmt.Sprintf(consts.ErrorMessageConnectionFailed, "SSH", sshClient.target, sshClient.port),
							consts.ErrorCode: consts.ErrorCodeConnectionFailed,
							consts.Error:     err.Error(),
						})
					} else {

						sshClient.errors = append(sshClient.errors, MotadataStringMap{
							consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidPort, sshClient.port),
							consts.ErrorCode: consts.ErrorCodeInvalidPort,
							consts.Error:     err.Error(),
						})
					}

				}

			} else {

				result = true
			}
		}
	}

	return
}

func (sshClient *SSHClient) ExecuteCommand(command MotadataString, ignoreErrors ...bool) (output MotadataString, valid bool) {

	output = consts.BlankString

	var stdout []byte

	var stderr error

	if sshClient.agent {

		stdout, stderr = exec.Command("sh", "-c", command.ToString()).Output()

	} else {

		var err error

		sshClient.session, err = sshClient.client.NewSession()

		if err != nil {

			sshClient.errors = append(sshClient.errors, MotadataStringMap{
				consts.Message:   err.Error(),
				consts.ErrorCode: consts.ErrorCodeInternalError,
				consts.Error:     err.Error(),
			})
		}

		stdout, stderr = sshClient.session.CombinedOutput(command.ToString())

		if sshClient.session != nil {

			_ = sshClient.session.Close()
		}
	}

	if stderr != nil {

		sshClient.errors = append(sshClient.errors, MotadataStringMap{
			consts.Message:   "failed to execute command [" + ToString(command) + "] on target " + ToString(sshClient.target),
			consts.ErrorCode: consts.ErrorCodeCommandExecutionFailed,
			consts.Error:     stderr.Error(),
		})

	} else if stdout != nil {

		output = MotadataString(stdout)
	}

	//In case of ignoreError is true we will consider output.It's corner case where we need error and output both(To ignore particular error later)
	if len(ignoreErrors) > 0 && ignoreErrors[0] == true && stdout != nil {
		output = MotadataString(stdout)
	}

	if len(stdout) > 0 && !MotadataString(stdout).Contains("command not found") {

		valid = true
	}

	return
}

func (sshClient *SSHClient) Destroy() {

	if sshClient.client != nil {

		_ = sshClient.client.Close()

		sshClient.logger.Debug(MotadataString(fmt.Sprintf(consts.InfoMessageConnectionDestroyed, sshClient.target, (sshClient.port).ToString())))

	}
}

func (sshClient *SSHClient) setTarget(context MotadataMap) *SSHClient {

	if context.Contains(consts.ObjectIP) {

		sshClient.target = context.GetMotadataStringValue(consts.ObjectIP)

	} else {

		sshClient.target = consts.LocalHost
	}

	return sshClient
}

func (sshClient *SSHClient) setTerminalRequired(context MotadataMap) *SSHClient {

	if context.Contains(consts.TerminalRequired) {

		sshClient.terminalRequired = context.GetBoolValue(consts.TerminalRequired)

	}

	return sshClient
}

func (sshClient *SSHClient) setProxyServer(context MotadataMap) *SSHClient {

	if context.Contains(consts.ProxyServer) {

		sshClient.proxyServer = context.GetMotadataStringValue(consts.ProxyServer)
	}

	return sshClient
}

func (sshClient *SSHClient) setPort(context MotadataMap) *SSHClient {

	if context.Contains(consts.Port) {

		sshClient.port = context.GetUINT16Value(consts.Port)

	} else {

		sshClient.port = MotadataUINT16(22)
	}

	return sshClient
}

func (sshClient *SSHClient) setUsername(context MotadataMap) *SSHClient {

	if context.Contains(consts.UserName) {

		sshClient.userName = context.GetMotadataStringValue(consts.UserName)

	}

	return sshClient
}

func (sshClient *SSHClient) setPassword(context MotadataMap) *SSHClient {

	if context.Contains(consts.Password) {

		sshClient.password = context.GetMotadataStringValue(consts.Password)

	}

	return sshClient
}

func (sshClient *SSHClient) setProxyPort(context MotadataMap) *SSHClient {

	if context.Contains(consts.ProxyPort) {

		sshClient.proxyPort = context.GetUINT16Value(consts.ProxyPort)
	}

	return sshClient
}

func (sshClient *SSHClient) setAgent(context MotadataMap) *SSHClient {

	if context.Contains(consts.ObjectDiscoveryMethod) &&
		context.GetStringValue(consts.ObjectDiscoveryMethod) == consts.ObjectDiscoveryMethodAgent {

		sshClient.agent = true

	} else {

		sshClient.agent = false
	}

	return sshClient
}

func (sshClient *SSHClient) setLogger(logger *Logger) *SSHClient {

	sshClient.logger = logger

	return sshClient
}

func (sshClient *SSHClient) setProxyUsername(context MotadataMap) *SSHClient {

	if context.Contains(consts.ProxyUserName) {

		sshClient.proxyUserName = context.GetMotadataStringValue(consts.ProxyUserName)
	}

	return sshClient
}

func (sshClient *SSHClient) setProxyPassword(context MotadataMap) *SSHClient {

	if context.Contains(consts.ProxyPassword) {

		sshClient.proxyPassword = context.GetMotadataStringValue(consts.ProxyPassword)
	}

	return sshClient
}

func (sshClient *SSHClient) setSSHKey(context MotadataMap) *SSHClient {

	if context.Contains(sshKey) {

		sshClient.key = context.GetMotadataStringValue(sshKey)

	} else {

		sshClient.key = consts.BlankString
	}

	return sshClient
}

func (sshClient *SSHClient) setPassphrase(context MotadataMap) *SSHClient {

	if context.Contains(passphrase) {

		sshClient.passphrase = context.GetMotadataStringValue(passphrase)

	} else {

		sshClient.passphrase = consts.BlankString
	}

	return sshClient
}

func (sshClient *SSHClient) setTimeout(context MotadataMap) *SSHClient {

	if context.Contains(consts.Timeout) {

		sshClient.timeout = context.GetUINTValue(consts.Timeout)

	} else {

		sshClient.timeout = MotadataUINT(60)
	}

	return sshClient
}

func (sshClient *SSHClient) setWorklogs(context MotadataMap) *SSHClient {

	if context.Contains(consts.ConfigWorklogs) {

		sshClient.logs = context.GetMapValue(consts.ConfigWorklogs).Copy()

	} else {

		sshClient.logs = make(MotadataMap)
	}

	return sshClient
}

func (sshClient *SSHClient) GetWorklogs() MotadataMap {

	return sshClient.logs
}

func (sshClient *SSHClient) GetCLIOutput() MotadataString {

	return sshClient.output
}

func (sshClient *SSHClient) SetPrintOutput(value bool) *SSHClient {

	if sshClient.terminalRequired {

		sshClient.printOutput = false

	} else {

		sshClient.printOutput = value
	}

	return sshClient
}

func (sshClient *SSHClient) setAttributes() *SSHClient {

	sshClient.attributes = MotadataMap{}

	return sshClient
}

func (sshClient *SSHClient) GetAttributes() MotadataMap {

	return sshClient.attributes
}

func (sshClient *SSHClient) IsAgent() bool {

	return sshClient.agent

}

func (sshClient *SSHClient) GetClient() *ssh.Client {

	return sshClient.client
}

func (sshClient *SSHClient) setNewLineSeparator(context MotadataMap) *SSHClient {

	if context.GetMotadataStringValue(consts.ObjectVendor).ToLower().Contains("tp-link") {

		sshClient.newLineSeparator = consts.NewLineRegexPattern
	} else {

		sshClient.newLineSeparator = consts.NewLineSeparator
	}

	return sshClient
}

func (SSHClient *SSHClient) GetNewLineSeparator() MotadataString {

	return SSHClient.newLineSeparator
}

// InitConnection
/* Method will be used to initialize connection with devices
   - Storing connection progress in map which will be used in UI to display the phase wise details
*/
func (sshClient *SSHClient) InitConnection() (result bool) {

	result = false

	interactive := getInteractiveCallBack(sshClient.password.ToString())

	config := &ssh.ClientConfig{
		Timeout:           time.Duration(sshClient.timeout.ToInt()) * time.Second,
		User:              ToString(sshClient.userName),
		HostKeyCallback:   ssh.InsecureIgnoreHostKey(),
		HostKeyAlgorithms: hostKeyAlgorithms,
	}

	config.SetDefaults()

	config.Config.KeyExchanges = append(config.Config.KeyExchanges, kexAlgorithms...)

	config.Ciphers = append(config.Ciphers, ciphers...)

	config.MACs = append(config.MACs, macAlgorithms...) // Add MAC algorithms

	flag := true

	if sshClient.key.IsNotEmpty() {

		var signer ssh.Signer

		var err error

		if sshClient.passphrase.IsNotEmpty() {

			signer, err = ssh.ParsePrivateKeyWithPassphrase([]byte(sshClient.key), []byte(sshClient.passphrase))

		} else {

			signer, err = ssh.ParsePrivateKey([]byte(sshClient.key))
		}

		config.Auth = []ssh.AuthMethod{ssh.PublicKeys(signer)}

		if err != nil {

			if MotadataString(err.Error()).HasSuffix("decryption password incorrect") {

				sshClient.errors = append(sshClient.errors, MotadataStringMap{
					consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidCredentials, sshClient.target, sshClient.port),
					consts.ErrorCode: consts.ErrorCodeInvalidCredentials,
					consts.Error:     err.Error(),
				})

			} else {

				sshClient.errors = append(sshClient.errors, MotadataStringMap{
					consts.Message:   "Invalid public/private SSH key",
					consts.ErrorCode: consts.ErrorCodeInvalidPublicOrPrivateSSHKey,
					consts.Error:     err.Error(),
				})
			}

			flag = false
		}

	} else {

		config.Auth = []ssh.AuthMethod{ssh.KeyboardInteractive(interactive), ssh.Password(ToString(sshClient.password))}
	}

	if flag {

		var err error

		if sshClient.proxyServer.IsNotEmpty() {

			proxyConfig := &ssh.ClientConfig{
				Timeout:           time.Duration(sshClient.timeout.ToInt()) * time.Second,
				User:              ToString(sshClient.proxyUserName),
				Auth:              []ssh.AuthMethod{ssh.Password(ToString(sshClient.proxyPassword))},
				HostKeyCallback:   ssh.InsecureIgnoreHostKey(),
				HostKeyAlgorithms: hostKeyAlgorithms,
			}

			proxyConfig.SetDefaults()

			proxyConfig.Config.KeyExchanges = append(proxyConfig.Config.KeyExchanges, kexAlgorithms...)

			proxyConfig.Ciphers = append(proxyConfig.Ciphers, ciphers...)

			config.MACs = append(config.MACs, macAlgorithms...) // Add MAC algorithms

			sshClient.proxyClient, err = ssh.Dial("tcp", net.JoinHostPort(string(sshClient.proxyServer), strconv.Itoa(int(sshClient.proxyPort))), proxyConfig)

			if err == nil {

				conn, err := sshClient.proxyClient.Dial("tcp", net.JoinHostPort(string(sshClient.target), strconv.Itoa(int(sshClient.port))))

				if err != nil {

					sshClient.handleErrors(err)

					return false
				}

				targetConn, channel, request, err := ssh.NewClientConn(conn, net.JoinHostPort(string(sshClient.target), strconv.Itoa(int(sshClient.port))), config)

				if err != nil {

					sshClient.handleErrors(err)

					return false
				}

				sshClient.client = ssh.NewClient(targetConn, channel, request)
			}

		} else {

			sshClient.client, err = ssh.Dial("tcp", net.JoinHostPort(string(sshClient.target), strconv.Itoa(int(sshClient.port))), config)
		}

		if err != nil {

			sshClient.handleErrors(err)

		} else {

			result = true

			sshClient.logs[consts.ConnectionKey] = consts.StatusSucceed + consts.WorkLogSeparator + consts.StatusSucceed

			sshClient.logs[consts.UserName] = sshClient.userName + consts.WorkLogSeparator + consts.StatusSucceed

			sshClient.logs[consts.Password] = consts.BlankString + consts.WorkLogSeparator + consts.StatusSucceed
		}
	}

	return
}

func (sshClient *SSHClient) handleErrors(err error) {

	sshClient.logger.Warn(MotadataString(fmt.Sprintf("error %v occurred for %v host...", err.Error(), sshClient.target)))

	if MotadataString(err.Error()).HasSuffix("i/o timeout") {

		if IsOpened(sshClient.port, sshClient.target) {

			sshClient.errors = append(sshClient.errors, MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageConnectionTimeout, "SSH", sshClient.target, sshClient.port),
				consts.ErrorCode: consts.ErrorCodeTimeout,
				consts.Error:     err.Error(),
			})
		} else {

			sshClient.errors = append(sshClient.errors, MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidPort, sshClient.port),
				consts.ErrorCode: consts.ErrorCodeInvalidPort,
				consts.Error:     err.Error(),
			})
		}

	} else if MotadataString(err.Error()).HasSuffix("connection refused") {

		sshClient.errors = append(sshClient.errors, MotadataStringMap{
			consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidPort, sshClient.port),
			consts.ErrorCode: consts.ErrorCodeInvalidPort,
			consts.Error:     err.Error(),
		})

	} else if MotadataString(err.Error()).Contains("unable to authenticate") {

		sshClient.errors = append(sshClient.errors, MotadataStringMap{
			consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidCredentials, sshClient.target, sshClient.port),
			consts.ErrorCode: consts.ErrorCodeInvalidCredentials,
			consts.Error:     err.Error(),
		})

	} else {

		if IsOpened(sshClient.port, sshClient.target) {

			sshClient.errors = append(sshClient.errors, MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageConnectionFailed, "SSH", sshClient.target, sshClient.port),
				consts.ErrorCode: consts.ErrorCodeConnectionFailed,
				consts.Error:     err.Error(),
			})
		} else {

			sshClient.errors = append(sshClient.errors, MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidPort, sshClient.port),
				consts.ErrorCode: consts.ErrorCodeInvalidPort,
				consts.Error:     err.Error(),
			})
		}
	}
}

//InitSession
/* Method will be used build new shell session with reader and writer.
- Setting terminal modes to control the behavior of the terminal window
*/
func (sshClient *SSHClient) InitSession(prompt MotadataString, enablePrompt MotadataString, enableUserName MotadataString, enablePassword MotadataString) MotadataStringMap {

	session, err := sshClient.client.NewSession()

	if err != nil {

		sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageSessionCreationIssue, err.Error(), sshClient.target)))

		return MotadataStringMap{
			consts.Message:   fmt.Sprintf(consts.ErrorMessageWhileInitSSHSession, sshClient.target, sshClient.port),
			consts.ErrorCode: consts.ErrorCodeWhileInitSSHSession,
			consts.Error:     err.Error(),
		}
	}

	sshClient.reader, _ = session.StdoutPipe()

	sshClient.writer, _ = session.StdinPipe()

	modes := ssh.TerminalModes{
		ssh.ECHO:          1,
		ssh.TTY_OP_ISPEED: 14400,
		ssh.TTY_OP_OSPEED: 14400,
	}

	if err := session.RequestPty("vt100", 0, 200, modes); err != nil {

		sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageWhileRequestingPTY, err.Error(), sshClient.target)))

		return MotadataStringMap{
			consts.Message:   fmt.Sprintf(consts.ErrorMessageWhileRequestingPTYWithHost, sshClient.target, sshClient.port),
			consts.ErrorCode: consts.ErrorCodeWhileRequestingPTYWithHost,
			consts.Error:     err.Error(),
		}
	}

	if err := session.Shell(); err != nil {

		sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageWhileInvokingSSHShell, err.Error(), sshClient.target)))

		return MotadataStringMap{
			consts.Message:   fmt.Sprintf(consts.ErrorMessageWhileInvokingSSHShellWithHost, sshClient.target, sshClient.port),
			consts.ErrorCode: consts.ErrorCodeWhileInvokingSSHShellWithHost,
			consts.Error:     err.Error(),
		}
	}

	if sshClient.terminalRequired {

		return nil

	} else {

		return sshClient.PrepareSession(prompt, enablePrompt, enableUserName, enablePassword)

	}
}

func (sshClient *SSHClient) Read() (MotadataString, error) {

	bytes := make([]byte, consts.ReadBufferBytes)

	n, err := sshClient.reader.Read(bytes)

	return MotadataString(junkCharPattern.ReplaceAllString(string(bytes[:n]), consts.BlankString)), err
}

func (sshClient *SSHClient) Write(command MotadataString) int {

	bytes := []byte(command)

	status, _ := sshClient.writer.Write(bytes)

	return status
}

func getInteractiveCallBack(password string) ssh.KeyboardInteractiveChallenge {

	return func(user, instruction string, questions []string, echos []bool) (answers []string, err error) {

		answers = make([]string, len(questions))

		// The second parameter is unused
		for index := range questions {

			if strings.Contains(strings.ToLower(ToString(questions[index])), consts.Password) {

				answers[index] = password

			} else {

				answers[index] = consts.Yes
			}
		}

		return answers, nil
	}
}

// SendCommand
/* Method used to send command with expected pattern
- After writing command, wait for some time (i.e. delay time) to make sure that output is ready in the channel
- Read the output until it matches the expected pattern.
- Validating output to check whether it contains any error or not
*/
func (sshClient *SSHClient) SendCommand(command MotadataString, pattern MotadataString, delayTime MotadataUINT, timeout MotadataUINT) (MotadataString, error) {

	sshClient.logger.Debug(MotadataString(fmt.Sprintf(consts.InfoMessageExecutingCommand, command, sshClient.target)))

	tick := time.Now().Unix()

	command += sshClient.GetNewLineSeparator()

	sshClient.Write(command)

	time.Sleep(time.Duration(delayTime) * time.Millisecond)

	result, err := sshClient.ReadUntil(command, pattern, timeout)

	sshClient.logger.Debug(MotadataString(fmt.Sprintf(consts.InfoMessageExecutedCommand, command.ReplaceAll(sshClient.GetNewLineSeparator(), consts.BlankString), sshClient.target, time.Now().Unix()-tick)))

	if err == nil {

		status := sshClient.validateOutput(result, command.ReplaceAll(sshClient.GetNewLineSeparator(), consts.BlankString))

		if status.IsNotEmpty() {

			err = errors.New(status.ToString())
		}
	}

	return result, err
}

// SendCommandDirect
/* Method will send the command without any expected pattern
- As of now we have used this method to find backup end prompt
*/
func (sshClient *SSHClient) SendCommandDirect(command MotadataString) (MotadataString, error) {

	result, err := sshClient.SendCommand(command, consts.BlankString, consts.DefaultCommandDelayTime, consts.DefaultCommandTimeout)

	return result, err

}

// ReadUntil
/* Method will read the output until we get the expected pattern
- It will time out after waiting for the provided time in case, we didn't get expected pattern
*/
func (sshClient *SSHClient) ReadUntil(command MotadataString, pattern MotadataString, timeout MotadataUINT) (MotadataString, error) {

	parentChannel := make(chan MotadataString)

	var err error

	go func(sshClient *SSHClient, pattern MotadataString) {

		childChannel := make(chan MotadataString)

		go read(sshClient, pattern, childChannel, command.ReplaceAll(sshClient.GetNewLineSeparator(), consts.BlankString))

		select {

		case buffer := <-childChannel:

			parentChannel <- buffer

		case <-time.After(time.Duration(timeout) * time.Millisecond):

			err = errors.New(fmt.Sprintf(consts.ErrorMessageReadingOutputTimeOut, command.ReplaceAll(sshClient.GetNewLineSeparator(), consts.BlankString), pattern))

			sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageTimeOutWhileReadingOutput, command, pattern, sshClient.target)))

			parentChannel <- MotadataString(fmt.Sprintf(consts.ErrorMessageReadingOutputTimeOut, command.ReplaceAll(sshClient.GetNewLineSeparator(), consts.BlankString), pattern))

			close(parentChannel)
		}

	}(sshClient, pattern)

	return <-parentChannel, err
}

// PrepareSession
/* Method will prepare ssh session by entering enable command with enable username and password.
- Also, we will store the phase wise details in the map which will be used in UI to show the errors
*/
func (sshClient *SSHClient) PrepareSession(prompt MotadataString, enablePrompt MotadataString, enableUserName MotadataString, enablePassword MotadataString) MotadataStringMap {

	if len(enablePrompt.ToString()) > 0 {

		enablePasswordPattern := MotadataString(consts.BlankString)

		if len(enablePassword.ToString()) > 0 {

			enablePasswordPattern = consts.EnablePasswordPattern
		}

		output, err := sshClient.SendCommand(enablePrompt, enablePasswordPattern, consts.DefaultCommandDelayTime, consts.DefaultCommandTimeout)

		if err != nil {

			sshClient.logs[consts.EnablePromptKey] = enablePrompt.ToString() + consts.WorkLogSeparator + consts.StatusFail

			sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageWhileSendingEnableCommand, err.Error(), sshClient.target)))

			return MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageWhileSendingEnableCommandWithHost, sshClient.target, sshClient.port),
				consts.ErrorCode: consts.ErrorCodeWhileSendingEnableCommandWithHost,
				consts.Error:     err.Error(),
			}
		}

		sshClient.logs[consts.EnablePromptKey] = enablePrompt.ToString() + consts.WorkLogSeparator + consts.StatusSucceed

		if enableUserName.IsNotEmpty() {

			output, err = sshClient.SendCommand(enableUserName, enablePasswordPattern, consts.DefaultCommandDelayTime, consts.DefaultCommandTimeout)

			if err != nil {

				sshClient.logs[consts.EnableUserNameKey] = enableUserName.ToString() + consts.WorkLogSeparator + consts.StatusFail

				sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageWhileSendingEnableUserCommand, err.Error(), sshClient.target)))

				return MotadataStringMap{
					consts.Message:   fmt.Sprintf(consts.ErrorMessageWhileSendingEnableUserCommandWithHost, sshClient.target, sshClient.port),
					consts.ErrorCode: consts.ErrorCodeWhileSendingEnableUserCommandWithHost,
					consts.Error:     err.Error(),
				}
			}

			sshClient.logs[consts.EnableUserNameKey] = enableUserName.ToString() + consts.WorkLogSeparator + consts.StatusSucceed
		}

		if output.ToLower().Contains(consts.Password) {

			if enablePassword.IsNotEmpty() {

				output, err = sshClient.SendCommand(enablePassword, prompt, consts.DefaultCommandDelayTime, consts.DefaultCommandTimeout)

				if err != nil {

					sshClient.logs[consts.EnablePasswordKey] = consts.BlankString + consts.WorkLogSeparator + consts.StatusFail

					sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageWhileSendingEnablePasswordCommand, err.Error(), sshClient.target)))

					return MotadataStringMap{
						consts.Message:   fmt.Sprintf(consts.ErrorMessageWhileSendingEnablePasswordCommandWithHost, sshClient.target, sshClient.port),
						consts.ErrorCode: consts.ErrorCodeWhileSendingEnablePasswordCommandWithHost,
						consts.Error:     err.Error(),
					}
				}

				sshClient.logs[consts.EnablePasswordKey] = consts.BlankString + consts.WorkLogSeparator + consts.StatusSucceed

			} else {

				sshClient.logs[consts.EnablePasswordKey] = consts.BlankString + consts.WorkLogSeparator + consts.StatusFail

				sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageEnablePasswordNotProvided, sshClient.target)))

				return MotadataStringMap{
					consts.Message:   fmt.Sprintf(consts.ErrorMessageEnablePasswordNotProvided, sshClient.target),
					consts.ErrorCode: consts.ErrorCodeEnablePasswordNotProvided,
					consts.Error:     fmt.Sprintf(consts.ErrorMessageEnablePasswordNotProvided, sshClient.target),
				}
			}
		}

		if !output.Contains(prompt.ToString()) {

			sshClient.logs[consts.PromptKey] = prompt.ToString() + consts.WorkLogSeparator + consts.StatusFail

			sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageFailToEnterEnableMode, sshClient.target)))

			return MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageFailToEnterEnableModeWithHost, sshClient.target, sshClient.port),
				consts.ErrorCode: consts.ErrorCodeFailToEnterEnableModeWithHost,
				consts.Error:     fmt.Sprintf(consts.ErrorMessageFailToEnterEnableModeWithHost, sshClient.target, sshClient.port),
			}

		} else {

			sshClient.logs[consts.PromptKey] = prompt.ToString() + consts.WorkLogSeparator + consts.StatusSucceed
		}
	}

	//Sending blank command to read pending I/O stream data , necessary for the direct backup command
	sshClient.SendCommand(consts.BlankString, consts.BlankString, consts.DefaultBannerPrintTime, consts.DefaultCommandTimeout)

	return nil
}

func (sshClient *SSHClient) validateOutput(result MotadataString, command MotadataString) (error MotadataString) {

	for _, cliError := range configCLIErrors {

		if result.Contains(cliError) {

			sshClient.logger.Warn(MotadataString(fmt.Sprintf(consts.ErrorMessageConfigOutputError, command, sshClient.target, result)))

			error = MotadataString(cliError)

			break
		}
	}

	return error
}

// read
/* Method will read the output until we get the expected pattern
 */
func read(sshClient *SSHClient, pattern MotadataString, response chan<- MotadataString, command MotadataString) {

	result, err := sshClient.Read()

	if err != nil {

		result = MotadataString(fmt.Sprintf(consts.ErrorMessageFailReadingOutput, err.Error(), sshClient.target))

	} else {

		sshClient.AppendCLIOutput(result)

		status := sshClient.validateOutput(result, command.ReplaceAll(sshClient.GetNewLineSeparator(), consts.BlankString))

		if status.IsNotEmpty() {

			if !sshClient.terminalRequired && sshClient.printOutput == false { // This is in case of no protocol, if there is error in output then only we will add the cli output.

				sshClient.printOutput = true

				sshClient.AppendCLIOutput(result)
			}

			err = errors.New(status.ToString())

			result = status
		}
	}

	for (pattern.IsNotEmpty()) && (err == nil) && (!result.Contains(pattern.ToString())) {

		value, err := sshClient.Read()

		if err != nil {

			result = MotadataString(fmt.Sprintf(consts.ErrorMessageFailReadingOutput, err.Error(), sshClient.target))

			break
		}

		sshClient.AppendCLIOutput(value)

		result += value
	}

	sshClient.logger.Debug(MotadataString(fmt.Sprintf("output for 'command %s' : '%s'", command, result)))

	response <- result
}

func (sshClient *SSHClient) AppendCLIOutput(value MotadataString) {

	if sshClient.printOutput {

		sshClient.output = sshClient.output + value
	}
}
