/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package sshclient

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

var (
	command = MotadataString("uname -a")

	invalidCommand = MotadataString("unddame -a")

	bytes []byte

	loggerObj = NewLogger("Test SSHClient", consts.BlankString)
)

func TestMain(m *testing.M) {

	bytes, _ = os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(consts.CurrentDir)))) + consts.PathSeparator + consts.ConfigDirectory + consts.PathSeparator + "context.json")

	m.Run()
}

func TestSSHClientNetworkDevice(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	result := sshClient.Discover(GetContext(contexts, MotadataStringList{consts.Linux, "cisco"}), &loggerObj)

	assertions.NotNil(result)

	assertions.True(len(result) > 0)

	assertions.True(result.Contains(consts.ObjectCredentialProfile))

	assertions.True(result.Contains(consts.CredentialProfileName))

	BuildLinuxUnixDiscoveryResult(GetContext(contexts, MotadataStringList{consts.Linux, "cisco"}), GetContext(contexts, MotadataStringList{consts.Linux, "cisco"})[consts.ObjectIP], sshClient)

	assertions.True(len(sshClient.GetErrors()) == 1)

	assertions.Equal(consts.ErrorCodeInvalidLinuxORUnixHost, sshClient.GetErrors()[0][consts.ErrorCode])

	sshClient.Destroy()

}

func TestSSHClientInvalidCommand(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	sshClient.SetContext(GetContext(contexts, MotadataStringList{"ssh-client", "test_valid_ssh_key_with_passphrase"}), &loggerObj)

	result := sshClient.Init()

	assertions.Equal(true, result)

	output, _ := sshClient.ExecuteCommand(invalidCommand)

	assertions.True(len(output) == 0)

	assertions.True(len(sshClient.GetErrors()) == 1)

	assertions.Equal(consts.ErrorCodeCommandExecutionFailed, sshClient.GetErrors()[0][consts.ErrorCode])

	sshClient.Destroy()
}

func TestSSHClientInvalidSSHKey(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	sshClient.SetContext(GetContext(contexts, MotadataStringList{"ssh-client", "test_invalid_ssh_key_without_passphrase"}), &loggerObj)

	result := sshClient.Init()

	assertions.Equal(false, result)

	assertions.True(len(sshClient.GetErrors()) == 1)

	assertions.Equal(consts.ErrorCodeInvalidPublicOrPrivateSSHKey, sshClient.GetErrors()[0][consts.ErrorCode])
}

func TestSSHClientInvalidPassphrase(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	sshClient.SetContext(GetContext(contexts, MotadataStringList{"ssh-client", "test_invalid_passphrase_ssh_key"}), &loggerObj)

	result := sshClient.Init()

	assertions.Equal(false, result)

	assertions.True(len(sshClient.GetErrors()) == 1)

	assertions.Equal(consts.ErrorCodeInvalidCredentials, sshClient.GetErrors()[0][consts.ErrorCode])
}

func TestSSHClientInvalidPassword(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{consts.Linux, "rhel", "7.5"})

	context[consts.Password] = consts.InvalidPassword

	sshClient.SetContext(context, &loggerObj)

	result := sshClient.Init()

	assertions.Equal(false, result)

	assertions.True(len(sshClient.GetErrors()) == 1)

	assertions.Equal(consts.ErrorCodeInvalidCredentials, sshClient.GetErrors()[0][consts.ErrorCode])
}

func TestSSHClientInvalidUsername(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{consts.Linux, "centos", "7.5"})

	context[consts.UserName] = consts.InvalidUserName

	sshClient.SetContext(context, &loggerObj)

	result := sshClient.Init()

	assertions.Equal(false, result)

	assertions.True(len(sshClient.GetErrors()) == 1)

	assertions.Equal(consts.ErrorCodeInvalidCredentials, sshClient.GetErrors()[0][consts.ErrorCode])

	sshClient.Destroy()
}

func TestSSHClientInvalidHost(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{consts.Linux, "rhel", "7.5"})

	context[consts.ObjectIP] = consts.InvalidHost

	sshClient.SetContext(context, &loggerObj)

	result := sshClient.Init()

	assertions.Equal(false, result)

	assertions.True(len(sshClient.GetErrors()) == 1)

	assertions.Equal(consts.ErrorCodeInvalidPort, sshClient.GetErrors()[0][consts.ErrorCode])
}

func TestSSHClient(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	sshClient.SetContext(GetContext(contexts, MotadataStringList{"ssh-client", "test_valid_ssh_key_with_passphrase"}), &loggerObj)

	result := sshClient.Init()

	assertions.Equal(true, result)

	output, _ := sshClient.ExecuteCommand(command)

	assertions.True(len(output) > 0)

	assertions.True(len(sshClient.GetErrors()) == 0)

	sshClient.Destroy()
}

func TestSSHClientAgent(t *testing.T) {

	if strings.Compare(consts.PathSeparator, "\\") == 0 {

		t.Skipf("Skipped due to non *nix environment...")
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"ssh-client", "test_valid_ssh_key_without_passphrase"})

	context[consts.ObjectDiscoveryMethod] = consts.ObjectDiscoveryMethodAgent

	assertions := assert.New(t)

	sshClient.SetContext(context, &loggerObj).setAgent(context)

	result := sshClient.Init()

	assertions.Equal(true, result)

	output, _ := sshClient.ExecuteCommand("uname -a")

	assertions.True(len(output) > 0)

	assertions.Contains(output, "x86_64")

	assertions.True(len(sshClient.GetErrors()) == 0)

	sshClient.Destroy()
}

func TestSSHClientAmazonLinux(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	sshClient.SetContext(GetContext(contexts, MotadataStringList{"ssh-client", "test_amazon_linux_ami_1"}), &loggerObj)

	result := sshClient.Init()

	assertions.Equal(true, result)

	output, _ := sshClient.ExecuteCommand(command)

	assertions.True(len(output) > 0)

	assertions.True(len(sshClient.GetErrors()) == 0)

	sshClient.Destroy()
}

func TestSSHClientValidateMetricValue(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	sshClient.SetContext(GetContext(contexts, MotadataStringList{consts.Linux, "ubuntu", "14"}), &loggerObj)

	result := sshClient.Init()

	assertions.Equal(true, result)

	output, _ := sshClient.ExecuteCommand(ToMotadataString("echo '" + consts.UniqueIdentifier + "1" + "';mpstat  1 1;" +
		"echo '" + consts.UniqueIdentifier + "2" + "';vmstat 1 3;" +
		"echo '" + consts.UniqueIdentifier + "3" + "';df -l -k -T| grep -vE 'squashfs|tmpfs|cdrom|none';" +
		"echo '" + consts.UniqueIdentifier + "5" + "';iostat -d -xk;"))

	output += "command not found"

	_, valid := ValidateMetricValue(output, consts.UniqueIdentifier+"2", consts.UniqueIdentifier+"1")

	assertions.True(len(output) > 0)

	assertions.True(valid)

	sshClient.Destroy()
}

func TestSSHClientGetProcessMetrics(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	sshClient := SSHClient{}

	context := GetContext(contexts, MotadataStringList{consts.Linux, "************"})

	context = BeforeTest(context)

	sshClient.SetContext(context, &loggerObj)

	sshClient.Init()

	processes := make(MotadataMap)

	logicalProcessors := MotadataFloat64(0)

	if output, valid := sshClient.ExecuteCommand("cat /proc/cpuinfo | grep processor | wc -l"); len(output) > 1 {

		if valid {

			tokens := output.Split(consts.SpaceSeparator)

			if IsNotEmptyStringSlice(tokens) && tokens[0].IsDigit() {

				logicalProcessors = tokens[0].ToFloat64()
			}
		}
	}

	result := GetProcessMetrics(context, processes, &sshClient, logicalProcessors)

	assert.NotNil(t, result)

	assert.Greater(t, len(result), 0)

	sshClient.Destroy()

}

func TestSSHClientUptimePattern1(t *testing.T) {

	assertSSHClientUptimePatternTestResult("1:00pm  up 23:56,  4 users,  load average: 0.01, 0.01, 0.01", MotadataMap{}, assert.New(t))
}

func TestSSHClientUptimePattern2(t *testing.T) {

	assertSSHClientUptimePatternTestResult("1:07pm  up 1 day, 3 mins,  4 users,  load average: 0.01, 0.01, 0.01", MotadataMap{}, assert.New(t))
}

func TestSSHClientUptimePattern3(t *testing.T) {

	assertSSHClientUptimePatternTestResult("4:09am  up 1 day, 15:05,  2 users,  load average: 0.01, 0.01, 0.01", MotadataMap{}, assert.New(t))
}

func TestSSHClientUptimePattern4(t *testing.T) {

	assertSSHClientUptimePatternTestResult("4:09am  up 1 days, 15:05,  2 users,  load average: 0.01, 0.01, 0.01", MotadataMap{}, assert.New(t))
}

func TestSSHClientUptimePattern5(t *testing.T) {

	assertSSHClientUptimePatternTestResult("11:08pm  up 2 days, 2 mins,  1 user,  load average: 0.03, 0.03, 0.03", MotadataMap{}, assert.New(t))
}

func TestSSHClientUptimePattern6(t *testing.T) {

	assertSSHClientUptimePatternTestResult("11:08pm  up 2 mins,  1 user,  load average: 0.03, 0.03, 0.03", MotadataMap{}, assert.New(t))
}

func TestSSHClientUptimePattern7(t *testing.T) {

	assertSSHClientUptimePatternTestResult("11:08pm  up 1 min,  1 user,  load average: 0.03, 0.03, 0.03", MotadataMap{}, assert.New(t))
}

func TestSSHClientUptimePattern8(t *testing.T) {

	assertSSHClientUptimePatternTestResult("11:08pm  up 1 hr,  1 user,  load average: 0.03, 0.03, 0.03", MotadataMap{}, assert.New(t))
}

func TestSSHClientUptimePattern9(t *testing.T) {

	assertSSHClientUptimePatternTestResult("11:08pm  up 2 hrs,  1 user,  load average: 0.03, 0.03, 0.03", MotadataMap{}, assert.New(t))
}

func assertSSHClientUptimePatternTestResult(token string, result MotadataMap, assertions *assert.Assertions) {

	output := SetUptimeMetrics(MotadataString(token), result)

	assertions.Greater(output.GetINTValue(consts.StartedTimeSeconds), 0)

	assertions.Greater(len(output.GetMotadataStringValue(consts.StartedTime)), 0)
}

func TestSSHClientExecuteCLICommandGroupNoBackupProtocol(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	var contexts MotadataMap

	sshClient := &SSHClient{}

	_ = json.Unmarshal(bytes, &contexts)

	context := BeforeTest(GetContext(contexts, MotadataStringList{"configSSH", "cisco", "valid.without.protocol"}))

	sshClient.SetContext(context, &loggerObj)

	sshClient.InitConnection()

	err := sshClient.InitSession(context.GetMotadataStringValue(consts.Prompt), context.GetMotadataStringValue(consts.EnablePrompt), context.GetMotadataStringValue(consts.EnableUsername), context.GetMotadataStringValue(consts.EnablePassword))

	if err != nil {

		t.Fail()
	}

	backupResult := MotadataMap{}

	prompt := "ospf1#"

	backupFileName := "Backup_startup.cfg"

	template := context.GetMapValue(consts.ConfigTemplate)

	groups := template.GetMapValue(context.GetStringValue(consts.FileTransferProtocol))

	delayTime := MotadataUINT(consts.DefaultCommandDelayTime)

	result := ExecuteCLICommandGroups(sshClient, groups.GetSliceValue(consts.StartUpBackup), context, MotadataString(backupFileName), "startup.config", MotadataString(prompt), backupResult, delayTime)

	assert.NotNil(t, result)

	assert.True(t, result)

	sshClient.Destroy()

}

func TestSSHClientExecuteCLICommandGroupNoSyncProtocol(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	var contexts MotadataMap

	sshClient := &SSHClient{}

	_ = json.Unmarshal(bytes, &contexts)

	context := BeforeTest(GetContext(contexts, MotadataStringList{"configSSH", "cisco", "valid.without.protocol"}))

	sshClient.SetContext(context, &loggerObj)

	if sshClient.InitConnection() {

		err := sshClient.InitSession(context.GetMotadataStringValue(consts.Prompt), context.GetMotadataStringValue(consts.EnablePrompt), context.GetMotadataStringValue(consts.EnableUsername), context.GetMotadataStringValue(consts.EnablePassword))

		if err != nil {

			t.Fail()
		}

		syncResult := MotadataMap{}

		template := context.GetMapValue(consts.ConfigTemplate)

		groups := template.GetMapValue(context.GetStringValue(consts.FileTransferProtocol))

		delayTime := MotadataUINT(consts.DefaultCommandDelayTime)

		result := ExecuteCLICommandGroups(sshClient, groups.GetSliceValue(consts.OperationTypeSync), context, consts.BlankString, consts.OperationTypeSync, consts.BlankString, syncResult, delayTime)

		assert.NotNil(t, result)

		assert.True(t, result)

	} else {

		t.Fail()
	}

	sshClient.Destroy()

}

func TestSSHClientExecuteCLICommandGroupNoBackupProtocolInvalidCommandGroup(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	var contexts MotadataMap

	sshClient := &SSHClient{}

	_ = json.Unmarshal(bytes, &contexts)

	context := BeforeTest(GetContext(contexts, MotadataStringList{"configSSH", "cisco", "valid.without.protocol"}))

	sshClient.SetContext(context, &loggerObj)

	if sshClient.InitConnection() {

		err := sshClient.InitSession(context.GetMotadataStringValue(consts.Prompt), context.GetMotadataStringValue(consts.EnablePrompt), context.GetMotadataStringValue(consts.EnableUsername), context.GetMotadataStringValue(consts.EnablePassword))

		if err != nil {

			t.Fail()
		}

		backupEndPrompt := "ospf1#"

		backupFileName := "Backup_startup.cfg"

		backupResult := MotadataMap{}

		commandGroups := []interface{}{

			map[string]interface{}{
				"operation.command":        "Bad secrets",
				"operation.delay.time":     1000,
				"operation.timeout":        2000,
				"operation.prompt":         "]?",
				"operation.prompt.command": "No Command",
			},
		}

		delayTime := MotadataUINT(consts.DefaultCommandDelayTime)

		result := ExecuteCLICommandGroups(sshClient, commandGroups, context, MotadataString(backupFileName), "startup.config", MotadataString(backupEndPrompt), backupResult, delayTime)

		assert.NotNil(t, result)

		assert.False(t, result)

	} else {

		t.Fail()
	}

	sshClient.Destroy()

}

func TestSSHClientSetProcessNetworkConnectionMetrics(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	metrics := MotadataMap{"system.process.network.connection": make([]MotadataMap, 0)}

	tokens := []MotadataString{"0.0.0.0:22 0.0.0.0:* 23980/sshd", ":::22 :::* 23980/sshd"}

	processes := MotadataMap{"23980": MotadataMap{"system.process": "sshd|/usr/sbin/sshd -D", "system.process.id": 23980}}

	SetProcessNetworkConnectionMetrics(metrics, tokens, "************", processes, false)

	assertSSHClientSetProcessNetworkConnectionMetrics(metrics[consts.NetworkConnection].([]MotadataMap), assert.New(t))

}

func assertSSHClientSetProcessNetworkConnectionMetrics(result []MotadataMap, assertions *assert.Assertions) {

	assertions.NotNil(result)

	assertions.NotNil(result[0])

	assertions.Greater(len(result), 0)

	assertions.Contains(result[0], consts.SourceIP)

	assertions.Contains(result[0], consts.DestinationIP)

	assertions.Contains(result[0], consts.DestinationPort)

	assertions.Contains(result[0], consts.SourcePort)

	assertions.Contains(result[0], consts.Process)

	assertions.Equal(result[0].GetStringValue(consts.SourceIP), "************")

	assertions.Equal(result[0].GetStringValue(consts.DestinationIP), "************")

}

func TestSSHClientGetReadWriteExecuteFilePermissionMode(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	result := GetFilePermissionMode("rwx")

	assertions.NotNil(result)

	assertions.Equal(MotadataString("read write execute"), result)

}

func TestSSHClientGetReadWriteFilePermissionMode(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	result := GetFilePermissionMode("rw")

	assertions.NotNil(result)

	assertions.Equal(MotadataString("read write"), result)

}

func TestSSHClientGetWriteFilePermissionMode(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	result := GetFilePermissionMode("w")

	assertions.NotNil(result)

	assertions.Equal(MotadataString("write"), result)

}

func TestSSHClientGetReadFilePermissionMode(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	result := GetFilePermissionMode("r")

	assertions.NotNil(result)

	assertions.Equal(MotadataString("read"), result)

}

func TestSSHClientGetReadExecuteFilePermissionMode(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	result := GetFilePermissionMode("rx")

	assertions.NotNil(result)

	assertions.Equal(MotadataString("read execute"), result)

}

func TestSSHClientDiscoverProcesses(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	assertions := assert.New(t)

	context := GetContext(contexts, MotadataStringList{consts.Linux, "************"})

	sshClient.SetContext(context, &loggerObj)

	if sshClient.Init() {

		DiscoverProcesses(context, sshClient)

		assertions.NotNil(context[consts.Objects])

		assertions.Contains(context, consts.Status)

		assertions.Equal(context[consts.Status], consts.StatusSucceed)

	} else {

		t.Fail()
	}

	sshClient.Destroy()
}

func TestSSHClientBuildLinuxUnixDiscoveryResult(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	sshClient := &SSHClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{consts.Linux, "************.discovery"})

	assertions := assert.New(t)

	result := sshClient.Discover(context, &loggerObj)

	if result.IsNotEmpty() {

		BuildLinuxUnixDiscoveryResult(context, contexts[consts.ObjectIP], sshClient)

		assertions.True(len(sshClient.GetErrors()) == 0)

		assertions.Contains(context, consts.Objects)

		assertions.NotNil(context[consts.Objects])

		assertions.Equal(context[consts.Status], consts.StatusSucceed)

	}

	sshClient.Destroy()
}

func TestSSHClientGetMetricValue(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}

	assertions := assert.New(t)

	tokens := MotadataStringList{
		"Average:",
		"all",
		"0.75",
		"0.00",
		"99.25",
	}

	result := GetMetricValue(3, -1, tokens)

	assertions.NotNil(result)

	assertions.NotEqual(result, -1)

}

func TestSSHClientSendCommandDirect(t *testing.T) {

	if consts.PathSeparator == consts.WindowsPathSeparator {

		t.Skip()
	}
	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"configSSH", "cisco", "valid.without.protocol"})

	sshClient := &SSHClient{}

	sshClient.SetContext(context, &loggerObj)

	sshClient.InitConnection()

	_ = sshClient.InitSession(context.GetMotadataStringValue(consts.Prompt), context.GetMotadataStringValue(consts.EnablePrompt), context.GetMotadataStringValue(consts.EnableUsername), context.GetMotadataStringValue(consts.EnablePassword))

	assertions := assert.New(t)

	result, err := sshClient.SendCommandDirect(consts.BlankString)

	assertions.NotNil(result)

	assertions.Nil(err)

	sshClient.Destroy()
}

func TestPrePromptPasswordSuccess(t *testing.T) {

	context := make(MotadataMap)

	context[consts.ObjectIP] = "***********"

	context[consts.Port] = 22

	context[consts.Password] = "Mind@123"

	context[consts.UserName] = "admin"

	context[consts.Timeout] = "180"

	sshClient := &SSHClient{}

	sshClient.SetContext(context, &loggerObj)

	assert.True(t, sshClient.InitConnection())

	sshClient.Destroy()
}
