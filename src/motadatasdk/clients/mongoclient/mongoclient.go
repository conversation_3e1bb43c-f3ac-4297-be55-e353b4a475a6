/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package mongoclient

import (
	"context"
	"encoding/json"
	"fmt"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"net/url"
	"runtime"
	"time"
)

type MongoClient struct {
	target        MotadataString
	port          MotadataUINT16
	userName      MotadataString
	password      MotadataString
	dataSource    MotadataString
	connectionURI MotadataString
	logger        *Logger
	errors        []MotadataStringMap
	client        *mongo.Client
	timeout       time.Duration
}

func (mongoClient *MongoClient) Init() (result bool) {

	mongoClient.logger.Debug(MotadataString(fmt.Sprintf("connecting to MongoDB server %s:%d", mongoClient.target, mongoClient.port)))

	result = true

	ctx, cancel := context.WithTimeout(context.Background(), mongoClient.timeout)

	defer cancel()

	var err error

	mongoClient.client, err = mongo.Connect(ctx, options.Client().ApplyURI(mongoClient.connectionURI.ToString()))

	if err != nil {

		mongoClient.logger.Warn(MotadataString(fmt.Sprintf("error %v occurred while creating connection to MongoDB %v", err, mongoClient.connectionURI)))

		mongoClient.appendError(err)

		return false
	}

	// Ping the database to verify connection

	err = mongoClient.client.Ping(ctx, nil)

	if err != nil {

		mongoClient.logger.Warn(MotadataString(fmt.Sprintf("error %v occurred while pinging MongoDB %v", err, mongoClient.connectionURI)))

		mongoClient.appendError(err)

		return false
	}

	mongoClient.logger.Debug(MotadataString(fmt.Sprintf("Connection established to MongoDB server %s:%d", mongoClient.target, mongoClient.port)))

	return
}

func (mongoClient *MongoClient) appendError(err error) {

	msg := err.Error()

	if MotadataString(msg).Contains("AuthenticationFailed") || MotadataString(msg).Contains("auth failed") {

		mongoClient.errors = append(mongoClient.errors, MotadataStringMap{

			consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidCredentials, mongoClient.target, mongoClient.port),

			consts.ErrorCode: consts.ErrorCodeInvalidCredentials,

			consts.Error: msg,
		})
	} else if MotadataString(msg).Contains("no reachable servers") || MotadataString(msg).Contains("connection refused") {

		mongoClient.errors = append(mongoClient.errors, MotadataStringMap{

			consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidPort, mongoClient.port),

			consts.ErrorCode: consts.ErrorCodeInvalidPort,

			consts.Error: msg,
		})
	} else if MotadataString(msg).Contains("no route to host") {

		mongoClient.errors = append(mongoClient.errors, MotadataStringMap{

			consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidHost, mongoClient.target),

			consts.ErrorCode: consts.ErrorCodeInvalidHost,

			consts.Error: msg,
		})
	} else {

		mongoClient.errors = append(mongoClient.errors, MotadataStringMap{

			consts.Message: fmt.Sprintf(consts.ErrorMessageConnectionFailed, "MongoDB", mongoClient.target, mongoClient.port),

			consts.ErrorCode: consts.ErrorCodeConnectionFailed,

			consts.Error: msg,
		})
	}
}

func (mongoClient *MongoClient) ExecuteCommand(command bson.D) (result bson.M, err error) {

	mongoClient.logger.Debug(MotadataString(fmt.Sprintf("Executing command for %s target", mongoClient.target)))

	ctx, cancel := context.WithTimeout(context.Background(), mongoClient.timeout)

	defer cancel()

	err = mongoClient.client.Database("admin").RunCommand(ctx, command).Decode(&result)

	if err != nil {

		mongoClient.logger.Warn(MotadataString(fmt.Sprintf("error %v occurred while executing command for %s target", err, mongoClient.target)))

		mongoClient.errors = append(mongoClient.errors, MotadataStringMap{

			consts.Message: err.Error(),

			consts.ErrorCode: consts.ErrorCodeCommandExecutionFailed,

			consts.Error: err.Error(),
		})
		return nil, err
	}

	return result, nil
}

func (mongoClient *MongoClient) GetServerStatus() (bson.M, error) {

	return mongoClient.ExecuteCommand(bson.D{{"serverStatus", 1}})
}

func (mongoClient *MongoClient) GetErrors() []MotadataStringMap {
	return mongoClient.errors
}

// ListDatabases returns a list of all databases on the MongoDB server
func (mongoClient *MongoClient) GetDatabaseNames() ([]string, error) {

	ctx, cancel := context.WithTimeout(context.Background(), mongoClient.timeout)

	defer cancel()

	names, err := mongoClient.client.ListDatabaseNames(ctx, bson.D{})

	if err != nil {

		mongoClient.logger.Warn(MotadataString(fmt.Sprintf("error %v occurred while listing databases for %s target", err, mongoClient.target)))

		return nil, err
	}

	return names, nil
}

// GetDBStatsForDatabase returns database statistics for a specific database
func (mongoClient *MongoClient) GetDatabaseStats(databaseName string) (bson.M, error) {

	if databaseName == "" {

		err := fmt.Errorf("database name cannot be empty")

		mongoClient.logger.Warn(MotadataString(fmt.Sprintf("error: %v for %s target", err, mongoClient.target)))

		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), mongoClient.timeout)

	defer cancel()

	var stats bson.M

	err := mongoClient.client.Database(databaseName).RunCommand(ctx, bson.D{{"dbStats", 1}}).Decode(&stats)

	if err != nil {

		mongoClient.logger.Warn(MotadataString(fmt.Sprintf("error %v occurred while getting stats for database %s on %s target", err, databaseName, mongoClient.target)))

		return nil, err
	}

	return stats, nil
}

func (mongoClient *MongoClient) setLogger(logger *Logger) *MongoClient {

	mongoClient.logger = logger

	return mongoClient
}

func (mongoClient *MongoClient) Destroy() {

	if mongoClient.client != nil {

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)

		defer cancel()

		_ = mongoClient.client.Disconnect(ctx)

		mongoClient.logger.Debug(MotadataString(fmt.Sprintf(consts.InfoMessageConnectionDestroyed, mongoClient.target, (mongoClient.port).ToString())))
	}
}

func (mongoClient *MongoClient) setTarget(context MotadataMap) *MongoClient {

	mongoClient.target = consts.LocalHost

	if context.Contains(consts.ObjectIP) {

		mongoClient.target = context.GetMotadataStringValue(consts.ObjectIP)
	}

	return mongoClient
}

func (mongoClient *MongoClient) setPort(context MotadataMap) *MongoClient {

	mongoClient.port = 27017

	if context.Contains(consts.Port) {

		mongoClient.port = context.GetUINT16Value(consts.Port)
	}

	return mongoClient
}

func (mongoClient *MongoClient) setUsername(context MotadataMap) *MongoClient {

	if context.Contains(consts.UserName) {

		mongoClient.userName = context.GetMotadataStringValue(consts.UserName)
	}

	return mongoClient
}

func (mongoClient *MongoClient) setPassword(context MotadataMap) *MongoClient {

	if context.Contains(consts.Password) {

		mongoClient.password = context.GetMotadataStringValue(consts.Password)
	}

	return mongoClient
}

func (mongoClient *MongoClient) setTimeout(context MotadataMap) *MongoClient {

	timeout := 30 // default timeout in seconds

	if context.Contains(consts.Timeout) {

		timeout = context.GetIntValue(consts.Timeout)
	}

	mongoClient.timeout = time.Duration(timeout) * time.Second

	return mongoClient
}

func (mongoClient *MongoClient) SetConnectionURI(connectionURI MotadataString) *MongoClient {

	mongoClient.connectionURI = connectionURI

	return mongoClient
}

// GetConnectionURI returns the current connection URI
func (mongoClient *MongoClient) GetConnectionURI() MotadataString {
	return mongoClient.connectionURI
}

func (mongoClient *MongoClient) SetContext(context MotadataMap, logger *Logger) *MongoClient {

	mongoClient.setUsername(context).
		setPassword(context).
		setTarget(context).
		setPort(context).
		setTimeout(context).
		setLogger(logger)

	mongoClient.BuildConnectionURI()

	return mongoClient
}

// BuildConnectionURI builds the MongoDB connection URI based on available fields
func (mongoClient *MongoClient) BuildConnectionURI() *MongoClient {

	var uri string

	// Check if username and password are provided
	if mongoClient.userName.ToString() != "" && mongoClient.password.ToString() != "" {

		uri = fmt.Sprintf(consts.MongoConnectionURI,
			url.QueryEscape(mongoClient.userName.ToString()),
			url.QueryEscape(mongoClient.password.ToString()),
			mongoClient.target.ToString(),
			mongoClient.port)
	} else {
		// No authentication
		uri = fmt.Sprintf(consts.MongoConnectionURINoAuth,
			mongoClient.target.ToString(),
			mongoClient.port)
	}

	mongoClient.connectionURI = MotadataString(uri)

	return mongoClient
}

func (mongoClient *MongoClient) ToJSON(value bson.M) (MotadataMap, error) {

	var result map[string]interface{}

	bytes, _ := json.MarshalIndent(value, "", "  ")

	err := json.Unmarshal(bytes, &result)

	if err != nil {

		return nil, err
	}

	return ToMap(result), nil
}

func SetupCleanupRoutine(mongoClient *MongoClient, responses chan<- MotadataMap, response MotadataMap, requestType MotadataString) {

	if r := recover(); r != nil {

		err := make(MotadataStringMap)

		err[consts.Message] = fmt.Sprintf("%v", r)

		err[consts.ErrorCode] = consts.ErrorCodeInternalError

		err[consts.Error] = fmt.Sprintf("%v", r)

		response[consts.Errors] = append(response.GetStringMapSliceValue(consts.Errors), err)

		stackTraceBytes := make([]byte, consts.StackTraceSizeInBytes)

		PanicLogger.Fatal(MotadataString(fmt.Sprintf("serious error %v occurred in the plugin %v\nStack Trace ==> %v\n", r, RemoveSensitiveFields(response).ToJSON(), string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))))

	}

	mongoClient.Destroy()

	if responses != nil {

		if requestType == consts.EventPoll {

			AddStatusField(response)

		}

		responses <- response

	}

}

func (mongoClient *MongoClient) Discover(context MotadataMap, logger *Logger) (result MotadataMap) {

	result = make(MotadataMap)

	credentialProfiles := context.GetSliceValue(consts.DiscoveryCredentialProfiles)

	if credentialProfiles == nil || len(credentialProfiles) == 0 {

		credentialProfiles = []interface{}{make(MotadataMap)}

	}

	for _, credentialProfile := range credentialProfiles {

		profile := ToMap(credentialProfile)

		profile[consts.ObjectIP] = context.GetMotadataStringValue(consts.ObjectIP)

		profile[consts.Port] = context.GetIntValue(consts.Port)

		profile[consts.UserName] = profile.GetStringValue(consts.UserName)

		profile[consts.Password] = profile.GetStringValue(consts.Password)

		mongoClient.SetContext(profile, logger)

		if mongoClient.Init() {

			result[consts.ObjectCredentialProfile] = profile.GetFloatValue(consts.Id)

			result[consts.CredentialProfileName] = profile.GetStringValue(consts.CredentialProfileName)

			break

		}
	}
	return

}

func (mongoClient *MongoClient) GetCollectionStats(dbName, collName string) (bson.M, error) {

	cmd := bson.D{{"collStats", collName}}

	var result bson.M

	err := mongoClient.client.Database(dbName).RunCommand(context.TODO(), cmd).Decode(&result)

	return result, err
}

// GetCollectionNames returns the list of collection names in the given database.
func (mongoClient *MongoClient) GetCollectionNames(dbName string) ([]string, error) {

	collections, err := mongoClient.client.Database(dbName).ListCollectionNames(context.TODO(), bson.D{})

	if err != nil {
		return nil, err
	}

	return collections, nil
}
