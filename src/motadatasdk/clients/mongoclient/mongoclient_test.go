/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package mongoclient

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"os"
	"path/filepath"
	"testing"
)

var (
	contexts MotadataMap

	_logger = NewLogger("MongoDB", "MongoDB Test")
)

func TestMain(m *testing.M) {

	SetLogLevel(consts.LogLevelDebug)

	consts.CurrentDir = filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(consts.CurrentDir))))

	bytes, _ := os.ReadFile(consts.CurrentDir + consts.PathSeparator + consts.ConfigDirectory + consts.PathSeparator + "context.json")

	err := json.Unmarshal(bytes, &contexts)

	if err != nil {

		panic(err)
	}

	m.Run()
}

func TestMongoClientInvalidCredentials(t *testing.T) {

	mongoClient := &MongoClient{}

	mongoClient = mongoClient.SetContext(GetContext(contexts, MotadataStringList{"mongo-client", "invalid_credentials"}), &_logger)

	assertions := assert.New(t)

	assertions.False(mongoClient.Init(), "mongo client should fail with invalid credentials")

	assertions.True(len(mongoClient.GetErrors()) == 1)

	assertions.Equal(consts.ErrorCodeInvalidCredentials, mongoClient.GetErrors()[0][consts.ErrorCode])

	mongoClient.Destroy()
}

func TestMongoClientConnectionRefused(t *testing.T) {

	mongoClient := &MongoClient{}

	context := GetContext(contexts, MotadataStringList{"mongo-client", "connection_refused"})

	mongoClient = mongoClient.SetContext(context, &_logger)

	assertions := assert.New(t)

	assertions.False(mongoClient.Init(), "mongo client should fail with connection refused")

	assertions.True(len(mongoClient.GetErrors()) == 1)

	assertions.Equal(consts.ErrorCodeInvalidPort, mongoClient.GetErrors()[0][consts.ErrorCode])

	mongoClient.Destroy()
}

func TestMongoClientListDatabases(t *testing.T) {

	mongoClient := &MongoClient{}

	mongoClient = mongoClient.SetContext(GetContext(contexts, MotadataStringList{"mongo-client", "valid"}), &_logger)

	assertions := assert.New(t)

	assertions.True(mongoClient.Init(), "mongo client initialize failed")

	names, err := mongoClient.GetDatabaseNames()

	assertions.Nil(err)

	assertions.NotNil(names)

	assertions.True(len(names) > 0)

}

func TestMongoClientGetDatabaseStats(t *testing.T) {

	mongoClient := &MongoClient{}

	mongoClient = mongoClient.SetContext(GetContext(contexts, MotadataStringList{"mongo-client", "valid"}), &_logger)

	assertions := assert.New(t)

	assertions.True(mongoClient.Init(), "mongo client initialize failed")

	// Test with admin database
	stats, err := mongoClient.GetDatabaseStats("admin")

	assertions.Nil(err)

	assertions.NotNil(stats)

	assertions.True(len(stats) > 0)

}

func TestMongoClientExecuteCommand(t *testing.T) {

	mongoClient := &MongoClient{}

	mongoClient = mongoClient.SetContext(GetContext(contexts, MotadataStringList{"mongo-client", "valid"}), &_logger)

	assertions := assert.New(t)

	assertions.True(mongoClient.Init(), "mongo client initialize failed")

	// Test with ping command

	result, err := mongoClient.ExecuteCommand(bson.D{{"ping", 1}})

	assertions.Nil(err)

	assertions.NotNil(result)

	assertions.True(len(result) > 0)

	mongoClient.Destroy()
}

func TestMongoClientNoAuth(t *testing.T) {

	mongoClient := &MongoClient{}

	mongoClient = mongoClient.SetContext(GetContext(contexts, MotadataStringList{"mongo-client", "no_auth"}), &_logger)

	assertions := assert.New(t)

	assertions.True(mongoClient.Init(), "mongo client initialize failed")

	assertions.True(len(mongoClient.GetErrors()) == 0)

	mongoClient.Destroy()
}

func TestMongoClientWithTimeout(t *testing.T) {

	mongoClient := &MongoClient{}

	context := GetContext(contexts, MotadataStringList{"mongo-client", "valid"})

	context[consts.Timeout] = 60 // 60 seconds timeout

	mongoClient = mongoClient.SetContext(context, &_logger)

	assertions := assert.New(t)

	assertions.True(mongoClient.Init(), "mongo client initialize failed")

	assertions.True(len(mongoClient.GetErrors()) == 0)

	mongoClient.Destroy()
}

func TestMongoClientWithoutTimeout(t *testing.T) {

	mongoClient := &MongoClient{}

	context := GetContext(contexts, MotadataStringList{"mongo-client", "valid"})

	// Remove timeout to test default
	delete(context, consts.Timeout)

	mongoClient = mongoClient.SetContext(context, &_logger)

	assertions := assert.New(t)

	assertions.True(mongoClient.Init(), "mongo client initialize failed")

	assertions.True(len(mongoClient.GetErrors()) == 0)

	mongoClient.Destroy()
}

func TestMongoClientInvalidUsername(t *testing.T) {

	mongoClient := &MongoClient{}

	context := GetContext(contexts, MotadataStringList{"mongo-client", "valid"})

	context[consts.UserName] = consts.InvalidUserName

	mongoClient = mongoClient.SetContext(context, &_logger)

	assertions := assert.New(t)

	assertions.False(mongoClient.Init(), "mongo client should fail with invalid username")

	assertions.Equal(consts.ErrorCodeInvalidCredentials, mongoClient.GetErrors()[0][consts.ErrorCode])

	mongoClient.Destroy()
}

func TestMongoClientInvalidPassword(t *testing.T) {

	mongoClient := &MongoClient{}

	context := GetContext(contexts, MotadataStringList{"mongo-client", "valid"})

	context[consts.Password] = consts.InvalidPassword

	mongoClient = mongoClient.SetContext(context, &_logger)

	assertions := assert.New(t)

	assertions.False(mongoClient.Init(), "mongo client should fail with invalid password")

	assertions.True(len(mongoClient.GetErrors()) == 1)

	assertions.Equal(consts.ErrorCodeInvalidCredentials, mongoClient.GetErrors()[0][consts.ErrorCode])

	mongoClient.Destroy()
}

func TestMongoClientSetContextFields(t *testing.T) {

	mongoClient := &MongoClient{}

	context := MotadataMap{
		consts.ObjectIP: "*************",
		consts.Port:     27018,
		consts.UserName: "testuser",
		consts.Password: "testpass",
		consts.Timeout:  45,
	}

	mongoClient.SetContext(context, &_logger)

	assertions := assert.New(t)

	assertions.Equal("*************", mongoClient.target.ToString())

	assertions.Equal(MotadataUINT16(27018), mongoClient.port)

	assertions.Equal("testuser", mongoClient.userName.ToString())

	assertions.Equal("testpass", mongoClient.password.ToString())

	assertions.NotNil(mongoClient.logger)

	mongoClient.Destroy()
}
