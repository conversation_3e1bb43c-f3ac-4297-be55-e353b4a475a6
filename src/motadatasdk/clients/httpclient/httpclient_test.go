/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package httpclient

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"net/http"
	"os"
	"path/filepath"
	"testing"
)

var (
	contexts MotadataMap

	_logger = NewLogger("HTTP", "HTTP Test")
)

func TestMain(m *testing.M) {

	SetLogLevel(consts.LogLevelDebug)

	consts.CurrentDir = filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(consts.CurrentDir))))

	bytes, _ := os.ReadFile(consts.CurrentDir + consts.PathSeparator + consts.ConfigDirectory + consts.PathSeparator + "context.json")

	err := json.Unmarshal(bytes, &contexts)

	if err != nil {

		panic(err)
	}

	m.Run()
}

func TestHTTPClientInvalidBasicAuth(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_get_client_valid_url_invalid_credentials"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(http.StatusUnauthorized, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientBadRequest(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_client_bad_request"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(URLResponseCodeBadRequest, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientUnauthorizedRequest(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_client_unauthorized_request"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(URLResponseCodeUnauthorized, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientForbiddenRequest(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_client_forbidden_request"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(URLResponseCodeForbidden, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientInternalServerErrorRequest(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_client_internal_server_error_request"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(http.StatusInternalServerError, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

// This test case is for default case if status code is not handled in httpclient
func TestHTTPClientStatusCode_502_Request(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_client_status_code_502_request"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(502, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientTimeout(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_client_timeout"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	httpClient.ExecuteGETRequest()

	assertions.Equal(consts.ErrorCodeTimeout, httpClient.errors[0][consts.ErrorCode])

	httpClient.Destroy()
}

func TestHTTPClientConnectionFailed(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_client_connection_failed"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	httpClient.ExecuteGETRequest()

	assertions.Equal(consts.ErrorCodeConnectionFailed, httpClient.errors[0][consts.ErrorCode])

	httpClient.Destroy()
}

func TestHTTPClientNTLMAuthNoDomain(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_get_client_ntlm_credential_with_domain"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(http.StatusOK, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientNTLMAuthDomainUser(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_https_get_client_ntlm_credential_with_domain"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(http.StatusOK, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientInvalidNTLMAuth(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_invalid_http_get_client_ntlm_credential_with_domain"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(http.StatusUnauthorized, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPGetClientDigestAuthHTTPS(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_client_digest_credential_with_domain"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(http.StatusOK, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPPostClientDigestAuthHTTPS(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_client_digest_credential_with_domain"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecutePOSTRequest()

	assertions.Equal(http.StatusOK, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientNoAuth(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_get_client_without_credentials"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(http.StatusOK, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientWithoutTimeout(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_get_client_invalid_timeout"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(http.StatusOK, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientBasicAuth(t *testing.T) {

	httpClient := &HTTPClient{}

	context := GetContext(contexts, MotadataStringList{"http-client", "test_http_get_client_with_token"})

	httpClient = httpClient.SetContext(context, &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	assertions.Equal(context.GetStringValue("object.target"), httpClient.GetURL().ToString())

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(http.StatusOK, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientClientCertificateAuth(t *testing.T) {

	httpClient := &HTTPClient{}

	context := GetContext(contexts, MotadataStringList{"http-client", "test_https_get_client_certificate_credential"})

	httpClient = httpClient.SetContext(context, &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	assertions.Equal(context.GetStringValue("object.target"), httpClient.GetURL().ToString())

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(http.StatusOK, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientPostRequest(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_post_client_with_data"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(http.StatusOK, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientParams(t *testing.T) {

	httpClient := &HTTPClient{}

	httpClient = httpClient.SetContext(GetContext(contexts, MotadataStringList{"http-client", "test_http_get_client_with_params"}), &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	response := httpClient.ExecuteGETRequest()

	assertions.Equal(http.StatusOK, response.GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestGetNutanixClusterNames(t *testing.T) {

	//Skipping this test case because if we hit nutanix apis frequently, nutanix will add us into block list.
	t.Skip()

	httpClient := &HTTPClient{}

	context := BeforeTest(GetContext(contexts, MotadataStringList{"prism", "valid"}))

	httpClient = httpClient.SetContext(context, &_logger)

	assertions := assert.New(t)

	result := GetNutanixClusterNames(httpClient, context)

	assertions.True(result.IsNotEmpty())

	for key := range result {

		assertions.True(result.GetMotadataStringValue(key).IsNotEmpty())

	}

	httpClient.Destroy()
}

func TestHTTPClientBasicAuthProxyEnabled(t *testing.T) {

	httpClient := &HTTPClient{}

	context := GetContext(contexts, MotadataStringList{"http-client", "test_http_get_client_with_token"})

	context[ProxySettings] = MotadataMap{
		"proxy.server.host":           "************",
		"proxy.server.port":           3128,
		"proxy.server.type":           "HTTP",
		"proxy.server.timeout":        120,
		"proxy.server.authentication": "no",
		"id":                          10000000000001,
		"_type":                       "1",
	}

	httpClient = httpClient.SetContext(context, &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	assertions.Equal(context.GetStringValue("object.target"), httpClient.GetURL().ToString())

	assertions.Equal(http.StatusOK, httpClient.ExecuteGETRequest().GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientNTLMAuthProxyEnabled(t *testing.T) {

	httpClient := &HTTPClient{}

	context := GetContext(contexts, MotadataStringList{"http-client", "test_http_get_client_ntlm_credential_with_domain"})

	context[ProxySettings] = MotadataMap{
		"proxy.server.host":           "************",
		"proxy.server.port":           3128,
		"proxy.server.type":           "HTTP",
		"proxy.server.timeout":        120,
		"proxy.server.authentication": "no",
		"id":                          10000000000001,
		"_type":                       "1",
	}

	httpClient = httpClient.SetContext(context, &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	assertions.Equal(http.StatusOK, httpClient.ExecuteGETRequest().GetIntValue(URLResponseCode))

	httpClient.Destroy()
}

func TestHTTPClientDigestAuthProxyEnabled(t *testing.T) {

	httpClient := &HTTPClient{}

	context := GetContext(contexts, MotadataStringList{"http-client", "test_http_client_digest_credential_with_domain"})

	context[ProxySettings] = MotadataMap{
		"proxy.server.host":           "************",
		"proxy.server.port":           3128,
		"proxy.server.type":           "HTTP",
		"proxy.server.timeout":        120,
		"proxy.server.authentication": "no",
		"id":                          10000000000001,
		"_type":                       "1",
	}

	httpClient = httpClient.SetContext(context, &_logger)

	assertions := assert.New(t)

	assertions.True(httpClient.Init(false), "http client initialize failed")

	assertions.Equal(http.StatusOK, httpClient.ExecuteGETRequest().GetIntValue(URLResponseCode))

	httpClient.Destroy()
}
