/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
 * Change Logs:
 * Date			  Author			Notes
 * 9-May-2025	  Sankalp			MOTADATA-6197: Added TLS ciphers for http connection
 * 15-May-2025    Darshan Parmar    MOTADATA-6256: response code 201 constant added
 * 05-Jun-2025    Darshan Parmar    MOTADATA-6338: log printing encoded request object
 * 20-Jun-2025    Darshan Parmar    MOTADATA-6587: Certificate based authentication support
 * 06-Aug-2025    Darshan Parmar    MOTADATA-7069: CookieJar support for HTTPClient
 * 06-Aug-2025    jenil ka<PERSON>a  MOTADATA-6876: Added method for put and delete
 */

package httpclient

import (
	"bytes"
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"fmt"
	"github.com/icholy/digest"
	httpntlm "github.com/vadimi/go-http-ntlm/v2"
	"io"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"time"
)

const (
	authenticationTypeNTLM = "ntlm"

	authenticationTypeDigest = "digest"

	authenticationTypeBasic = "basic"

	authenticationTypeCertificate = "certificate"

	AuthenticationType = "authentication.type"

	PersistCookies = "persist.cookies"

	JSONURL = "url.json"

	Cookies = "cookies"

	Data = "data"

	URLHeaders = "url.headers"

	ParamKey = "token.key"

	ParamValue = "token.value"

	URLParams = "url.parameters"

	ProxySettings = "proxy"

	URLProtocolHTTPs = "https"

	HTTPContentType = "content-type"

	HTTPGETRequest = "GET"

	HTTPPOSTRequest = "POST"

	HTTPPUTREQUEST = "PUT"

	HTTPDELETEREQUEST = "DELETE"

	URLMethod = "url.method"

	URLProtocol = "url.protocol"

	URLProtocolHTTP = "http"

	URLMethodPost = "post"

	URLResponseContent = "url-content"

	URLResponseBuffer = "url-buffer"

	URLResponseHeaders = "url.response.headers"

	URLResponseCode = "response-code"

	ClientCertificate = "client.certificate"

	ClientKey = "client.key"

	CertificateAuthority = "certificate.authority"

	URLResponseCodeCreated = 201

	URLResponseCodeSuccess = 200

	URLResponseCodeUnauthorized = 401

	URLResponseCodeForbidden = 403

	URLResponseCodeBadRequest = 400

	AccessToken = "access_token"

	TokenType = "token_type"

	HTTP = "http://"

	HTTPS = "https://"

	Cookie = "cookie"

	XCSRFToken = "X-Csrf-Token"
)

var cipherSuites = []uint16{
	tls.TLS_RSA_WITH_AES_128_CBC_SHA,
	tls.TLS_RSA_WITH_AES_256_CBC_SHA,
	tls.TLS_RSA_WITH_AES_128_CBC_SHA256,
	tls.TLS_RSA_WITH_RC4_128_SHA,
	tls.TLS_RSA_WITH_3DES_EDE_CBC_SHA,
	tls.TLS_RSA_WITH_AES_128_CBC_SHA,
	tls.TLS_RSA_WITH_AES_256_CBC_SHA,
	tls.TLS_RSA_WITH_AES_128_CBC_SHA256,
	tls.TLS_RSA_WITH_AES_128_GCM_SHA256,
	tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
	tls.TLS_ECDHE_ECDSA_WITH_RC4_128_SHA,
	tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA,
	tls.TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA,
	tls.TLS_ECDHE_RSA_WITH_RC4_128_SHA,
	tls.TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA,
	tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,
	tls.TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,
	tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256,
	tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,
	tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
	tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
	tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
	tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
	tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
	tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,

	// TLS 1.3 cipher suites.
	tls.TLS_AES_128_GCM_SHA256,
	tls.TLS_AES_256_GCM_SHA384,
	tls.TLS_CHACHA20_POLY1305_SHA256,
}

type HTTPClient struct {
	errors []MotadataStringMap

	errorMessage MotadataString

	exceptions []error

	client *http.Client

	target MotadataString

	port MotadataUINT16

	url MotadataString

	username MotadataString

	password MotadataString

	clientCertificate MotadataString

	clientKey MotadataString

	certificateAuthority MotadataString

	paramKey MotadataString

	paramValue MotadataString

	timeout MotadataUINT

	params MotadataStringMap

	data MotadataStringMap

	headers MotadataMap

	jsonURL bool

	encodeQuery bool

	authType MotadataString

	setCookies bool

	cookies *http.Cookie

	cookieJar http.CookieJar

	proxySettings MotadataMap

	body *bytes.Buffer

	protocol MotadataString

	logger *Logger

	bypassCertificate bool
}

func (httpClient *HTTPClient) SetErrors(errors []MotadataStringMap) *HTTPClient {

	httpClient.errors = errors

	return httpClient
}

func (httpClient *HTTPClient) SetErrorMessage(errorMessage MotadataString) *HTTPClient {

	httpClient.errorMessage = errorMessage

	return httpClient
}

func (httpClient *HTTPClient) SetExceptions(exceptions []error) *HTTPClient {

	httpClient.exceptions = exceptions

	return httpClient
}

func (httpClient *HTTPClient) SetClient(client *http.Client) *HTTPClient {

	httpClient.client = client

	return httpClient
}

func (httpClient *HTTPClient) SetPort(context MotadataMap) *HTTPClient {

	if context.Contains(consts.Port) {

		httpClient.port = context.GetUINT16Value(consts.Port)

	} else {

		httpClient.port = MotadataUINT16(80)
	}

	return httpClient
}

func (httpClient *HTTPClient) SetURL(url MotadataString) *HTTPClient {

	httpClient.url = url

	return httpClient
}

func (httpClient *HTTPClient) GetURL() MotadataString {

	return httpClient.url
}

func (httpClient *HTTPClient) SetUsername(username MotadataString) *HTTPClient {

	httpClient.username = username

	return httpClient
}

func (httpClient *HTTPClient) SetPassword(password MotadataString) *HTTPClient {

	httpClient.password = password

	return httpClient
}

func (httpClient *HTTPClient) SetParamKey(key MotadataString) *HTTPClient {

	httpClient.paramKey = key

	return httpClient
}

func (httpClient *HTTPClient) SetParamValue(value MotadataString) *HTTPClient {

	httpClient.paramValue = value

	return httpClient
}

func (httpClient *HTTPClient) SetTimeout(context MotadataMap) *HTTPClient {

	if context.Contains(consts.Timeout) {

		httpClient.timeout = context.GetUINTValue(consts.Timeout)

	} else {

		httpClient.timeout = MotadataUINT(consts.DefaultTimeout)
	}

	return httpClient
}

func (httpClient *HTTPClient) SetParamsAsMap(params MotadataStringMap) *HTTPClient {

	httpClient.params = params

	return httpClient

}

func (httpClient *HTTPClient) SetParams(params MotadataString) *HTTPClient {

	parameters := make(MotadataStringMap)

	if params.Contains("=") {

		for _, param := range params.Split("&") {

			parameters[param.Split("=")[0].ToString()] = param.Split("=")[1].ToString()
		}
	}

	httpClient.params = parameters

	return httpClient
}

func (httpClient *HTTPClient) SetData(data MotadataStringMap) *HTTPClient {

	httpClient.data = data

	return httpClient
}

func (httpClient *HTTPClient) SetHeaders(headers MotadataMap) *HTTPClient {

	httpClient.headers = headers

	return httpClient
}

func (httpClient *HTTPClient) SetURLType(jsonURL bool) *HTTPClient {

	httpClient.jsonURL = jsonURL

	return httpClient
}

func (httpClient *HTTPClient) SetQueryEncoding(encodeQuery bool) *HTTPClient {

	httpClient.encodeQuery = encodeQuery

	return httpClient
}

func (httpClient *HTTPClient) SetAuthType(authType MotadataString) *HTTPClient {

	httpClient.authType = authType

	return httpClient
}

func (httpClient *HTTPClient) SetBypassCertificate(bypass bool) *HTTPClient {

	httpClient.bypassCertificate = bypass

	return httpClient
}

func (httpClient *HTTPClient) SetCookies(cookies MotadataMap) *HTTPClient {

	httpClient.cookies = &http.Cookie{
		Name:       cookies.GetStringValue("name"),
		Value:      cookies.GetStringValue("value"),
		Path:       cookies.GetStringValue("path"),
		Domain:     cookies.GetStringValue("domain"),
		Expires:    time.Unix(cookies.GetInt64Value("expires"), 0), // requires time in seconds
		RawExpires: cookies.GetStringValue("raw.expires"),
		MaxAge:     cookies.GetIntValue("max.age"),
		Secure:     cookies.GetBoolValue("secure"),
		HttpOnly:   cookies.GetBoolValue("http.only"),
		SameSite:   http.SameSite(cookies.GetIntValue("same.site")),
		Raw:        cookies.GetStringValue("raw"),
	}

	return httpClient
}

func (httpClient *HTTPClient) SetProxySettings(proxySettings MotadataMap) *HTTPClient {

	if proxySettings != nil && proxySettings.IsNotEmpty() {

		httpClient.proxySettings = proxySettings
	}

	return httpClient
}

func (httpClient *HTTPClient) SetBody(payload *bytes.Buffer) *HTTPClient {

	httpClient.body = payload

	return httpClient
}

func (httpClient *HTTPClient) SetBodyAsMap(payload MotadataMap) *HTTPClient {

	buffer := bytes.Buffer{}

	buffer.WriteString(payload.ToJSON().ToString())

	httpClient.body = &buffer

	return httpClient
}

func (httpClient *HTTPClient) SetProtocol(protocol MotadataString) *HTTPClient {

	httpClient.protocol = protocol

	return httpClient
}

func (httpClient *HTTPClient) SetLogger(logger *Logger) *HTTPClient {

	httpClient.logger = logger

	return httpClient
}

func (httpClient *HTTPClient) SetClientCertificate(clientCertificate MotadataString) *HTTPClient {

	httpClient.clientCertificate = clientCertificate

	return httpClient
}

func (httpClient *HTTPClient) SetClientKey(clientKey MotadataString) *HTTPClient {

	httpClient.clientKey = clientKey

	return httpClient
}

func (httpClient *HTTPClient) SetCertificateAuthority(certificateAuthority MotadataString) *HTTPClient {

	httpClient.certificateAuthority = certificateAuthority

	return httpClient
}

func (httpClient *HTTPClient) GetErrors() []MotadataStringMap {

	return httpClient.errors
}

func (httpClient *HTTPClient) Init(setCookies bool) (result bool) {

	httpClient.logger.Debug(MotadataString(fmt.Sprintf("getting http client connection: %v", httpClient.url)))

	if httpClient.timeout == 0 {

		httpClient.timeout = MotadataUINT(consts.DefaultTimeout)
	}

	httpClient.setCookies = setCookies

	return true
}

func (httpClient *HTTPClient) SetContext(context MotadataMap, logger *Logger) *HTTPClient {
	client := httpClient.SetURL(context.GetMotadataStringValue(consts.ObjectTarget)).
		SetTarget(context.GetMotadataStringValue(consts.ObjectIP)).
		SetPort(context).SetUsername(context.GetMotadataStringValue(consts.UserName)).
		SetPassword(context.GetMotadataStringValue(consts.Password)).
		SetParamKey(context.GetMotadataStringValue(ParamKey)).
		SetParamValue(context.GetMotadataStringValue(ParamValue)).
		SetTimeout(context).SetParams(context.GetMotadataStringValue(URLParams)).
		SetData(context.GetStringMapValue(Data)).
		SetHeaders(context.GetMapValue(URLHeaders)).
		SetURLType(context.GetBoolValue(JSONURL)).
		SetQueryEncoding(true).     // In case of we don't want to encode query params followed by '?' we can set this to false. EX: LightHTTPd Plugin
		SetBypassCertificate(true). // In case of we want to check ssl certificate is valid or not EX: URL Plugin
		SetAuthType(context.GetMotadataStringValue(AuthenticationType)).
		SetCookies(context.GetMapValue(Cookies)).
		SetProxySettings(context.GetMapValue(ProxySettings)).
		SetProtocol(context.GetMotadataStringValue(URLProtocol)).
		SetLogger(logger).
		SetClientCertificate(context.GetMotadataStringValue(ClientCertificate)).
		SetClientKey(context.GetMotadataStringValue(ClientKey)).
		SetCertificateAuthority(context.GetMotadataStringValue(CertificateAuthority))

	// Enable cookie jar if requested
	if context.GetBoolValue(PersistCookies) {
		client.EnableCookie()
	}

	return client
}

func (httpClient *HTTPClient) ExecuteGETRequest() MotadataMap {

	httpClient.logger.Debug(MotadataString(fmt.Sprintf("executing http get request: %s", httpClient.url)))

	response := &http.Response{}

	var err error

	if httpClient.authType.ToLowerNative() == authenticationTypeNTLM {

		response, err = httpClient.executeNTLMAuthRequest(HTTPGETRequest, nil)

	} else if httpClient.authType == authenticationTypeDigest {

		response, err = httpClient.executeDigestAuthRequest(HTTPGETRequest, nil)

	} else if httpClient.authType == authenticationTypeBasic {

		response, err = httpClient.executeBasicAuthRequest(HTTPGETRequest, nil)

	} else if httpClient.authType == authenticationTypeCertificate {

		response, err = httpClient.executeCertificateAuthRequest(HTTPGETRequest, nil)

	} else {

		response, err = httpClient.executeNoAuthRequest(HTTPGETRequest, nil)
	}

	return httpClient.buildResponse(response, err)
}

func (httpClient *HTTPClient) ExecutePOSTRequest() MotadataMap {

	httpClient.logger.Debug(MotadataString(fmt.Sprintf("executing http post request: %s", httpClient.url)))

	response := &http.Response{}

	var err error

	if httpClient.authType == authenticationTypeNTLM {

		response, err = httpClient.executeNTLMAuthRequest(HTTPPOSTRequest, httpClient.body)

	} else if httpClient.authType == authenticationTypeDigest {

		response, err = httpClient.executeDigestAuthRequest(HTTPPOSTRequest, httpClient.body)

	} else if httpClient.authType == authenticationTypeBasic {

		response, err = httpClient.executeBasicAuthRequest(HTTPPOSTRequest, httpClient.body)

	} else if httpClient.authType == authenticationTypeCertificate {

		response, err = httpClient.executeCertificateAuthRequest(HTTPPOSTRequest, httpClient.body)

	} else {

		response, err = httpClient.executeNoAuthRequest(HTTPPOSTRequest, httpClient.body)

	}

	return httpClient.buildResponse(response, err)
}

func (httpClient *HTTPClient) ExecutePUTRequest() MotadataMap {

	httpClient.logger.Debug(MotadataString(fmt.Sprintf("executing http post request: %s", httpClient.url)))

	response := &http.Response{}

	var err error

	if httpClient.authType == authenticationTypeNTLM {

		response, err = httpClient.executeNTLMAuthRequest(HTTPPUTREQUEST, httpClient.body)

	} else if httpClient.authType == authenticationTypeDigest {

		response, err = httpClient.executeDigestAuthRequest(HTTPPUTREQUEST, httpClient.body)

	} else if httpClient.authType == authenticationTypeBasic {

		response, err = httpClient.executeBasicAuthRequest(HTTPPUTREQUEST, httpClient.body)

	} else if httpClient.authType == authenticationTypeCertificate {

		response, err = httpClient.executeCertificateAuthRequest(HTTPPUTREQUEST, httpClient.body)

	} else {

		response, err = httpClient.executeNoAuthRequest(HTTPPUTREQUEST, httpClient.body)

	}

	return httpClient.buildResponse(response, err)
}

func (httpClient *HTTPClient) ExecuteDeleteRequest() MotadataMap {

	httpClient.logger.Debug(MotadataString(fmt.Sprintf("executing http post request: %s", httpClient.url)))

	response := &http.Response{}

	var err error

	if httpClient.authType == authenticationTypeNTLM {

		response, err = httpClient.executeNTLMAuthRequest(HTTPDELETEREQUEST, httpClient.body)

	} else if httpClient.authType == authenticationTypeDigest {

		response, err = httpClient.executeDigestAuthRequest(HTTPDELETEREQUEST, httpClient.body)

	} else if httpClient.authType == authenticationTypeBasic {

		response, err = httpClient.executeBasicAuthRequest(HTTPDELETEREQUEST, httpClient.body)

	} else if httpClient.authType == authenticationTypeCertificate {

		response, err = httpClient.executeCertificateAuthRequest(HTTPDELETEREQUEST, httpClient.body)

	} else {

		response, err = httpClient.executeNoAuthRequest(HTTPDELETEREQUEST, httpClient.body)

	}

	return httpClient.buildResponse(response, err)
}

func (httpClient *HTTPClient) executeBasicAuthRequest(requestType MotadataString, body *bytes.Buffer) (response *http.Response, err error) {

	var request *http.Request

	if httpClient.client == nil {

		httpClient.client = &http.Client{}

	}

	// Set cookie jar if enabled
	if httpClient.cookieJar != nil {
		httpClient.client.Jar = httpClient.cookieJar
	}

	httpClient.client.Timeout = time.Duration(httpClient.timeout.ToInt()) * time.Second

	if httpClient.url.HasPrefix(URLProtocolHTTPs) {

		httpClient.client.Transport = &http.Transport{

			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: httpClient.bypassCertificate, // WARNING: Disables certificate validation (use only if needed)
				CipherSuites:       cipherSuites,
				MinVersion:         tls.VersionTLS10,
				Renegotiation:      tls.RenegotiateFreelyAsClient,
			},
		}

	} else {

		httpClient.client.Transport = &http.Transport{}
	}

	if httpClient.proxySettings != nil && httpClient.proxySettings.IsNotEmpty() {

		proxyURL := httpClient.getProxyURL()

		if httpClient.client.Transport != nil {

			httpClient.client.Transport.(*http.Transport).Proxy = http.ProxyURL(proxyURL)

		} else {

			httpClient.client.Transport = &http.Transport{Proxy: http.ProxyURL(proxyURL)}
		}

		httpClient.logger.Debug(MotadataString(fmt.Sprintf(consts.DebugMessageProxyServerRequest, httpClient.url, proxyURL)))

	}

	if body != nil {

		request, err = http.NewRequest(requestType.ToString(), httpClient.url.ToString(), body)

	} else {

		request, err = http.NewRequest(requestType.ToString(), httpClient.url.ToString(), nil)
	}

	request.SetBasicAuth(httpClient.username.ToString(), httpClient.password.ToString())

	httpClient.buildRequest(request)

	return httpClient.client.Do(request)
}

func (httpClient *HTTPClient) executeCertificateAuthRequest(requestType MotadataString, body *bytes.Buffer) (response *http.Response, err error) {

	var request *http.Request

	if httpClient.client == nil {

		httpClient.client = &http.Client{}

	}

	// Set cookie jar if enabled
	if httpClient.cookieJar != nil {
		httpClient.client.Jar = httpClient.cookieJar
	}

	httpClient.client.Timeout = time.Duration(httpClient.timeout.ToInt()) * time.Second

	if httpClient.url.HasPrefix(URLProtocolHTTPs) {

		clientCertificate, err := tls.X509KeyPair([]byte(httpClient.clientCertificate), []byte(httpClient.clientKey))

		if err != nil {

			httpClient.logger.Debug(MotadataString(fmt.Sprintf(consts.ErrorMessageFailedToParseCertificate, httpClient.url)))

			return nil, err
		}

		certPool := x509.NewCertPool()

		ok := certPool.AppendCertsFromPEM([]byte(httpClient.certificateAuthority))

		if !ok {
			httpClient.logger.Debug(MotadataString(fmt.Sprintf(consts.ErrorMessageFailedToParseCACertificate, httpClient.url)))

			return nil, fmt.Errorf("error occurred while parsing CA certificate")
		}

		httpClient.client.Transport = &http.Transport{

			TLSClientConfig: &tls.Config{
				CipherSuites:  cipherSuites,
				MinVersion:    tls.VersionTLS10,
				Renegotiation: tls.RenegotiateFreelyAsClient,
				Certificates:  []tls.Certificate{clientCertificate},
				RootCAs:       certPool,
			},
		}

	} else {

		httpClient.client.Transport = &http.Transport{}
	}

	if httpClient.proxySettings != nil && httpClient.proxySettings.IsNotEmpty() {

		proxyURL := httpClient.getProxyURL()

		if httpClient.client.Transport != nil {

			httpClient.client.Transport.(*http.Transport).Proxy = http.ProxyURL(proxyURL)

		} else {

			httpClient.client.Transport = &http.Transport{Proxy: http.ProxyURL(proxyURL)}
		}

		httpClient.logger.Debug(MotadataString(fmt.Sprintf(consts.DebugMessageProxyServerRequest, httpClient.url, proxyURL)))

	}

	if body != nil {

		request, err = http.NewRequest(requestType.ToString(), httpClient.url.ToString(), body)

	} else {

		request, err = http.NewRequest(requestType.ToString(), httpClient.url.ToString(), nil)
	}

	httpClient.buildRequest(request)

	return httpClient.client.Do(request)
}

func (httpClient *HTTPClient) executeNTLMAuthRequest(requestType MotadataString, body *bytes.Buffer) (response *http.Response, err error) {

	var request *http.Request

	if httpClient.client == nil {

		httpClient.client = &http.Client{}

	}

	httpClient.client.Timeout = time.Duration(httpClient.timeout.ToInt()) * time.Second

	ntlmTransport := &httpntlm.NtlmTransport{}

	httpClient.client.Transport = ntlmTransport

	// Set cookie jar if enabled
	if httpClient.cookieJar != nil {
		httpClient.client.Jar = httpClient.cookieJar
	}

	if httpClient.url.HasPrefix(URLProtocolHTTPs) {

		ntlmTransport.RoundTripper = &http.Transport{

			TLSClientConfig: &tls.Config{
				CipherSuites:       cipherSuites,
				MinVersion:         tls.VersionTLS10,
				InsecureSkipVerify: httpClient.bypassCertificate,
				Renegotiation:      tls.RenegotiateFreelyAsClient,
			},
		}

	}

	if httpClient.username.Contains("\\") {

		ntlmTransport.Domain = httpClient.username.Split("\\")[0].ToString()

		ntlmTransport.User = httpClient.username.Split("\\")[1].ToString()

		ntlmTransport.Password = httpClient.password.ToString()

	} else {

		ntlmTransport.User = httpClient.username.ToString()

		ntlmTransport.Password = httpClient.password.ToString()

	}

	if httpClient.proxySettings != nil && httpClient.proxySettings.IsNotEmpty() {

		proxyURL := httpClient.getProxyURL()

		if httpClient.client.Transport.(*httpntlm.NtlmTransport).RoundTripper != nil {

			httpClient.client.Transport.(*httpntlm.NtlmTransport).RoundTripper.(*http.Transport).Proxy = http.ProxyURL(proxyURL)

		} else {

			httpClient.client.Transport.(*httpntlm.NtlmTransport).RoundTripper = &http.Transport{

				Proxy: http.ProxyURL(proxyURL)}
		}

		httpClient.logger.Debug(MotadataString(fmt.Sprintf(consts.DebugMessageProxyServerRequest, httpClient.url, proxyURL)))

	}

	if body != nil {

		request, err = http.NewRequest(requestType.ToString(), httpClient.url.ToString(), body)

	} else {

		request, err = http.NewRequest(requestType.ToString(), httpClient.url.ToString(), nil)
	}

	httpClient.buildRequest(request)

	return httpClient.client.Do(request)
}

func (httpClient *HTTPClient) executeDigestAuthRequest(requestType MotadataString, body *bytes.Buffer) (response *http.Response, err error) {

	var request *http.Request

	if httpClient.client == nil {

		httpClient.client = &http.Client{}

	}

	httpClient.client.Timeout = time.Duration(httpClient.timeout.ToInt()) * time.Second

	digestTransport := &digest.Transport{
		Username: httpClient.username.ToString(),
		Password: httpClient.password.ToString(),
	}

	// Set cookie jar for digest auth
	if httpClient.cookieJar != nil {
		digestTransport.Jar = httpClient.cookieJar
	}

	httpClient.client.Transport = digestTransport

	if httpClient.url.HasPrefix(URLProtocolHTTPs) {

		digestTransport.Transport = &http.Transport{

			TLSClientConfig: &tls.Config{
				CipherSuites:       cipherSuites,
				MinVersion:         tls.VersionTLS10,
				InsecureSkipVerify: httpClient.bypassCertificate,
				Renegotiation:      tls.RenegotiateFreelyAsClient,
			},
		}

	}

	if httpClient.proxySettings != nil && httpClient.proxySettings.IsNotEmpty() {

		proxyURL := httpClient.getProxyURL()

		if httpClient.client.Transport.(*digest.Transport).Transport != nil {

			httpClient.client.Transport.(*digest.Transport).Transport.(*http.Transport).Proxy = http.ProxyURL(proxyURL)

		} else {

			httpClient.client.Transport.(*digest.Transport).Transport = &http.Transport{Proxy: http.ProxyURL(proxyURL)}
		}
		httpClient.logger.Debug(MotadataString(fmt.Sprintf(consts.DebugMessageProxyServerRequest, httpClient.url, proxyURL)))

	}

	if body != nil {

		request, err = http.NewRequest(requestType.ToString(), httpClient.url.ToString(), body)

	} else {

		request, err = http.NewRequest(requestType.ToString(), httpClient.url.ToString(), nil)
	}

	httpClient.buildRequest(request)

	return httpClient.client.Do(request)
}

func (httpClient *HTTPClient) executeNoAuthRequest(requestType MotadataString, body *bytes.Buffer) (response *http.Response, err error) {

	var request *http.Request

	if httpClient.client == nil {

		httpClient.client = &http.Client{}
	}

	// Set cookie jar if enabled
	if httpClient.cookieJar != nil {
		httpClient.client.Jar = httpClient.cookieJar
	}

	httpClient.client.Timeout = time.Duration(httpClient.timeout.ToInt()) * time.Second

	if httpClient.url.HasPrefix(URLProtocolHTTPs) {

		httpClient.client.Transport = &http.Transport{

			TLSClientConfig: &tls.Config{
				CipherSuites:       cipherSuites,
				MinVersion:         tls.VersionTLS10,
				InsecureSkipVerify: httpClient.bypassCertificate,
				Renegotiation:      tls.RenegotiateFreelyAsClient,
			},
		}

	}

	if httpClient.proxySettings != nil && httpClient.proxySettings.IsNotEmpty() {

		proxyURL := httpClient.getProxyURL()

		if httpClient.client.Transport != nil {

			httpClient.client.Transport.(*http.Transport).Proxy = http.ProxyURL(proxyURL)

		} else {

			httpClient.client.Transport = &http.Transport{Proxy: http.ProxyURL(proxyURL)}
		}
		httpClient.logger.Debug(MotadataString(fmt.Sprintf(consts.DebugMessageProxyServerRequest, httpClient.url, proxyURL)))

	}

	if body != nil {

		request, err = http.NewRequest(requestType.ToString(), httpClient.url.ToString(), body)

	} else {

		request, err = http.NewRequest(requestType.ToString(), httpClient.url.ToString(), nil)
	}

	httpClient.buildRequest(request)

	return httpClient.client.Do(request)
}

func (httpClient *HTTPClient) buildRequest(request *http.Request) {

	if httpClient.encodeQuery {

		query := request.URL.Query()

		if httpClient.params.IsNotEmpty() {

			for key, value := range httpClient.params {

				query.Add(key, value)

			}
		}

		request.URL.RawQuery = query.Encode()

	}

	request.Header.Set("User-Agent", "Mozilla")

	if httpClient.setCookies {

		request.AddCookie(httpClient.cookies)
	}

	if httpClient.paramKey.IsNotEmpty() && httpClient.paramValue.IsNotEmpty() {

		request.Header.Set("Authorization", httpClient.paramKey.ToString()+" "+httpClient.paramValue.ToString())
	}

	if httpClient.headers.IsNotEmpty() {

		for key, value := range httpClient.headers {

			request.Header.Set(key, ToString(value))
		}
	}

	if httpClient.jsonURL {

		request.Header.Set("Content-Type", "application/json")
	}

	httpClient.logger.Debug(MotadataString(fmt.Sprintf("request object of url %v is %v", httpClient.url, Encode(fmt.Sprintf("%v", request)))))
}

func (httpClient *HTTPClient) buildResponse(response *http.Response, err error) MotadataMap {

	result := make(MotadataMap)

	if response != nil {

		httpClient.logger.Trace(MotadataString(fmt.Sprintf("response body of url %v is %v", httpClient.url, response)))

		buffer, err := io.ReadAll(response.Body)

		defer response.Body.Close()

		if err == nil {

			var content MotadataMap

			_ = json.Unmarshal(buffer, &content)

			if MotadataINT(response.StatusCode).ToString().HasPrefix("1") || MotadataINT(response.StatusCode).ToString().HasPrefix("2") || MotadataINT(response.StatusCode).ToString().HasPrefix("3") {

				result[URLResponseCode] = response.StatusCode

				result[URLResponseContent] = content

				result[URLResponseBuffer] = MotadataString(buffer)

				// This take only one header , at bottom all header are included "result[URLResponseHeaders] = response.Header"
				/*result[URLHeaders] = MotadataStringMap{
					ContentType: response.Header.Get("Content-Type"),
				}*/

				result[URLResponseHeaders] = response.Header

				result[URLResponseHeaders] = response.Header

			} else {

				switch response.StatusCode {

				case 400: // Bad Request

					result[URLResponseCode] = URLResponseCodeBadRequest

					httpClient.errors = append(httpClient.errors, MotadataStringMap{

						consts.Error: string(buffer),

						consts.ErrorCode: consts.ErrorCodeBadRequest,

						consts.Message: "Bad Request",
					})

				case 401: // Unauthorized

					result[URLResponseCode] = URLResponseCodeUnauthorized

					httpClient.errors = append(httpClient.errors, MotadataStringMap{

						consts.Error: string(buffer),

						consts.ErrorCode: consts.ErrorCodeInvalidCredentials,

						consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidCredentials, httpClient.url, httpClient.port.ToInt()),
					})

				case 403: // Forbidden

					result[URLResponseCode] = URLResponseCodeForbidden

					httpClient.errors = append(httpClient.errors, MotadataStringMap{

						consts.Error: string(buffer),

						consts.ErrorCode: consts.ErrorCodeUnAuthorizedURLAccess,

						consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidURLPrivilege, httpClient.url, httpClient.url),
					})

				case 500: //  Internal Server Error

					result[URLResponseCode] = http.StatusInternalServerError

					httpClient.errors = append(httpClient.errors, MotadataStringMap{

						consts.Error: string(buffer),

						consts.ErrorCode: consts.ErrorCodeInternalError,

						consts.Message: string(buffer),
					})

				default:

					result[URLResponseCode] = response.StatusCode

					httpClient.errors = append(httpClient.errors, MotadataStringMap{

						consts.Error: string(buffer),

						consts.ErrorCode: consts.ErrorCodeBadResponse,

						consts.Message: fmt.Sprintf(consts.ErrorMessageBadResponse, httpClient.url),
					})

				}

			}

		} else {

			httpClient.logger.Warn(MotadataString(fmt.Sprintf("error %v occurred while reading response %v", err, httpClient.url)))

			result[URLResponseCode] = response.StatusCode

			httpClient.errors = append(httpClient.errors, MotadataStringMap{

				consts.Error: err.Error(),

				consts.ErrorCode: consts.ErrorCodeInternalError,

				consts.Message: fmt.Sprintf(consts.ErrorMessageBadResponse, httpClient.url),
			})

		}

	}

	if err != nil {

		httpClient.logger.Warn(MotadataString(fmt.Sprintf("error %v occurred while fetching url %v", err, httpClient.url)))

		if MotadataString(err.Error()).Contains("(Client.Timeout exceeded while awaiting headers)") || MotadataString(err.Error()).Contains("i/o timeout") || MotadataString(err.Error()).Contains("has timed out") {

			httpClient.errors = append(httpClient.errors, MotadataStringMap{

				consts.Error: err.Error(),

				consts.ErrorCode: consts.ErrorCodeTimeout,

				consts.Message: fmt.Sprintf(consts.ErrorMessageConnectionTimeout, httpClient.url, httpClient.target, httpClient.port),
			})

		} else if MotadataString(err.Error()).Contains("failed to verify certificate") {

			httpClient.errors = append(httpClient.errors, MotadataStringMap{

				consts.Error: err.Error(),

				consts.ErrorCode: consts.ErrorCodeSSLVerificationFailed,

				consts.Message: fmt.Sprintf(consts.ErrorMessageSSLVerificationFailed, httpClient.url),
			})

		} else {

			httpClient.errors = append(httpClient.errors, MotadataStringMap{

				consts.Error: err.Error(),

				consts.ErrorCode: consts.ErrorCodeConnectionFailed,

				consts.Message: fmt.Sprintf(consts.ErrorMessageConnectionFailed, httpClient.url, httpClient.target, httpClient.port),
			})

		}

	}

	httpClient.logger.Trace(MotadataString(fmt.Sprintf("url %v response %v errors %v", httpClient.url, result, httpClient.GetErrors())))

	return result
}

func (httpClient *HTTPClient) getProxyURL() (proxyURL *url.URL) {

	host := consts.LocalHost

	port := 3128

	if httpClient.proxySettings["proxy.server.host"] != nil && httpClient.proxySettings["proxy.server.port"] != nil {

		host = ToString(httpClient.proxySettings["proxy.server.host"])

		port = ToInt(httpClient.proxySettings["proxy.server.port"])

	}

	proxyURLString := fmt.Sprintf("http://%s:%d", host, port)

	if httpClient.proxySettings["proxy.server.username"] != nil && httpClient.proxySettings["proxy.server.password"] != nil {

		proxyURLString = fmt.Sprintf("http://%s:%s@%s:%d", ToString(httpClient.proxySettings["proxy.server.username"]), ToString(httpClient.proxySettings["proxy.server.password"]), host, port)
	}

	proxyURL, err := url.Parse(proxyURLString)

	if err != nil {

		httpClient.logger.Info(MotadataString(err.Error()))
	}

	return
}

func (httpClient *HTTPClient) Destroy() {

	if httpClient.client != nil {

		httpClient.client.CloseIdleConnections()
	}
}

func (httpClient *HTTPClient) SetTarget(target MotadataString) *HTTPClient {

	httpClient.target = target

	return httpClient

}

func (httpClient *HTTPClient) GetTarget() MotadataString {

	return httpClient.target

}

func (httpClient *HTTPClient) SetCookie(cookie http.CookieJar) *HTTPClient {

	httpClient.cookieJar = cookie

	return httpClient
}

func (httpClient *HTTPClient) EnableCookie() *HTTPClient {

	cookie, _ := cookiejar.New(nil)

	httpClient.cookieJar = cookie

	return httpClient
}
