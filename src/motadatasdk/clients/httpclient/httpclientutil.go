/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
 * Change Logs:
 *  Date			Author			Notes
 *  5-May-2025     Darshan Parmar  MOTADATA-6084: method for hpe to set session key for hpe primera and hpe 3PAR
 *  15-May-2025    Darshan Parmar  MOTADATA-6256: response code changed in method for hpe to set session key for hpe primera and hpe 3PAR
 */

package httpclient

import (
	"fmt"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"net/http"
	"runtime"
)

func SetupCleanupRoutine(httpClient *HTTPClient, responses chan<- MotadataMap, response MotadataMap, requestType MotadataString) {

	if r := recover(); r != nil {

		err := make(MotadataStringMap)

		err[consts.Message] = fmt.Sprintf("%v", r)

		err[consts.ErrorCode] = consts.ErrorCodeInternalError

		err[consts.Error] = fmt.Sprintf("%v", r)

		response[consts.Errors] = append(response.GetStringMapSliceValue(consts.Errors), err)

		stackTraceBytes := make([]byte, consts.StackTraceSizeInBytes)

		PanicLogger.Fatal(MotadataString(fmt.Sprintf("serious error %v occurred in the plugin %v\nStack Trace ==> %v\n", r, RemoveSensitiveFields(response).ToJSON(), string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))))
	}

	httpClient.Destroy()

	if responses != nil {

		if requestType == consts.EventPoll {

			AddStatusField(response)

		}

		responses <- response
	}

}

func GetNutanixClusterNames(httpClient *HTTPClient, context MotadataMap) MotadataMap {

	httpClient.SetURL(MotadataString(fmt.Sprintf("https://%s:%s/PrismGateway/services/rest/v2.0/clusters/", httpClient.GetTarget(), context.GetMotadataStringValue(consts.Port))))

	output := httpClient.ExecuteGETRequest()

	clusters := make(MotadataMap)

	if output.IsNotEmpty() && output.Contains(URLResponseCode) && output.GetINTValue(URLResponseCode) == URLResponseCodeSuccess && output.GetMapValue(URLResponseContent).IsNotEmpty() {

		for _, element := range output.GetMapValue(URLResponseContent).GetSliceValue("entities") {

			element := ToMap(element)

			if element.Contains("uuid") && element["uuid"] != nil && element.Contains("name") && element["name"] != nil {

				clusters[element.GetStringValue("uuid")] = element.GetStringValue("name")
			}
		}
	}

	return clusters
}

/*
Getting Cisco ACI token and setting it to http Header.
*/
func SetCiscoACIToken(httpClient *HTTPClient, context MotadataMap) {

	response := httpClient.SetURL(MotadataString(fmt.Sprintf("%s://%s:%s/api/aaaLogin.json", context.GetMotadataStringValue(URLProtocol), context.GetMotadataStringValue(consts.ObjectIP), context.GetMotadataStringValue(consts.Port)))).SetBodyAsMap(MotadataMap{"aaaUser": MotadataMap{"attributes": MotadataMap{"name": context.GetStringValue("username"), "pwd": context.GetStringValue("password")}}}).
		ExecutePOSTRequest()

	response = RetryIfTimedOut(httpClient, response)

	if response.IsNotEmpty() && response.Contains(URLResponseCode) && response.GetIntValue(URLResponseCode) == URLResponseCodeSuccess && response.GetMapValue(URLResponseContent).IsNotEmpty() && response.GetMapValue(URLResponseContent).Contains("imdata") && len(response.GetMapValue(URLResponseContent).GetSliceValue("imdata")) > 0 && ToMap(response.GetMapValue(URLResponseContent).GetSliceValue("imdata")[0]).GetMapValue("aaaLogin").GetMapValue("attributes").Contains("token") && ToMap(response.GetMapValue(URLResponseContent).GetSliceValue("imdata")[0]).GetMapValue("aaaLogin").GetMapValue("attributes")["token"] != nil {

		httpClient.SetHeaders(MotadataMap{Cookie: response[URLResponseHeaders].(http.Header).Get("Set-Cookie")})
	}

}

/*
	 	Cisco APIC allows a maximum of 90 seconds to respond to any query that possibly timed out due to having
		too many activities. In this case, the Cisco APIC responds with "destination not available" because
		the destination could not finish the request in 90 seconds.
*/
func RetryIfTimedOut(httpClient *HTTPClient, response MotadataMap) MotadataMap {

	if response.IsNotEmpty() && response.GetMapValue(URLResponseContent).IsNotEmpty() && response.GetMapValue(URLResponseContent).Contains("imdata") && len(response.GetMapValue(URLResponseContent).GetSliceValue("imdata")) > 0 && response.GetMapValue(URLResponseContent).Contains("imdata") && len(response.GetSliceValue("imdata")) > 0 && ToMap(response.GetSliceValue("imdata")[0]).GetMapValue("error").GetMotadataStringValue("text").Contains("destination not available") {

		response = httpClient.ExecuteGETRequest()
	}

	return response
}

func SetHPESessionToken(httpClient *HTTPClient, context MotadataMap) bool {

	httpClient.SetURL(MotadataString(fmt.Sprintf("%s://%s:%s/api/v1/credentials", context.GetMotadataStringValue(URLProtocol), httpClient.GetTarget(), context.GetMotadataStringValue(consts.Port)))).SetHeaders(MotadataMap{"Content-Type": "application/json"})

	httpClient.SetBodyAsMap(MotadataMap{"user": context.GetMotadataStringValue(consts.UserName), consts.Password: context.GetMotadataStringValue(consts.Password)})

	response := httpClient.ExecutePOSTRequest()

	if response.IsNotEmpty() && response.Contains(URLResponseCode) && (response.GetINTValue(URLResponseCode) == URLResponseCodeSuccess || response.GetINTValue(URLResponseCode) == URLResponseCodeCreated) && response.Contains(URLResponseContent) && response.GetMapValue(URLResponseContent).IsNotEmpty() && response.GetMapValue(URLResponseContent).Contains(consts.Key) && response.GetMapValue(URLResponseContent).GetMotadataStringValue(consts.Key).IsNotEmpty() {

		httpClient.SetHeaders(MotadataMap{"X-HP3PAR-WSAPI-SessionKey": response.GetMapValue(URLResponseContent).GetMotadataStringValue(consts.Key)})

		return true
	}

	return false
}
