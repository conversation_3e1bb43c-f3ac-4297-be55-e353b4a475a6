/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package vmwareclient

import (
	contextlib "context"
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"os"
	"path/filepath"
	"regexp"
	"testing"
	"time"
)

var (
	context MotadataMap

	contexts MotadataMap

	loggerObj = NewLogger("Test/VMware Client", "VMware Client")
)

const (
	Node = "vcenter.node"
)

func TestMain(m *testing.M) {

	SetLogLevel(consts.LogLevelDebug)

	bytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(consts.CurrentDir)))) + consts.PathSeparator + consts.ConfigDirectory + consts.PathSeparator + "context.json")

	if err == nil {

		contexts = make(MotadataMap)

		err = json.Unmarshal(bytes, &contexts)

		if err == nil {

			context = GetContext(contexts, MotadataStringList{"esxi", "valid"})

			m.Run()

		}
	}
}

func TestVMMareClientInvalidHost(t *testing.T) {

	vmwareClient := &VMwareClient{}

	assertion := assert.New(t)

	invalidContext := context.Copy()

	invalidContext[consts.ObjectIP] = consts.InvalidHost

	vmwareClient.SetContext(invalidContext, &loggerObj)

	ctx, cancel := contextlib.WithTimeout(contextlib.Background(), time.Duration(invalidContext.GetIntValue(consts.Timeout))*time.Second)

	defer cancel()

	assertion.False(vmwareClient.Init(ctx))

	assertion.True(len(vmwareClient.GetErrors()) == 1)

	assertion.Equal(consts.ErrorCodeInvalidPort, vmwareClient.GetErrors()[0][consts.ErrorCode])

}

func TestVMMareClientInvalidUsername(t *testing.T) {

	vmwareClient := &VMwareClient{}

	assertion := assert.New(t)

	invalidContext := context.Copy()

	invalidContext[consts.UserName] = consts.InvalidUserName

	vmwareClient.SetContext(invalidContext, &loggerObj)

	ctx, cancel := contextlib.WithTimeout(contextlib.Background(), time.Duration(invalidContext.GetIntValue(consts.Timeout))*time.Second)

	defer cancel()

	assertion.False(vmwareClient.Init(ctx))

	assertion.True(len(vmwareClient.GetErrors()) == 1)

	assertion.Equal(consts.ErrorCodeInvalidCredentials, vmwareClient.GetErrors()[0][consts.ErrorCode])

}

func TestVMMareClientInvalidPassword(t *testing.T) {

	vmwareClient := &VMwareClient{}

	assertion := assert.New(t)

	invalidContext := context.Copy()

	invalidContext[consts.Password] = consts.InvalidPassword

	vmwareClient.SetContext(invalidContext, &loggerObj)

	ctx, cancel := contextlib.WithTimeout(contextlib.Background(), time.Duration(invalidContext.GetIntValue(consts.Timeout))*time.Second)

	defer cancel()

	assertion.False(vmwareClient.Init(ctx))

	assertion.True(len(vmwareClient.GetErrors()) == 1)

	assertion.Equal(consts.ErrorCodeInvalidCredentials, vmwareClient.GetErrors()[0][consts.ErrorCode])

}

func TestVMMareClientInvalidPort(t *testing.T) {

	vmwareClient := &VMwareClient{}

	assertion := assert.New(t)

	invalidContext := context.Copy()

	invalidContext[consts.Port] = consts.InvalidPort

	vmwareClient.SetContext(invalidContext, &loggerObj)

	ctx, cancel := contextlib.WithTimeout(contextlib.Background(), time.Duration(invalidContext.GetIntValue(consts.Timeout))*time.Second)

	defer cancel()

	assertion.False(vmwareClient.Init(ctx))

	assertion.True(len(vmwareClient.GetErrors()) == 1)

	assertion.Equal(consts.ErrorCodeInvalidPort, vmwareClient.GetErrors()[0][consts.ErrorCode])

}

func TestVMMareClient(t *testing.T) {

	vmwareClient := &VMwareClient{}

	assertion := assert.New(t)

	context := context.Copy()

	vmwareClient.SetContext(context, &loggerObj)

	ctx, cancel := contextlib.WithTimeout(contextlib.Background(), time.Duration(context.GetIntValue(consts.Timeout))*time.Second)

	defer cancel()

	assertion.True(vmwareClient.Init(ctx))

	assertion.True(len(vmwareClient.GetErrors()) == 0)

	vmwareClient.client.Logout(ctx)

}

func TestVMWareClientDiscoverESXi(t *testing.T) {

	context = GetContext(contexts, MotadataStringList{"esxi.discovery", "valid"})

	vmwareClient := &VMwareClient{}

	ctx, cancel := contextlib.WithTimeout(contextlib.Background(), time.Duration(context.GetIntValue(consts.Timeout))*time.Second)

	defer cancel()

	result := vmwareClient.Discover(context, ctx, &loggerObj)

	assertions := assert.New(t)

	var props = []string{consts.CredentialProfileName, consts.ObjectCredentialProfile}

	assertions.True(result.IsNotEmpty())

	assertions.True(len(result) == 2)

	for _, prop := range props {

		assertions.Greater(len(result.GetStringValue(prop)), 0)

	}

	if result.IsNotEmpty() {

		defer SetupCleanupRoutine(vmwareClient, ctx, context, consts.EventDiscovery, nil)

		context[consts.ObjectCredentialProfile] = result[consts.ObjectCredentialProfile]

		context[consts.CredentialProfileName] = result[consts.CredentialProfileName]

		result = BuildESXiDiscoveryResult(vmwareClient, ctx, context)

	}

	assertVMWareClientDiscoverESXiTestResult(result, assertions)

}

func TestVMWareClientDiscovervCenter(t *testing.T) {

	context := GetContext(contexts, MotadataStringList{"vcenter", "valid"})

	vmwareClient := &VMwareClient{}

	ctx, cancel := contextlib.WithTimeout(contextlib.Background(), time.Second*time.Duration(context.GetIntValue(consts.Timeout)))

	defer cancel()

	vmwareClient.SetContext(context, &loggerObj)

	result := vmwareClient.Discover(context, ctx, &loggerObj)

	vmwareClient.Init(ctx)

	context[consts.ObjectCredentialProfile] = result[consts.ObjectCredentialProfile]

	context[consts.CredentialProfileName] = result[consts.CredentialProfileName]

	BuildvCenterDiscoveryResult(vmwareClient, ctx, context)

	assertions := assert.New(t)

	assertVMWareClientDiscovervCenterTestResult(context, assertions)

}

func TestVMWareClientRetrieveContainerValue(t *testing.T) {

	context := GetContext(contexts, MotadataStringList{"vcenter", "valid"})

	vmwareClient := &VMwareClient{}

	ctx, cancel := contextlib.WithTimeout(contextlib.Background(), time.Second*time.Duration(context.GetIntValue(consts.Timeout)))

	defer cancel()

	vmwareClient.SetContext(context, &loggerObj)

	vmwareClient.Init(ctx)

	assertions := assert.New(t)

	assertions.True(IsvCenterNode(vmwareClient))

	response := RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.Datastore)

	assertions.NotNil(response)

	assertions.Greater(len(response.([]mo.Datastore)), 0)

	response = RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.Datacenter)

	assertions.NotNil(response)

	assertions.Greater(len(response.([]mo.Datacenter)), 0)

	response = RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.ClusterComputeResource)

	assertions.NotNil(response)

	assertions.Greater(len(response.([]mo.ClusterComputeResource)), 0)

	response = RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.ResourcePool)

	assertions.NotNil(response)

	assertions.Greater(len(response.([]mo.ResourcePool)), 0)

	response = RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.Network)

	assertions.NotNil(response)

	assertions.Greater(len(response.([]mo.Network)), 0)

	response = RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.HostSystem)

	assertions.NotNil(response)

	assertions.Greater(len(response.([]mo.HostSystem)), 0)

	response = RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.VirtualMachine)

	assertions.NotNil(response)

	assertions.Greater(len(response.([]mo.VirtualMachine)), 0)

}

func TestVMWareClientRetrieveContainerValueError(t *testing.T) {

	context := GetContext(contexts, MotadataStringList{"vcenter", "valid"})

	vmwareClient := &VMwareClient{}

	ctx, cancel := contextlib.WithTimeout(contextlib.Background(), time.Second*time.Duration(context.GetIntValue(consts.Timeout)))

	defer cancel()

	vmwareClient.SetContext(context, &loggerObj)

	vmwareClient.Init(ctx)

	ctx, _ = contextlib.WithTimeout(contextlib.Background(), 1)

	assertions := assert.New(t)

	response := RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.Datastore)

	assertions.Nil(response)

	assertions.Greater(len(vmwareClient.errors), 0)

}

func TestVMWareClientGetSystemTags(t *testing.T) {

	context := GetContext(contexts, MotadataStringList{"vcenter", "valid"})

	vmwareClient := &VMwareClient{}

	ctx, cancel := contextlib.WithTimeout(contextlib.Background(), time.Second*time.Duration(context.GetIntValue(consts.Timeout)))

	defer cancel()

	vmwareClient.SetContext(context, &loggerObj)

	vmwareClient.Init(ctx)

	metric := make(MotadataMap)

	GetSystemTags(vmwareClient, ctx, metric)

	assertions := assert.New(t)

	assertions.True(metric.IsNotEmpty())

	assertions.True(metric.Contains("system.tags"))

}

func TestVMWareClientGetMetrics(t *testing.T) {

	context := GetContext(contexts, MotadataStringList{"esxi", "valid"})

	vmwareClient := &VMwareClient{}

	ctx, cancel := contextlib.WithTimeout(contextlib.Background(), time.Second*time.Duration(context.GetIntValue(consts.Timeout)))

	defer cancel()

	vmwareClient.SetContext(context, &loggerObj)

	vmwareClient.Init(ctx)

	response := RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.HostSystem)

	var names = []MotadataString{"mem.sysUsage.average", "mem.swapused.average", "mem.active.average",
		"mem.consumed.average", "mem.swapoutRate.average", "mem.totalCapacity.average",
		"mem.swapout.average", "mem.unreserved.average", "mem.heap.average",
		"mem.reservedCapacity.average", "mem.zero.average", "mem.heapfree.average",
		"mem.usage.average", "mem.shared.average", "mem.swapin.average",
		"mem.swapinRate.average", "mem.overhead.average", "mem.activewrite.average",
		"mem.vmmemctl.average", "mem.granted.average", "mem.sharedcommon.average",
		"net.bytesTx.average", "net.bytesRx.average", "net.usage.average", "disk.usage.average",
		"disk.write.average", "disk.read.average", "disk.queueReadLatency.average",
		"disk.commandsAveraged.average", "disk.queueWriteLatency.average",
		"disk.commandsAborted.summation", "disk.totalLatency.average", "disk.totalReadLatency.average",
		"disk.totalWriteLatency.average", "disk.queueLatency.average", "disk.numberReadAveraged.average",
		"disk.numberWriteAveraged.average", "cpu.usage.average", "cpu.usagemhz.average",
		"cpu.wait.summation", "cpu.ready.summation", "cpu.reservedCapacity.average",
		"cpu.swapwait.summation", "cpu.totalCapacity.average"}
	instances, counters := GetMetrics(vmwareClient, ctx, names, &(response.([]mo.HostSystem))[0], nil)

	assertions := assert.New(t)

	assertions.True(instances.IsNotEmpty())

	assertions.True(counters.IsNotEmpty())

	systemInfos := GetSystemInfoList(vmwareClient, ctx)

	assertions.Greater(len(systemInfos[0].Name), 0)

	assertions.Greater(len(systemInfos[0].EditionKey), 0)

	assertions.Greater(len(systemInfos[0].Properties), 0)

}

func TestVMWareClientGetMetrics2(t *testing.T) {

	context := GetContext(contexts, MotadataStringList{"esxi", "valid"})

	vmwareClient := &VMwareClient{}

	ctx, cancel := contextlib.WithTimeout(contextlib.Background(), time.Second*time.Duration(context.GetIntValue(consts.Timeout)))

	defer cancel()

	vmwareClient.SetContext(context, &loggerObj)

	vmwareClient.Init(ctx)

	response := RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.HostSystem)

	var metricNames = []MotadataString{"mem.sysUsage.average"}

	ctx, _ = contextlib.WithTimeout(contextlib.Background(), 1)

	instances, counters := GetMetrics(vmwareClient, ctx, metricNames, &(response.([]mo.HostSystem))[0], nil)

	assertions := assert.New(t)

	assertions.False(instances.IsNotEmpty())

	assertions.False(counters.IsNotEmpty())

	systemInfos := GetSystemInfoList(vmwareClient, ctx)

	assertions.Nil(systemInfos)
}

func assertVMWareClientDiscoverESXiTestResult(result MotadataMap, assertions *assert.Assertions) {

	var objects []MotadataMap

	var props = []string{consts.ObjectIP, consts.ObjectHost}

	assertions.Equal(consts.StatusSucceed, result.GetStringValue(consts.Status))

	assertions.Equal(0, len(result.GetStringMapSliceValue(consts.Errors)))

	assertions.True(result.Contains(consts.Objects))

	objects = result.GetMapSliceValue(consts.Objects)

	assertions.Equal(len(objects), 1)

	assertions.NotEmpty(objects[0])

	assertions.Equal(len(objects[0]), 3)

	for _, prop := range props {

		assertions.Greater(len(objects[0].GetStringValue(prop)), 0)

	}

	assertions.Regexp(regexp.MustCompile(consts.IPAddressRegexPattern), objects[0].GetStringValue(consts.ObjectIP))

	objects = objects[0].GetMapSliceValue(consts.Objects)

	for _, obj := range objects {

		assertions.Equal(len(obj), 4)

		assertions.Equal(obj.GetStringValue(consts.ObjectType), consts.VMNode)

		assertions.True(obj.GetStringValue(consts.Status) == consts.StatusUp || obj.GetStringValue(consts.Status) == consts.StatusDown)

		assertions.Greater(len(obj.GetStringValue(consts.ObjectName)), 0)

		assertions.True(obj.Contains(consts.ObjectIP))

		if len(obj.GetStringValue(consts.ObjectIP)) > 0 {

			assertions.Regexp(regexp.MustCompile(consts.IPAddressRegexPattern), obj.GetStringValue(consts.ObjectIP))

		}

	}

}

func assertVMWareClientDiscovervCenterTestResult(result MotadataMap, assertions *assert.Assertions) {

	object := result.GetMapSliceValue(consts.Objects)[0]

	assertions.Greater(len(result), 0)

	assertions.Equal(len(result.GetMapSliceValue(consts.Errors)), 0)

	nodes := result.GetMapSliceValue(Node)

	assertions.Greater(len(nodes), 0)

	for _, node := range nodes {

		assertions.Greater(len(node.GetStringValue(Node)), 0)

		assertions.Regexp(regexp.MustCompile(consts.IPAddressRegexPattern), node.GetStringValue(Node))

	}

	vms := result.GetMapSliceValue("vcenter.vm")

	assertions.Greater(len(vms), 0)

	for _, vm := range vms {

		assertions.Greater(len(vm.GetStringValue("vcenter.vm")), 0)

	}

	assertions.Regexp(regexp.MustCompile(consts.IPAddressRegexPattern), object.GetStringValue(consts.ObjectIP))

}
