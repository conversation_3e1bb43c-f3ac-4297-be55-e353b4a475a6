/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package vmwareclient

import (
	contextlib "context"
	"fmt"
	"github.com/vmware/govmomi"
	"motadatasdk/clients/httpclient"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"net/url"
)

type VMwareClient struct {
	client *govmomi.Client

	userInfo *url.URL

	userName MotadataString

	password MotadataString

	target MotadataString

	port MotadataUINT16

	timeout MotadataINT

	logger *Logger

	errors []MotadataStringMap
}

func (vmwareClient *VMwareClient) GetErrors() []MotadataStringMap {

	return vmwareClient.errors
}

func (vmwareClient *VMwareClient) GetClient() *govmomi.Client {

	return vmwareClient.client
}

func (vmwareClient *VMwareClient) SetContext(context MotadataMap, logger *Logger) *VMwareClient {

	return vmwareClient.setTarget(context).setPort(context).setUsername(context).setPassword(context).setTimeout(context).setLogger(logger)
}

func (vmwareClient *VMwareClient) Init(ctx contextlib.Context) bool {

	var err error

	userInfo := &url.URL{

		Scheme: httpclient.URLProtocolHTTPs,

		Path: "/sdk",

		User: url.UserPassword(ToString(vmwareClient.userName), ToString(vmwareClient.password)),

		Host: ToString(vmwareClient.target) + consts.ColonSeparator + ToString(int(vmwareClient.port)),
	}

	vmwareClient.logger.Debug(MotadataString(fmt.Sprintf("creating vmware api connection %s ", vmwareClient.target)))

	vmwareClient.userInfo = userInfo

	vmwareClient.client, err = govmomi.NewClient(ctx, userInfo, true)

	if err != nil {

		if MotadataString(err.Error()).Contains("no route to host") || MotadataString(err.Error()).Contains("connection refused") || MotadataString(err.Error()).Contains("i/o timeout") || MotadataString(err.Error()).Contains("TLS handshake timeout") || MotadataString(err.Error()).Contains("network is unreachable") || MotadataString(err.Error()).Contains("deadline exceeded") || MotadataString(err.Error()).Contains("connection reset by peer") {

			if IsOpened(vmwareClient.port, vmwareClient.target) {

				vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

					consts.Error: err.Error(),

					consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidCredentials, vmwareClient.target, vmwareClient.port),

					consts.ErrorCode: consts.ErrorCodeInvalidCredentials,
				})

			} else {

				vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

					consts.Error: err.Error(),

					consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidPort, vmwareClient.port),

					consts.ErrorCode: consts.ErrorCodeInvalidPort,
				})

			}
		} else if MotadataString(err.Error()).Contains("incorrect user name or password") {

			vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

				consts.Error: err.Error(),

				consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidCredentials, vmwareClient.target, vmwareClient.port),

				consts.ErrorCode: consts.ErrorCodeInvalidCredentials,
			})

		} else {

			vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

				consts.Error: err.Error(),

				consts.Message: fmt.Sprintf(consts.ErrorMessageConnectionFailed, consts.BlankString, vmwareClient.target, vmwareClient.port),

				consts.ErrorCode: consts.ErrorCodeConnectionFailed,
			})

		}

		vmwareClient.logger.Warn(MotadataString(fmt.Sprintf("Error occured while creating vmware api connection: %s on %s:%d", vmwareClient.errors, vmwareClient.target, vmwareClient.port)))

		return false
	}
	return true
}

func (vmwareClient *VMwareClient) setLogger(logger *Logger) *VMwareClient {

	vmwareClient.logger = logger

	return vmwareClient
}

func (vmwareClient *VMwareClient) setTarget(context MotadataMap) *VMwareClient {

	if context.Contains(consts.ObjectIP) {

		vmwareClient.target = context.GetMotadataStringValue(consts.ObjectIP)

	} else {

		vmwareClient.target = consts.LocalIP
	}

	return vmwareClient
}

func (vmwareClient *VMwareClient) setPort(context MotadataMap) *VMwareClient {

	if context.Contains(consts.Port) {

		vmwareClient.port = context.GetUINT16Value(consts.Port)

	} else {

		vmwareClient.port = MotadataUINT16(443)
	}

	return vmwareClient
}

func (vmwareClient *VMwareClient) setUsername(context MotadataMap) *VMwareClient {

	if context.Contains(consts.UserName) {

		vmwareClient.userName = context.GetMotadataStringValue(consts.UserName)

	}

	return vmwareClient
}

func (vmwareClient *VMwareClient) setPassword(context MotadataMap) *VMwareClient {

	if context.Contains(consts.Password) {

		vmwareClient.password = context.GetMotadataStringValue(consts.Password)

	}

	return vmwareClient
}

func (vmwareClient *VMwareClient) setTimeout(context MotadataMap) *VMwareClient {

	if context.Contains(consts.Timeout) {

		vmwareClient.timeout = context.GetINTValue(consts.Timeout)

	} else {

		vmwareClient.timeout = MotadataINT(60)
	}

	return vmwareClient
}

func (vmwareClient *VMwareClient) Destroy(context contextlib.Context) {

	if vmwareClient.client != nil {

		vmwareClient.client.Logout(context)

		vmwareClient.logger.Debug(MotadataString(fmt.Sprintf(consts.InfoMessageConnectionDestroyed, vmwareClient.target, (vmwareClient.port).ToString())))

	}
}
