/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package vmwareclient

import (
	contextlib "context"
	"fmt"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"runtime"

	"github.com/vmware/govmomi/license"
	"github.com/vmware/govmomi/performance"
	"github.com/vmware/govmomi/vapi/rest"
	"github.com/vmware/govmomi/vapi/tags"
	"github.com/vmware/govmomi/view"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
)

const (
	vCenter = "vCenter"

	VCenterNode = "vcenter.node"

	vCenterVM = "vcenter.vm"

	VM = "vm"
)

func SetupCleanupRoutine(vmwareClient *VMwareClient, ctx contextlib.Context, response MotadataMap, requestType MotadataString, responses chan<- MotadataMap) {

	if r := recover(); r != nil {

		err := make(MotadataStringMap)

		err[consts.Message] = fmt.Sprintf("%v", r)

		err[consts.ErrorCode] = consts.ErrorCodeInternalError

		err[consts.Error] = fmt.Sprintf("%v", r)

		response[consts.Errors] = append(response.GetStringMapSliceValue(consts.Errors), err)

		stackTraceBytes := make([]byte, consts.StackTraceSizeInBytes)

		PanicLogger.Fatal(MotadataString(fmt.Sprintf("serious error %v occurred in the plugin %v\nStack Trace ==> %v\n", r, RemoveSensitiveFields(response).ToJSON(), string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))))
	}

	vmwareClient.Destroy(ctx)

	if responses != nil {

		if requestType == consts.EventPoll {

			AddStatusField(response)

		}

		responses <- response

	}

}

func RetrieveContainerValue(vmwareClient *VMwareClient, ctx contextlib.Context, reference types.ManagedObjectReference, request string) interface{} {

	manager := view.NewManager(vmwareClient.client.Client)

	//defer manager.Destroy(ctx)

	var container *view.ContainerView

	var err error

	// if reference Type and Value is empty it means we need to fetch the data from root else we'll take the reference, and then we'll fetch data from it.
	// We'll use reference mainly in vCenter plugins

	if reference.Type == consts.BlankString && reference.Value == consts.BlankString {

		container, err = manager.CreateContainerView(ctx, vmwareClient.client.Client.ServiceContent.RootFolder, []string{request}, true)

	} else {

		container, err = manager.CreateContainerView(ctx, reference, []string{request}, true)

	}

	if err != nil {

		vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

			consts.Error: err.Error(),

			consts.Message: "unable to create container ",

			consts.ErrorCode: consts.ErrorCodeNoItemFound,
		})

	} else {

		defer container.Destroy(ctx)

		switch request {

		case consts.HostSystem:

			var hosts []mo.HostSystem

			err = container.Retrieve(ctx, []string{consts.HostSystem}, []string{}, &hosts)

			if err != nil || len(hosts) == 0 {

				vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

					consts.Error: err.Error(),

					consts.Message: "unable to retrieve the host system value",

					consts.ErrorCode: consts.ErrorCodeNoItemFound,
				})

			} else {

				return hosts

			}

		case consts.VirtualMachine:

			var virtualMachines []mo.VirtualMachine

			err = container.Retrieve(ctx, []string{consts.VirtualMachine}, []string{}, &virtualMachines)

			if err != nil {

				vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

					consts.Error: err.Error(),

					consts.Message: "unable to retrieve the virtual machine value",

					consts.ErrorCode: consts.ErrorCodeNoItemFound,
				})

			} else {

				return virtualMachines

			}

		case consts.Datastore:

			var dataStores []mo.Datastore

			err = container.Retrieve(ctx, []string{consts.Datastore}, []string{}, &dataStores)

			if err != nil {

				vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

					consts.Error: err.Error(),

					consts.Message: "unable to retrieve the data store value",

					consts.ErrorCode: consts.ErrorCodeNoItemFound,
				})

			} else {

				return dataStores

			}

		case consts.ClusterComputeResource:

			var clusters []mo.ClusterComputeResource

			err = container.Retrieve(ctx, []string{consts.ClusterComputeResource}, []string{}, &clusters)

			if err != nil {

				vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

					consts.Error: err.Error(),

					consts.Message: "unable to retrieve the cluster value",

					consts.ErrorCode: consts.ErrorCodeNoItemFound,
				})

			} else {

				return clusters

			}

		case consts.Network:

			var networks []mo.Network

			err = container.Retrieve(ctx, []string{consts.Network}, []string{}, &networks)

			if err != nil {

				vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

					consts.Error: err.Error(),

					consts.Message: "unable to retrieve the network value",

					consts.ErrorCode: consts.ErrorCodeNoItemFound,
				})

			} else {

				return networks

			}

		case consts.ResourcePool:

			var resourcePools []mo.ResourcePool

			err = container.Retrieve(ctx, []string{consts.ResourcePool}, []string{}, &resourcePools)

			if err != nil {

				vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

					consts.Error: err.Error(),

					consts.Message: "unable to retrieve the resource pool value",

					consts.ErrorCode: consts.ErrorCodeNoItemFound,
				})

			} else {

				return resourcePools

			}

		case consts.Datacenter:

			var datacenters []mo.Datacenter

			err = container.Retrieve(ctx, []string{consts.Datacenter}, []string{}, &datacenters)

			if err != nil {

				vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

					consts.Error: err.Error(),

					consts.Message: "unable to retrieve the datacenter value",

					consts.ErrorCode: consts.ErrorCodeNoItemFound,
				})

			} else {

				return datacenters

			}

		}

	}

	return nil

}

func GetMetrics(vmwareClient *VMwareClient, ctx contextlib.Context, metricNames []MotadataString, host *mo.HostSystem, virtualMachine *mo.VirtualMachine) (MotadataMap, MotadataMap) {

	instances := make(MotadataMap)

	metrics := make(MotadataMap)

	manager := performance.NewManager(vmwareClient.client.Client)

	//defer manager.Destroy(ctx)

	counters, err := manager.CounterInfo(ctx)

	if err != nil {

		vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

			consts.Error: err.Error(),

			consts.Message: "Unable to gather counterInfo value from perfManager",

			consts.ErrorCode: consts.ErrorCodeNoItemFound,
		})

	} else {

		var metricIds []types.PerfMetricId

		values := make(MotadataMap)

		if counters != nil && len(counters) > 0 {

			for _, counter := range counters {

				name := counter.GroupInfo.GetElementDescription().Key + "." + counter.NameInfo.GetElementDescription().Key + "." + string(counter.RollupType) //ToString does not support PerfSummaryType

				for _, metricName := range metricNames {

					if MotadataString(name) == metricName {

						values[name] = counter.Key

						metricId := types.PerfMetricId{

							DynamicData: types.DynamicData{},

							CounterId: counter.Key,

							Instance: "*",
						}

						metricIds = append(metricIds, metricId)

					}
				}
			}
		}

		var specs []types.PerfQuerySpec

		var spec types.PerfQuerySpec

		if host != nil {

			spec = types.PerfQuerySpec{

				MaxSample: 1,

				Entity: host.Reference(),

				MetricId: metricIds,
			}

		} else {

			spec = types.PerfQuerySpec{

				MaxSample: 1,

				Entity: virtualMachine.Reference(),

				MetricId: metricIds,
			}

		}

		specs = append(specs, spec)

		metricBases, err := manager.Query(ctx, specs)

		if err != nil {

			vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

				consts.Error: err.Error(),

				consts.Message: "unable to gather metric base value",

				consts.ErrorCode: consts.ErrorCodeNoItemFound,
			})

		} else {

			metricSeries, err := manager.ToMetricSeries(ctx, metricBases)

			if err != nil {

				vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

					consts.Error: err.Error(),

					consts.Message: "Unable to gather metric series value",

					consts.ErrorCode: consts.ErrorCodeNoItemFound,
				})

			} else {

				if metricSeries != nil && len(metricSeries) > 0 {

					series := metricSeries[0].Value

					if series != nil && len(series) > 0 {

						for _, value := range series {

							if values.Contains(value.Name) {

								if value.Instance != consts.BlankString {

									instance := make(MotadataMap)

									if instances.GetMapValue(value.Instance) != nil {

										instance = instances.GetMapValue(value.Instance)

										instance[value.Name] = value.Value[0]

										instances[value.Instance] = instance

									} else {

										instances[value.Instance] = instance

										instance[value.Name] = value.Value[0]

									}

								} else {

									metrics[value.Name] = value.Value[0]

								}

							}

						}

					}
				}
			}
		}
	}

	return instances, metrics

}

func (vmwareClient *VMwareClient) Discover(context MotadataMap, ctx contextlib.Context, logger *Logger) (result MotadataMap) {

	var credentialProfiles []interface{}

	credentialProfiles = context.GetSliceValue(consts.DiscoveryCredentialProfiles)

	result = make(MotadataMap)

	for _, credentialProfile := range credentialProfiles {

		credentialProfile := ToMap(credentialProfile)

		credentialProfile[consts.ObjectIP] = context[consts.ObjectIP]

		credentialProfile[consts.Port] = context[consts.Port]

		credentialProfile[consts.Timeout] = context[consts.Timeout]

		vmwareClient.SetContext(credentialProfile, logger)

		if vmwareClient.Init(ctx) {

			result[consts.ObjectCredentialProfile] = credentialProfile[consts.Id]

			result[consts.CredentialProfileName] = credentialProfile[consts.CredentialProfileName]

			break

		}

	}

	return

}

func BuildESXiDiscoveryResult(vmwareClient *VMwareClient, ctx contextlib.Context, context MotadataMap) MotadataMap {

	if vmwareClient.client != nil {

		context[consts.Status] = consts.StatusSucceed

		var vms []MotadataMap

		response := RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.VirtualMachine)

		if response != nil {

			virtualMachines := response.([]mo.VirtualMachine)

			if virtualMachines != nil && len(virtualMachines) > 0 {

				for _, virtualMachine := range virtualMachines {

					vm := make(MotadataMap)

					vm[consts.ObjectType] = VM

					vm[consts.ObjectName] = virtualMachine.Summary.Config.Name

					vm[consts.ObjectIP] = virtualMachine.Summary.Guest.IpAddress

					// Add OS name from guest information
					if virtualMachine.Guest != nil && virtualMachine.Guest.GuestFullName != "" {
						vm[consts.ObjectOSName] = virtualMachine.Guest.GuestFullName
					}

					// VM will be considered up only if it is in poweredon state, other states(i.e: poweredoff , suspended) it will be considered down

					if virtualMachine.Summary.Runtime.PowerState == "poweredOn" {

						vm[consts.Status] = consts.StatusUp

					} else {

						vm[consts.Status] = consts.StatusDown

					}

					if len(vm) > 0 {

						vms = append(vms, vm)

					}

				}

			}
		}

		response = RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.HostSystem)

		if response != nil {

			object := make(MotadataMap)

			object[consts.ObjectIP] = context.GetStringValue(consts.ObjectIP)

			if !context.Contains(consts.ObjectHost) {

				object[consts.ObjectHost] = response.([]mo.HostSystem)[0].ManagedEntity.Name

			}

			if len(vms) > 0 {

				object[consts.Objects] = vms

			}

			context[consts.Objects] = []MotadataMap{object}

		}

	}

	return context

}

func BuildvCenterDiscoveryResult(vmwareClient *VMwareClient, ctx contextlib.Context, context MotadataMap) {

	if vmwareClient.client != nil && MotadataString(vmwareClient.client.Client.ServiceContent.About.Name).Contains(vCenter) {

		var objects []MotadataMap

		objects = append(objects, MotadataMap{consts.ObjectIP: context.GetStringValue(consts.ObjectIP), consts.ObjectType: consts.ObjectTypeVcenter})

		response := RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.HostSystem)

		if response != nil {

			hosts := response.([]mo.HostSystem)

			if hosts != nil && len(hosts) > 0 {

				for _, host := range hosts {

					object := make(MotadataMap)

					object[consts.ObjectType] = consts.ObjectTypeVMwareESXi

					object[consts.ObjectName] = host.ManagedEntity.Name

					object[consts.ObjectIP] = host.Name

					object[consts.ObjectHost] = host.ManagedEntity.Name

					objects = append(objects, object)
				}

			}
		}

		response = RetrieveContainerValue(vmwareClient, ctx, types.ManagedObjectReference{}, consts.VirtualMachine)

		if response != nil {

			virtualMachines := response.([]mo.VirtualMachine)

			if virtualMachines != nil && len(virtualMachines) > 0 {

				for _, virtualMachine := range virtualMachines {

					if virtualMachine.Summary.Guest != nil && virtualMachine.Summary.Guest.IpAddress != consts.BlankString {

						object := make(MotadataMap)

						object[consts.ObjectName] = virtualMachine.Summary.Config.Name

						object[consts.ObjectHost] = virtualMachine.Name

						object[consts.ObjectIP] = virtualMachine.Summary.Guest.IpAddress

						// Add OS name from guest information
						if virtualMachine.Guest != nil && virtualMachine.Guest.GuestFullName != consts.BlankString {

							object[consts.ObjectOSName] = virtualMachine.Guest.GuestFullName

							if object.GetMotadataStringValue(consts.ObjectName).ToLower().Contains("windows") {

								object[consts.ObjectType] = consts.ObjectTypeWindows

							} else {

								object[consts.ObjectType] = consts.ObjectTypeLinux
							}

						} else {

							object[consts.ObjectType] = consts.ObjectTypeLinux
						}

						if virtualMachine.Summary.Runtime.PowerState == "poweredOn" {

							object[consts.Status] = consts.StatusUp

						} else {

							object[consts.Status] = consts.StatusDown

						}

						objects = append(objects, object)
					}
				}
			}
		}

		context[consts.Objects] = objects

		context[consts.Status] = consts.StatusSucceed

	}

}

func GetSystemInfoList(vmwareClient *VMwareClient, ctx contextlib.Context) license.InfoList {

	licenseManager := license.NewManager(vmwareClient.client.Client)

	//defer licenseManager.Destroy(ctx)

	// It returns List containing information like license,expiration date,model,vendor etc.

	systemInfo, err := licenseManager.List(ctx)

	if err != nil {

		vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

			consts.Error: err.Error(),

			consts.Message: "unable to gather the info list value from license manager",

			consts.ErrorCode: consts.ErrorCodeNoItemFound,
		})

	} else {

		return systemInfo

	}

	return nil
}

func GetSystemTags(vmwareClient *VMwareClient, ctx contextlib.Context, metrics MotadataMap) {

	// tags only available in version 6 or above

	if MotadataString(vmwareClient.client.ServiceContent.About.Version).Split(".")[0].ToInt() >= 6 {

		systemTags := MotadataStringList{}

		restClient := rest.NewClient(vmwareClient.client.Client)

		_, err := restClient.Session(ctx)

		if err != nil {

			vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

				consts.Error: err.Error(),

				consts.Message: "rest session is not created",

				consts.ErrorCode: consts.ErrorCodeNoItemFound,
			})

		} else {

			err = restClient.Login(ctx, vmwareClient.userInfo.User)

			if err != nil {

				vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

					consts.Error: err.Error(),

					consts.Message: "unable to login using rest rest - client",

					consts.ErrorCode: consts.ErrorCodeInvalidCredentials,
				})

			} else {

				manager := tags.NewManager(restClient)

				listOfTags, err := manager.ListTags(ctx)

				if err != nil {

					vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

						consts.Error: err.Error(),

						consts.Message: "unable to generate list of tags",

						consts.ErrorCode: consts.ErrorCodeNoItemFound,
					})

				}

				if listOfTags != nil && len(listOfTags) > 0 {

					for _, object := range listOfTags {

						tag, err := manager.GetTag(ctx, object)

						if err != nil {

							vmwareClient.errors = append(vmwareClient.errors, MotadataStringMap{

								consts.Error: err.Error(),

								consts.Message: "unable to get tag",

								consts.ErrorCode: consts.ErrorCodeNoItemFound,
							})
						}

						systemTags = append(systemTags, tag.Name)

					}
				}
			}
		}

		metrics[consts.SystemTags] = systemTags

	}
}

func IsvCenterNode(vmwareClient *VMwareClient) bool {

	if vmwareClient.client != nil {

		if MotadataString(vmwareClient.client.ServiceContent.About.Name).Contains(vCenter) {

			return true

		}

	}

	return false

}
