/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
 * Change Logs:
 *  Date			Author			Notes
 *  26-Feb-2025		Ya<PERSON>wari		MOTADATA-5243: Refactored the discover() function params & added datasource field to support multiple credential profile for database app based plugins
 *
 */
package databaseclient

import (
	"fmt"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"net/url"
	"runtime"
)

func SetupCleanupRoutine(databaseClient *DatabaseClient, responses chan<- MotadataMap, response MotadataMap, requestType MotadataString) {

	if r := recover(); r != nil {

		err := make(MotadataStringMap)

		err[consts.Message] = fmt.Sprintf("%v", r)

		err[consts.ErrorCode] = consts.ErrorCodeInternalError

		err[consts.Error] = fmt.Sprintf("%v", r)

		response[consts.Errors] = append(response.GetStringMapSliceValue(consts.Errors), err)

		stackTraceBytes := make([]byte, consts.StackTraceSizeInBytes)

		PanicLogger.Fatal(MotadataString(fmt.Sprintf("serious error %v occurred in the plugin %v\nStack Trace ==> %v\n", r, RemoveSensitiveFields(response).ToJSON(), string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))))
	}

	databaseClient.Destroy()

	if responses != nil {

		if requestType == consts.EventPoll {

			AddStatusField(response)

		}

		responses <- response

	}

}

func (databaseClient *DatabaseClient) Discover(context MotadataMap, isURLEncode bool, dataSource string, logger *Logger) (result MotadataMap) {

	result = make(MotadataMap)

	credentialProfiles := context.GetSliceValue(consts.DiscoveryCredentialProfiles)

	if credentialProfiles == nil || len(credentialProfiles) == 0 {

		credentialProfiles = []interface{}{make(MotadataMap)}

	}

	for _, credentialProfile := range credentialProfiles {

		profile := ToMap(credentialProfile)

		profile[consts.ObjectIP] = context.GetMotadataStringValue(consts.ObjectIP)

		profile[consts.Port] = context.GetIntValue(consts.Port)

		profile[consts.Database] = context.GetMotadataStringValue(consts.Database)

		profile[consts.Timeout] = context.GetUINTValue(consts.Timeout)

		databaseClient.SetContext(profile, logger)

		if isURLEncode {

			databaseClient.SetDataSource(MotadataString(fmt.Sprintf(dataSource, url.QueryEscape(profile.GetStringValue(consts.UserName)), url.QueryEscape(profile.GetStringValue(consts.Password)))))

		} else {

			databaseClient.SetDataSource(MotadataString(fmt.Sprintf(dataSource, profile.GetStringValue(consts.UserName), profile.GetStringValue(consts.Password))))

		}

		if databaseClient.Init() {

			result[consts.ObjectCredentialProfile] = profile.GetFloatValue(consts.Id)

			result[consts.CredentialProfileName] = profile.GetStringValue(consts.CredentialProfileName)

			break

		}
	}
	return

}
