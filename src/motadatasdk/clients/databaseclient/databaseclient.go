/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package databaseclient

import (
	"database/sql"
	"fmt"
	_ "github.com/SAP/go-hdb/driver"
	_ "github.com/go-sql-driver/mysql"
	_ "github.com/lib/pq"
	_ "github.com/microsoft/go-mssqldb"
	_ "github.com/sijms/go-ora/v2"
	"motadatasdk/consts"
	. "motadatasdk/globals"
)

type DatabaseClient struct {
	database MotadataString

	driver MotadataString

	target MotadataString

	port MotadataUINT16

	userName MotadataString

	password MotadataString

	dataSource MotadataString

	logger *Logger

	errors []MotadataStringMap

	connection *sql.DB
}

func (databaseClient *DatabaseClient) Init() (result bool) {

	databaseClient.logger.Debug(MotadataString(fmt.Sprintf("connecting to %s ip %d port %s db name", databaseClient.target, databaseClient.port, databaseClient.database)))

	result = true

	var err error

	databaseClient.connection, err = sql.Open(databaseClient.driver.ToString(), databaseClient.dataSource.ToString())

	err = databaseClient.connection.Ping()

	if err != nil {

		databaseClient.logger.Warn(MotadataString(fmt.Sprintf("error %v occurred while creating connection for database %v", err, databaseClient.dataSource)))

		if MotadataString(err.Error()).Contains("Access denied for user") || MotadataString(err.Error()).Contains("password authentication failed for user") || MotadataString(err.Error()).Contains("authentication failed") || MotadataString(err.Error()).Contains("invalid username/password; logon denied") || err.Error() == fmt.Sprintf("mssql: login error: Login failed for user '%s'.", databaseClient.userName) {

			if MotadataString(err.Error()).Contains(fmt.Sprintf("database '%s'", databaseClient.database)) {

				// error is because of invalid database access
				databaseClient.errors = append(databaseClient.errors, MotadataStringMap{
					consts.Message:   fmt.Sprintf(consts.ErrorMessageUnauthorizedDatabaseAccess, databaseClient.database),
					consts.ErrorCode: consts.ErrorCodeUnauthorizedDatabaseAccess,
					consts.Error:     err.Error(),
				})
			} else {

				// error is because of incorrect username or password
				databaseClient.errors = append(databaseClient.errors, MotadataStringMap{
					consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidCredentials, databaseClient.target, databaseClient.port),
					consts.ErrorCode: consts.ErrorCodeInvalidCredentials,
					consts.Error:     err.Error(),
				})
			}

		} else if MotadataString(err.Error()).Contains("connect: no route to host") {

			// error is because of incorrect IP
			databaseClient.errors = append(databaseClient.errors, MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidHost, databaseClient.target),
				consts.ErrorCode: consts.ErrorCodeInvalidHost,
				consts.Error:     err.Error(),
			})

		} else if MotadataString(err.Error()).Contains("connect: connection refused") {

			// error is because of incorrect port
			databaseClient.errors = append(databaseClient.errors, MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidPort, databaseClient.port),
				consts.ErrorCode: consts.ErrorCodeInvalidPort,
				consts.Error:     err.Error(),
			})
		} else if MotadataString(err.Error()).Contains("connect: connection timed out") || MotadataString(err.Error()).Contains("i/o timeout") {

			if IsOpened(databaseClient.port, databaseClient.target) {

				// error is because of time out error
				databaseClient.errors = append(databaseClient.errors, MotadataStringMap{
					consts.Message:   consts.ErrorMessageConnectionTimeout,
					consts.ErrorCode: consts.ErrorCodeTimeout,
					consts.Error:     err.Error(),
				})
			} else {

				// error is because of incorrect port
				databaseClient.errors = append(databaseClient.errors, MotadataStringMap{
					consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidPort, databaseClient.port),
					consts.ErrorCode: consts.ErrorCodeInvalidPort,
					consts.Error:     err.Error(),
				})
			}

		} else if MotadataString(err.Error()).Contains("permission denied for database") {

			// error is because of permission denied for database
			databaseClient.errors = append(databaseClient.errors, MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageUnauthorizedDatabaseAccess, databaseClient.database),
				consts.ErrorCode: consts.ErrorCodeUnauthorizedDatabaseAccess,
				consts.Error:     err.Error(),
			})

		} else if MotadataString(err.Error()).Contains("not permitted to log in") || err.Error() == fmt.Sprintf("mssql: login error: Login failed for user '%s'. Reason: The account is disabled.", databaseClient.userName) {

			// error is because of user not allowed to log in
			databaseClient.errors = append(databaseClient.errors, MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidLoginPrivilege, databaseClient.database, databaseClient.target),
				consts.ErrorCode: consts.ErrorCodeDatabaseLogin,
				consts.Error:     err.Error(),
			})

		} else if MotadataString(err.Error()).Contains("Unknown database") || MotadataString(err.Error()).Contains(fmt.Sprintf("database \"%s\"", databaseClient.database)) || MotadataString(err.Error()).Contains("listener does not currently know of service requested in connect descriptor") || MotadataString(err.Error()).Contains("listener does not currently know of sid given in connect descriptor") || MotadataString(err.Error()).Contains("ORA-12505") {

			// error is because of incorrect database name
			databaseClient.errors = append(databaseClient.errors, MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageInvalidDatabase),
				consts.ErrorCode: consts.ErrorCodeInvalidDatabase,
				consts.Error:     err.Error(),
			})

		} else {

			databaseClient.errors = append(databaseClient.errors, MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageConnectionFailed, consts.BlankString, databaseClient.target, databaseClient.port),
				consts.ErrorCode: consts.ErrorCodeConnectionFailed,
				consts.Error:     err.Error(),
			})

		}

		databaseClient.logger.Warn(MotadataString(fmt.Sprintf("Error occured while creating database connection: %s on %s ip %d port %s db name", databaseClient.errors, databaseClient.target, databaseClient.port, databaseClient.database)))

		return false
	}

	databaseClient.logger.Debug(MotadataString(fmt.Sprintf("Connection created for %s ip %d port %s db name", databaseClient.target, databaseClient.port, databaseClient.database)))

	return

}

func (databaseClient *DatabaseClient) SetContext(context MotadataMap, logger *Logger) *DatabaseClient {

	return databaseClient.setUsername(context).setPassword(context).setTarget(context).setPort(context).
		setDatabase(context).setLogger(logger)

}

func (databaseClient *DatabaseClient) ExecuteQuery(query MotadataString) (rows []MotadataMap) {

	databaseClient.logger.Debug(MotadataString(fmt.Sprintf("Executing %s query for %s target", query, databaseClient.target)))

	records, err := databaseClient.connection.Query(string(query))

	if err != nil {

		databaseClient.logger.Warn(MotadataString(fmt.Sprintf("error %v occured while executing %v query for %s target", err, query, databaseClient.target)))

		if err.Error() == "mssql: The user does not have permission to perform this action." {

			databaseClient.errors = append(databaseClient.errors, MotadataStringMap{
				consts.Message:   fmt.Sprintf(consts.ErrorMessageUnauthorizedDatabaseAccess, databaseClient.database),
				consts.ErrorCode: consts.ErrorCodeUnauthorizedDatabaseAccess,
				consts.Error:     err.Error(),
			})

		} else {

			databaseClient.errors = append(databaseClient.errors, MotadataStringMap{
				consts.Message:   err.Error(),
				consts.ErrorCode: consts.ErrorCodeCommandExecutionFailed,
				consts.Error:     err.Error(),
			})

		}

		return nil
	}

	columns, _ := records.Columns()

	for records.Next() {

		values := make([]interface{}, len(columns))

		row := make(MotadataMap)

		// scans is initialize to pointer
		for i := range values {

			values[i] = &values[i]

		}

		_ = records.Scan(values...)

		for index, value := range values {

			if value != nil {

				row[columns[index]] = value

			}

		}

		rows = append(rows, row)

	}

	return

}

func (databaseClient *DatabaseClient) GetErrors() []MotadataStringMap {

	return databaseClient.errors
}

func (databaseClient *DatabaseClient) setLogger(logger *Logger) *DatabaseClient {

	databaseClient.logger = logger

	return databaseClient
}

func (databaseClient *DatabaseClient) Destroy() {

	if databaseClient.connection != nil {

		_ = databaseClient.connection.Close()

		databaseClient.logger.Debug(MotadataString(fmt.Sprintf(consts.InfoMessageConnectionDestroyed, databaseClient.target, (databaseClient.port).ToString())))

	}
}

func (databaseClient *DatabaseClient) setTarget(context MotadataMap) *DatabaseClient {

	databaseClient.target = consts.LocalHost

	if context.Contains(consts.ObjectIP) {

		databaseClient.target = context.GetMotadataStringValue(consts.ObjectIP)

	}

	return databaseClient
}

func (databaseClient *DatabaseClient) setPort(context MotadataMap) *DatabaseClient {

	if context.Contains(consts.Port) {

		databaseClient.port = context.GetUINT16Value(consts.Port)

	}

	return databaseClient
}

func (databaseClient *DatabaseClient) setUsername(context MotadataMap) *DatabaseClient {

	if context.Contains(consts.UserName) {

		databaseClient.userName = context.GetMotadataStringValue(consts.UserName)

	}

	return databaseClient
}

func (databaseClient *DatabaseClient) setPassword(context MotadataMap) *DatabaseClient {

	if context.Contains(consts.Password) {

		databaseClient.password = context.GetMotadataStringValue(consts.Password)

	}

	return databaseClient
}

func (databaseClient *DatabaseClient) SetDriver(driver MotadataString) *DatabaseClient {

	databaseClient.driver = driver

	return databaseClient

}

func (databaseClient *DatabaseClient) SetDataSource(dataSource MotadataString) *DatabaseClient {

	databaseClient.dataSource = dataSource

	return databaseClient

}

func (databaseClient *DatabaseClient) setDatabase(context MotadataMap) *DatabaseClient {

	if context.Contains(consts.Database) {

		databaseClient.database = context.GetMotadataStringValue(consts.Database)

	}

	return databaseClient

}
