/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package databaseclient

import (
	"encoding/json"
	"fmt"
	"github.com/stretchr/testify/assert"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"os"
	"path/filepath"
	"testing"
)

var (
	bytes []byte

	loggerObj = NewLogger("Test DatabaseClient", consts.BlankString)
)

func TestMain(m *testing.M) {

	bytes, _ = os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(consts.CurrentDir)))) + consts.PathSeparator + consts.ConfigDirectory + consts.PathSeparator + "context.json")

	m.Run()
}

func TestDatabaseClientSAPHANA(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"saphana", "valid"})

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.HDB).SetDataSource(MotadataString(fmt.Sprintf("hdb://%s:%s@%s:%d/%s?timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(true, result)

	rows := databaseClient.ExecuteQuery("SELECT START_TIME FROM SYS.M_DATABASE")

	assertions.True(len(rows) > 0)

	assertions.True(len(databaseClient.GetErrors()) == 0)

	databaseClient.Destroy()
}

func TestDatabaseClientInvalidHostSAPHANA(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"saphana", "valid"})

	context[consts.ObjectIP] = "***********"

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.HDB).SetDataSource(MotadataString(fmt.Sprintf("hdb://%s:%s@%s:%d/%s?timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidHost, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientTimeoutSAPHANA(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"saphana", "valid"})

	context[consts.ObjectIP] = "************"

	context[consts.Timeout] = 1

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.HDB).SetDataSource(MotadataString(fmt.Sprintf("hdb://%s:%s@%s:%d/%s?timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidPort, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidQuerySAPHANA(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"saphana", "valid"})

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.HDB).SetDataSource(MotadataString(fmt.Sprintf("hdb://%s:%s@%s:%d/%s?timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	if result {

		databaseClient.ExecuteQuery("Hello world")

	}

	assertions := assert.New(t)

	assertions.Equal(true, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeCommandExecutionFailed, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidPortSAPHANA(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"saphana", "valid"})

	context[consts.Port] = consts.InvalidPort

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.HDB).SetDataSource(MotadataString(fmt.Sprintf("hdb://%s:%s@%s:%d/%s?timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidPort, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidUsernameSAPHANA(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"saphana", "valid"})

	context[consts.UserName] = consts.InvalidUserName

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.HDB).SetDataSource(MotadataString(fmt.Sprintf("hdb://%s:%s@%s:%d/%s?timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidCredentials, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidPasswordSAPHANA(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"saphana", "valid"})

	context[consts.Password] = consts.InvalidPassword

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.HDB).SetDataSource(MotadataString(fmt.Sprintf("hdb://%s:%s@%s:%d/%s?timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidCredentials, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientPostgreSQL(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"postgresql", "valid.user"})

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.Postgres).SetDataSource(MotadataString(fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable&&connect_timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(true, result)

	rows := databaseClient.ExecuteQuery("SELECT * FROM pg_stat_activity")

	assertions.True(len(rows) > 0)

	assertions.True(len(databaseClient.errors) == 0)

	databaseClient.Destroy()

}

func TestDatabaseClientInvalidHostPostgreSQL(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"postgresql", "valid.user"})

	context[consts.ObjectIP] = "***********"

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.Postgres).SetDataSource(MotadataString(fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable&&connect_timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidHost, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidPortPostgreSQL(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"postgresql", "valid.user"})

	context[consts.Port] = consts.InvalidPort

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.Postgres).SetDataSource(MotadataString(fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable&&connect_timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidPort, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidDatabasePostgreSQL(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"postgresql", "valid.user"})

	context[consts.Database] = consts.Database

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.Postgres).SetDataSource(MotadataString(fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable&&connect_timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidDatabase, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidUserNamePostgreSQL(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"postgresql", "valid.user"})

	context[consts.UserName] = consts.InvalidUserName

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.Postgres).SetDataSource(MotadataString(fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable&&connect_timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidCredentials, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidPasswordPostgreSQL(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"postgresql", "valid.user"})

	context[consts.Password] = consts.InvalidPassword

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.Postgres).SetDataSource(MotadataString(fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable&&connect_timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidCredentials, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidLoginAccessPostgreSQL(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"postgresql", "login"})

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.Postgres).SetDataSource(MotadataString(fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable&&connect_timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeDatabaseLogin, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidPrivilegePostgreSQL(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"postgresql", "restrict.database"})

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.Postgres).SetDataSource(MotadataString(fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable&&connect_timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeUnauthorizedDatabaseAccess, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientMySQL(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"mysql", "valid"})

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.MySQL).SetDataSource(MotadataString(fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?timeout=%ds", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(true, result)

	rows := databaseClient.ExecuteQuery("SHOW GLOBAL STATUS")

	assertions.True(len(rows) > 0)

	assertions.True(len(databaseClient.errors) == 0)

	databaseClient.Destroy()

}

func TestDatabaseClientInvalidHostMySQL(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"mysql", "valid"})

	context[consts.ObjectIP] = "***********"

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.MySQL).SetDataSource(MotadataString(fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?timeout=%ds", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidHost, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidPortMySQL(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"mysql", "valid"})

	context[consts.Port] = consts.Port

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.MySQL).SetDataSource(MotadataString(fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?timeout=%ds", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidPort, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientMariaDB(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"mariadb", "valid"})

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.MySQL).SetDataSource(MotadataString(fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?timeout=%ds", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.True(result)

	rows := databaseClient.ExecuteQuery("SHOW GLOBAL STATUS")

	assertions.True(len(rows) > 0)

	assertions.True(len(databaseClient.errors) == 0)

	databaseClient.Destroy()

}

func TestDatabaseClientInvalidHostMariaDB(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"mariadb", "valid"})

	context[consts.ObjectIP] = "***********"

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.MySQL).SetDataSource(MotadataString(fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?timeout=%ds", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidHost, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidPortMariaDB(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"mariadb", "valid"})

	context[consts.Port] = consts.InvalidPort

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.MySQL).SetDataSource(MotadataString(fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?timeout=%ds", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context[consts.Database], context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	assertions := assert.New(t)

	assertions.Equal(false, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeInvalidPort, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientInvalidPrivilege(t *testing.T) {

	databaseClient := &DatabaseClient{}

	var contexts MotadataMap

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"mssql", "valid.user.without.permission"})

	databaseClient.SetContext(context, &loggerObj).SetDriver(consts.SQLServer).SetDataSource(MotadataString(fmt.Sprintf("sqlserver://%s:%s@%s:%d?encrypt=disable&connection+timeout=%d", context[consts.UserName], context[consts.Password], context[consts.ObjectIP], context.GetUINT16Value(consts.Port), context.GetUINT16Value(consts.Timeout))))

	result := databaseClient.Init()

	if result {

		databaseClient.ExecuteQuery("SELECT sqlserver_start_time FROM sys.dm_os_sys_info")

	}

	assertions := assert.New(t)

	assertions.Equal(true, result)

	assertions.True(len(databaseClient.errors) == 1)

	assertions.Equal(consts.ErrorCodeUnauthorizedDatabaseAccess, databaseClient.GetErrors()[0][consts.ErrorCode])

}

func TestDatabaseClientDiscover(t *testing.T) {

	var contexts MotadataMap

	databaseClient := &DatabaseClient{}

	_ = json.Unmarshal(bytes, &contexts)

	context := GetContext(contexts, MotadataStringList{"mariadb", "valid.discovery"})

	databaseClient.SetDriver(consts.MySQL)

	result := databaseClient.Discover(context, false, fmt.Sprintf(consts.MySQLConnectionURL, "%s", "%s", "["+context.GetStringValue(consts.ObjectIP)+"]", context.GetINTValue(consts.Port), context.GetStringValue(consts.Database), context.GetIntValue(consts.Timeout)), &loggerObj)

	assertions := assert.New(t)

	assertions.NotNil(result)

	assertions.Contains(result, consts.ObjectCredentialProfile)

	assertions.Contains(result, consts.CredentialProfileName)
}
