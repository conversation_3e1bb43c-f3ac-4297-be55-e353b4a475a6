/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package consts

const (
	DefaultTimeout = 5

	ValidWindowsPort = 5985

	InvalidHost = "**************"

	InvalidPort = 1234

	InvalidUserName = "abc123"

	InvalidPassword = "motasdcsddataaa"

	InvalidSNMPCommunity = "xysdd"

	InvalidObjectHost = "DCBA123"

	InvalidSecretKey = "pu6mxO0hJxJHQ3/sfsdfee5d4rNmErG/sKvlfVoYhCVGS"

	InvalidTenantId = "5b4acec3-8790-4187-9489-98c654cc6c87"

	InvalidClientId = "aaa130f3-5ed7-345g-87f5-mota123dataa"

	InvalidTarget = "dsgdfhcfgj"

	InvalidDatabase = "asduhasuic"

	InvalidAccessId = "AKIAX2UDTUK4657QTNUV"

	TimeRegex = `( \d+ (?:days|day) \d+ (?:hours|hour) \d+ (?:minutes|minute) \d+ (?:seconds|second))`

	AccountId = "*************"

	ProdEnv = "prod"
)
