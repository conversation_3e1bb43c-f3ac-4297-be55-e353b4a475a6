/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
 * Change Logs:
 *  Date			Author				Notes
 *  5-May-2025      Darshan Parmar  	MOTADATA-6084: constants for hpe primera and hpe 3PAR
 *  23-May-2025     Darshan Parmar  	MOTADATA-6184: constants for Dell EMC Unity
 *  26-May-2025     Sankalp         	MOTADATA-6185 : Monitoring support for IBM AS 400
 *  20-Jun-2025     <PERSON><PERSON>    MOTADATA-6577 : constant for NSXT
 *  24-Jun-2025     <PERSON>A-6588 : constant for Container Orchestration
 *  07-Jul-2025     <PERSON>OTADATA-6675 : constant for Kubernetes
 *  28-Jul-2025     <PERSON>OTADATA-6955 : constant for IBM FlashSystem
 */

package consts

import (
	"os"
	"regexp"
)

const (
	Yes = "yes"

	No = "no"

	LocalIP = "127.0.0.1"

	LocalHost = "localhost"

	ObjectIP = "object.ip"

	Port = "port"

	UserName = "username"

	Password = "password"

	Key = "key"

	DiscoveryTarget = "discovery.target"

	Timeout = "timeout"

	SNMPCheckTimeoutSeconds = "snmp.check.timeout.seconds"

	SNMPCheckRetries = "snmp.check.retries"

	ConfigDirectory = "config"

	Errors = "errors"

	Result = "result"

	Context = "context"

	Error = "error"

	ErrorCode = "error.code"

	Message = "message"

	GoroutineResults = "goroutine.results"

	CorrelationMetrics = "correlation.metrics"

	Objects = "objects"

	Object = "object"

	CloudActiveServices = "cloud.active.services"

	ObjectTarget = "object.target"

	ObjectInstanceId = "object.instance.id"

	ObjectType = "object.type"

	ObjectCategory = "object.category"

	ObjectContext = "object.context"

	ObjectHostUUID = "object.host.uuid"

	ObjectVendor = "object.vendor"

	ObjectTypeSwitch = "Switch"

	ObjectTypeFirewall = "Firewall"

	ObjectTypeRuckusWireless = "Ruckus Wireless"

	CloudServiceDiscoveryTags = "cloud.service.discovery.tags"

	Status = "status"

	StatusSucceed = "succeed"

	StatusFail = "fail"

	StatusUnknown = "Unknown"

	StatusUp = "Up"

	StatusMaintenance = "Maintenance"

	StatusDown = "Down"

	StatusUnreachable = "Unreachable"

	StatusEnabled = "Enabled"

	StatusDisabled = "Disabled"

	StartedTimeSeconds = "started.time.seconds"

	StartedTime = "started.time"

	BlankString = ""

	ColonSeparator = ":"

	EqualSeparator = "="

	DotSeparator = "."

	Separator = "|"

	MotadataSeparator = "_|@#|_"

	SpaceSeparator = " "

	CommaSeparator = ","

	ForwardSlashSeparator = "/"

	NewLineSeparator = "\n"

	PipeSeparator = '|'

	TabSeparator = "\t"

	InstanceSeparator = "_"

	TimeFormat = "2006-01-02 15:04:05"

	CloudTimeFormat = "2006-01-02 15:04:05.000000"

	TimeFormatCloud = "2006-01-02"

	ObjectCredentialProfile = "object.credential.profile"

	CredentialProfileName = "credential.profile.name"

	CredentialProfileProtocol = "credential.profile.protocol"

	DiscoveryCredentialProfiles = "discovery.credential.profiles"

	ObjectHost = "object.host"

	ObjectName = "object.name"

	ObjectOSName = "object.os.name"

	SSBinPath = "ss.bin.path"

	ObjectTypeWindows = "Windows"

	ObjectTypeWindowsCluster = "Windows Cluster"

	ObjectTypeHyperV = "Hyper-V"

	ObjectTypeHyperVCluster = "Hyper-V Cluster"

	ObjectTypeLinux = "Linux"

	ObjectTypeIBMAIX = "IBM AIX"

	ObjectTypeIBMAS400 = "IBM AS/400"

	ObjectTypeHPUX = "HP-UX"

	ObjectTypeSolaris = "Solaris"

	ObjectSystemOID = "object.system.oid"

	ObjectTypeCitrixXen = "Citrix Xen"

	ObjectTypeCitrixXenCluster = "Citrix Xen Cluster"

	ObjectTypeVMwareESXi = "VMware ESXi"

	ObjectTypeVcenter = "vCenter"

	ObjectTypeSMG = "Symantec Messaging Gateway"

	ObjectTypeNutanix = "Nutanix"

	ObjectTypePrism = "Prism"

	ObjectTypeCiscovManage = "Cisco vManage"

	ObjectTypeCiscovSmart = "Cisco vSmart"

	ObjectTypeCiscovBond = "Cisco vBond"

	ObjectTypeCiscovEdge = "Cisco vEdge"

	ObjectTypeCiscoMeraki = "Cisco Meraki"

	ObjectTypeCiscoMerakiSwitch = "Cisco Meraki Switch"

	ObjectTypeCiscoMerakiRadio = "Cisco Meraki Radio"

	ObjectTypeCiscoMerakiSecurity = "Cisco Meraki Security"

	ObjectTypeCiscoACI = "Cisco ACI"

	ObjectTypeNetAppONTAPCluster = "NetApp ONTAP Cluster"

	ObjectTypeTanzuKubernetes = "Tanzu Kubernetes"

	ObjectTypeKubernetes = "Kubernetes"

	ObjectTypeIBMFlashSystem = "IBM FlashSystem"

	ObjectTypeHPEStoreOnce = "HPE StoreOnce"

	ObjectTypeHPEPrimera = "HPE Primera"

	ObjectTypeHPE3PAR = "HPE 3PAR"

	ObjectTypeDellEMCUnity = "Dell EMC Unity"

	ObjectTypeNSXT = "NSXT"

	ObjectTypeMariaDB          = "MariaDB"
	ObjectTypeSQLServer        = "SQL Server"
	ObjectTypeMySQL            = "MySQL"
	ObjectTypePostgreSQL       = "PostgreSQL"
	ObjectTypeSAPHANA          = "SAP HANA"
	ObjectTypeOracleDatabase   = "Oracle Database"
	ObjectTypeOracleRACCluster = "Oracle RAC Cluster"
	ObjectTypeMongoDB          = "MongoDB"

	ObjectCategoryWireless = "Wireless"

	ObjectCategoryOther = "Other"

	ObjectCategoryServer = "Server"

	ObjectCategoryNetwork = "Network"

	ObjectCategoryVirtualization = "Virtualization"

	ObjectCategoryHCI = "HCI"

	ObjectCategorySDN = "SDN"

	ObjectCategoryStorage = "Storage"

	ObjectCategoryContainerOrchestration = "Container Orchestration"

	Id = "id"

	PluginId = "plugin.id"

	MACAddressRegexPattern = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$"

	IPAddressRegexPattern = "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"

	IPv6AddressRegexPattern = "\\b^((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))\\s*$\\b"

	ExponentialRegexPattern = "(\\d+.\\d+E.*)"

	NumberRegexPattern = "\\d+"

	DecimalRegexPattern = "\\d+.\\d+"

	NewLineRegexPattern = "\r\n"

	CarriageReturnRegexPattern = "\r"

	ObjectDiscoveryMethod = "object.discovery.method"

	ObjectDiscoveryMethodAgent = "AGENT"

	DiscoveryOID = "discovery.oid"

	AccessId = "cloud.access.id"

	SecretKey = "cloud.secret.key"

	ClientId = "cloud.client.id"

	TenantId = "cloud.tenant.id"

	ObjectRegion = "object.region"

	ObjectMakeModel = "object.make.model"

	ObjectAccountId = "object.account.id"

	ObjectParentIP = "object.parent.ip"

	ObjectResourceGroup = "object.resource.group"

	PingCheckRetries = "ping.check.retries"

	TopologyPluginOIDs = "topology.plugin.oids"

	Command = "command"

	RunbookPluginScriptVariables = "runbook.plugin.variables"

	MetricPluginScriptVariables = "metric.plugin.variables"

	StackTraceSizeInBytes = 1 << 10

	EventDiscovery = "discovery"

	EventPoll = "metric.poll"

	Linux = "linux"

	Process = "system.process"

	ProcessName = "system.process.name"

	ProcessHandles = "system.process.handles"

	ProcessId = "system.process.id"

	ProcessUser = "system.process.user"

	ProcessCommand = "system.process.command"

	ProcessThreads = "system.process.threads"

	ProcessCPUPercent = "system.process.cpu.percent"

	ProcessMemoryPercent = "system.process.memory.used.percent"

	ProcessVirtualMemoryBytes = "system.process.virtual.memory.bytes"

	ProcessMemoryBytes = "system.process.memory.used.bytes"

	ProcessMemoryUsedBytes = "system.process.memory.used.bytes"

	ProcessIOOpsPerSec = "system.process.io.ops.per.sec"

	ProcessStartedTimeSeconds = "system.process.started.time.seconds"

	ProcessStartedTime = "system.process.started.time"

	ProcessIOBytesPerSec = "system.process.io.bytes.per.sec"

	ProcessUptimeSeconds = "system.process.uptime.sec"

	ProcessUptime = "system.process.uptime"

	DestinationIP = "system.process.destination.ip"

	DestinationPort = "system.process.destination.port"

	SourceIP = "system.process.source.ip"

	SourcePort = "system.process.source.port"

	NetworkConnection = "system.process.network.connection"

	VRFName = "vrf.name"

	Solaris = "solaris"

	Windows = "windows"

	CPUCoreUsedPercent = "system.cpu.core.percent"

	MetricName = "metric.name"

	URLBuffer = "url-buffer"

	HDB = "hdb"

	Database = "database"

	Postgres = "postgres"

	MySQL = "mysql"

	SQLServer = "sqlserver"

	MySQLConnectionURL = "%s:%s@tcp(%s:%d)/%s?timeout=%ds"

	CitrixRunningPowerState = "Running"

	CitrixHaltedPowerState = "Halted"

	CitrixPausedPowerState = "Paused"

	CitrixSuspendedPowerState = "Suspended"

	VMNode = "vm"

	HostSystem = "HostSystem"

	VirtualMachine = "VirtualMachine"

	Datastore = "Datastore"

	ClusterComputeResource = "ClusterComputeResource"

	ResourcePool = "ResourcePool"

	Network = "Network"

	Datacenter = "Datacenter"

	RunbookPluginVariables = "runbook.plugin.variables"

	VMName = "vm.name"

	ServiceName = "service.name" // debug/info/error message related const

	InfoMessageConnectionDestroyed = "disconnecting from %s : %s"

	ErrorMessageCommandExecutionFailed = "Command Execution Error: Failed to execute command %s on target %s with error %s."

	InfoMessageConnectionEstablished = "Connected to %s:%d"

	ErrorMessageVendorNotSupported = "Unsupported Vendor: Please contact Motadata support team for help on '%s.'"

	ErrorMessageConnectionTimeout = "Timeout Error: The %s request for %s:%d has timed out."

	ErrorMessageInvalidPort = "Connection Error: Invalid Port %d"

	ErrorMessageConnectionFailed = "Connection Error: Failed to Establish %s Connection on %s:%d"

	ErrorMessageInvalidCredentials = "Authentication Error: Invalid Credentials for %s:%d"

	ErrorMessageInvalidURLPrivilege = "HTTP Error: User might not have privileges to access the URL %s on %s."

	ErrorMessageOIDGroupNotFound = "OID Group Not Found."

	ErrorMessageOIDGroupTypeNotFound = "OID Group Type Not Found."

	DebugMessageRequest = "Request of : %s"

	DebugMessageResult = "Result %s : %s"

	InfoMessageCloudClientConnected = "%s client connected to %s"

	InfoMessageCloudClientConnecting = "%s client connecting to %s"

	AzureStorageGroup = "Azure Storage Resource group: %s target: %s "

	ErrorMessageInvalidSecret = "Connection Error: The Secret Key specified for %s Cloud might be invalid."

	ErrorMessageInvalidAccessId = "Connection Error: The Access Key specified for  %s Cloud might be invalid."

	ErrorMessageInvalidTenantId = "Connection Error: The Tenant ID specified for  %s Cloud might be invalid."

	ErrorMessageInvalidClientId = "Connection Error: The Client ID specified for  %s Cloud might be invalid."

	ErrorMessageInvalidSubscriptionId = "Connection Error: The Subscription ID specified for  %s Cloud might be invalid."

	ErrorMessageInvalidResourceGroup = "Connection Error: The Resource Group specified for  %s Cloud might be invalid."

	ErrorMessageAzureInstanceNotFound = "Instance Not Found: Instance '%s' not found on '%s'."

	ErrorMessageAWSInstanceNotFound = "Instance Not Found : Instance '%s' from '%s' was not found in the '%s' region."

	ErrorMessageFailedToListObjects = "Failed to list objects of %s service"

	ErrorMessagePluginNotFound = "Plugin Type not found"

	ErrorMessageRequestNotFound = "Request Type not found"

	ErrorMessageFailedToSerialize = "Serialization Error: Unable to ingest data."

	ErrorMessageInvalidUserName = "Authentication Error: Invalid Username."

	ErrorMessageInvalidAuthPassword = "Authentication Error: Invalid Password."

	ErrorMessageNoResponse = "No response: %s"

	ErrorMessageBadResponse = "Bad Response %s"

	ErrorMessageSSLVerificationFailed = "SSL Verification Failed : %s"

	ErrorMessageNoActiveMetrics = "Bad Response %v"

	ErrorMessageInvalidDatabase = "Invalid Database"

	ErrorMessageInvalidHost = "Connection Error: Invalid Host %s"

	ErrorMessageUnauthorizedDatabaseAccess = "Connection Error: Permission denied for %s database "

	ErrorMessageInvalidLoginPrivilege = "Connection Error: User might not have sufficient privileges to log on to %s on %s"

	ErrorMessageUnknownDriver = "Unknown Driver"

	ErrorMessageDHCPServiceNotRunning = "Windows DHCP services are not currently running"

	ErrorMessagePingFailed = "Network Error: Ping failed for the specified device."

	ErrorMessageInvalidDomain = "Invalid domain %s"

	ErrorMessageUnableToResolveDomainName = "Unable to resolve domain name"

	ErrorMessageInvalidCertificate = "Certificate verification failed or has expired"

	ErrorMessageNoSSLCertificateFound = "No valid SSL certificate found"

	ErrorMessageInvalidLeaseFile = "Command Error: Lease file not found on {} server"

	ErrorMessageInvalidConfigFile = "Command Error: Config file not found on {} server"

	ErrorMessageUnableToGetFileStat = "Error occurred while reading file stat: %s"

	ErrorMessageFailedToReadDir = "Error occurred while reading dir: %s"

	ErrorMessageFailedToParseCertificate = "Error occurred while parsing client cert/key for: %s"

	ErrorMessageFailedToParseCACertificate = "Error occurred while parsing CA cert for: %s"

	DebugMessageProxyServerRequest = "Request to URL: %s routed through proxy server: %s"

	//Log related Constants

	PathSeparator = string(os.PathSeparator)

	WindowsPathSeparator = "\\"

	OSWindows = PathSeparator == WindowsPathSeparator

	LogLevelTrace = 0

	LogLevelDebug = 1

	LogLevelInfo = 2

	LogDirectory = "logs"

	LogFileDateFormat = "02-January-2006"

	LogFileTimeFormat = "03:04:05.000000 PM"

	LogFile = "@@@-###.log"

	//Error Code

	ErrorCodeInvalidDatabase = "MD005"

	ErrorCodeInvalidHost = "MD310"

	ErrorCodeDatabaseLogin = "MD013"

	ErrorCodeUnauthorizedDatabaseAccess = "MD006"

	ErrorCodeInternalError = "MD031"

	ErrorCodeTimeout = "MD004"

	ErrorCodeSSLVerificationFailed = "MD123"

	ErrorCodePingFailed = "MD001"

	ErrorCodeInvalidPort = "MD002"

	ErrorCodeInvalidCredentials = "MD003"

	ErrorCodeInvalidCertificate = "MD0312"

	ErrorCodeUnauthorizedURLAccess = "MD007"

	ErrorCodeConnectionFailed = "MD047"

	ErrorCodeFTPServiceNotRunning = "MD070"

	ErrorCodeInvalidPublicOrPrivateSSHKey = "MD103"

	ErrorCodeConnectionReset = "MD058"

	ErrorCodeCommandExecutionFailed = "MD059"

	ErrorCodeOIDGroupNotFound = "MD060"

	ErrorCodeOIDGroupTypeNotFound = "MD061"

	ErrorCodeInvalidLinuxORUnixHost = "MD099"

	ErrorCodeInvalidOIDValue = "MD063"

	ErrorCodeRoutingProtocolNotFound = "MD074"

	ErrorCodeInvalidQueryTime = "MD144"

	ErrorCodeVRFNotFound = "MD077"

	ErrorCodeVLANNotFound = "MD078"

	ErrorCodeIPSLANotFound = "MD082"

	ErrorCodeSymantecEmailGatewayNotFound = "MD075"

	ErrorCodeIBMTapeLibraryNotFound = "MD140"

	ErrorCodeWirelessControllerNotFound = "MD076"

	ErrorCodeBadResponse = "MD083"

	ErrorCodeNoItemFound = "MD084"

	ErrorCodeSTPNotFound = "MD080"

	ErrorCodeSiteVPNNotFound = "MD048"

	ErrorCodeSNMPContextConfigurationError = "MD102"

	ErrorCodeVendorNotSupported = "MD049"

	ErrorCodeCloudInstanceNotFound = "MD072"

	ErrorCodeNoResponse = "MD056"

	ErrorCodeBadRequest = "MD022"

	ErrorCodeAPIInvalidAccessKey = "MD009"

	ErrorCodeAPIInvalidSecretKey = "MD008"

	ErrorCodeSNMPAgentSystemOIDNotFound = "MD101"

	ErrorCodeRemoteVPNNotFound = "MD081"

	ErrorCodeMulticastNotFound = "MD079"

	ErrorCodeWindowsClusterServiceNotRunning = "MD071"

	ErrorCodeAzureInvalidResourceGroup = "MD054"

	ErrorCodeAPIInvalidTenantId = "MD010"

	ErrorCodeAPIInvalidSubscriptionId = "MD011"

	ErrorCodeAPIInvalidClientId = "MD012"

	ErrorCodeInvalidRegionGroup = "MD055"

	ErrorCodeInvalidSecretKey = "MD008"

	ErrorCodeInvalidAccessKey = "MD009"

	ErrorCodeInvalidUserName = "MD108"

	ErrorCodeInvalidAuthPassword = "MD109"

	ErrorCodeFailedToListObjects = "MD110"

	ErrorCodeNoActiveMetrics = "MD113"

	ErrorCodeNoCiscoStackMetrics = "MD114"

	ErrorCodeHyperVClusterServiceNotRunning = "MD096"

	ErrorCodeADServiceNotRunning = "MD069"

	ErrorCodeInvalidExchangeHost = "MD116"

	ErrorCodeDHCPServiceNotRunning = "MD065"

	ErrorCodeDNSServiceNotRunning = "MD097"

	ErrorCodeTerminalServiceNotRunning = "MD064"

	ErrorCodeInvalidSecret = "MD136"

	ErrorCodeInvalidDomain = "MD067"

	ErrorCodeEmailServiceDown = "MD026"

	ErrorCodeIISServiceNotRunning = "MD068"

	ErrorCodeDHCPLeaseFileNotFound = "MD050"

	ErrorCodeDHCPConfigFileNotFound = "MD051"

	ErrorCodeTraceRouteFailed = "MD111"

	ErrorCodeUnAuthorizedURLAccess = "MD007"

	ErrorCodeNutanixHostNotFound = "MD203"

	//IP SLA related error coded

	ErrorIPSLANotSupported = "MD201"

	ErrorCodeNoAccess = "MO202"

	ErrorCodeUnableToGetFileStat = "MD202"

	ErrorCodeFailedToReadDir = "MD207"

	ErrorCodeUnableToGetWD = "MD204"

	ErrorCodeFailedToChangeDir = "MD214"

	ErrorCodeUnableToOpenFile = "MD211"

	ErrorCodeUnableToTransferFile = "MD203"

	ErrorCodeFailedToCreateFile = "MD209"

	ErrorCodeFailedToCopyFile = "MD210"

	ErrorCodeUnableToWriteFile = "MD212"

	ErrorCodeUnableToCreateClient = "MD200"

	ErrorCodeFailedToRebootVM = "MD225"

	ErrorCodeFailedToResetVM = "MD221"

	ErrorCodeFailedToShutDownGuestOS = "MD229"

	ErrorCodeFailedToStartVM = "MD215"

	ErrorCodeFailedToStopVM = "MD216"

	ErrorCodeFailedToTakeSnapShot = "MD217"

	ErrorCodeFailedToSuspendVM = "MD218"

	ErrorCodeFailedToStandByGuestOS = "MD219"

	ErrorCodeFailedToShutDownSystem = "MD220"

	ErrorCodeFailedToStartService = "MD222"

	ErrorCodeFailedToRetrieveRunningService = "MD223"

	ErrorCodeFailedToStopService = "MD224"

	ErrorCodeFailedToRefreshVM = "MD227"

	ErrorCodeFailedToRebootSystem = "MD226"

	ErrorCodeFailedToRestartService = "MD228"

	ErrorCodeNoExtremeStackMetrics = "MD230"

	// cloud error code constant

	CloudErrorCodeInvalidTenantId = 90002

	CloudErrorCodeInvalidClientId = 700016

	CloudErrorCodeInvalidSecretId = 7000215

	RunbookPluginOIDS = "runbook.plugin.oids"

	MetricPluginOIDS = "metric.plugin.oids"

	RunbookPluginCategory = "runbook.plugin.category"

	PluginEngineLogDirectory = "plugin-engine"

	SystemTags = "system.tags"

	TagSeparator = ":"

	MaxPoolSize = 10

	URL = "url"

	IPVersionSeparator = " | "

	ASCIIJunkCharacterPattern = "(\\x9B|\\x1B\\[)[0-?]*[ -/]*[@-~]"

	SNMPDigitRegexPattern = "[0-9]+"

	PluginContextDirectory = "plugin-contexts"

	Interface = "interface"

	InterfaceIndex = "interface.index"

	InterfaceName = "interface.name"

	InterfaceOperationalStatus = "interface.operational.status"

	InterfaceSpeedBytesPerSec = "interface.speed.bytes.per.sec"

	InterfaceType = "interface.type"

	InterfaceDescription = "interface.description"

	InterfaceAddress = "interface.address"

	InterfaceAlias = "interface.alias"

	InterfaceAdminStatus = "interface.admin.status"

	InterfaceOutPackets = "interface.out.packets"

	InterfaceSentOctets = "interface.sent.octets"

	InterfaceReceivedOctets = "interface.received.octets"

	InterfaceInPackets = "interface.in.packets"

	InterfaceReceivedDiscardPackets = "interface.received.discard.packets"

	InterfaceSentDiscardPackets = "interface.sent.discard.packets"

	InterfaceDiscardPackets = "interface.discard.packets"

	InterfaceReceivedErrorPackets = "interface.received.error.packets"

	InterfaceSentErrorPackets = "interface.sent.error.packets"

	InterfacePackets = "interface.packets"

	InterfaceErrorPackets = "interface.error.packets"

	InterfaceLastChange = "interface.last.change"

	InterfaceBitType = "interface.bit.type"

	InterfaceIPAddress = "interface.ip.address"

	InterfaceLocalIPAddress = "interface.local.ip.address"

	Latency = "ipsla.latency.ms"

	RTTStatus = "ipsla.rtt.completion.status"

	AdminStatus = "ipsla.admin.status"

	Owner = "ipsla.owner"

	InterfaceDiscovery = "interface.discovery"

	DiscoverDownInterfaceStatus = "discover.down.interface.status"

	SNMPInterfaceStatusUp = "up"

	SNMPInterfaceStatusDown = "down"

	WirelessAccessPoint = "wireless.access.point"

	WirelessAccessPointMACAddress = "wireless.access.point.mac.address"

	WirelessAccessPointOperationalStatus = "wireless.access.point.operational.status"

	WirelessAccessPointStatus = "wireless.access.point.status"

	OIDs = "oids"

	CompositeOIDs = "compositeOIDs"

	IPSLA = "ipsla"

	IPSLAOperationType = "ipsla.operation.type"

	Ok = 1 // This indicates that the IP SLA operation is currently running without any issues packets are being sent and received as expected.

	EnableOk = 21 // This indicates that the configuration for the IP SLA operation has been applied correctly, and the operation is running.

	SNMPDevice = "snmp"

	WirelessAccessPointIPAddress = "wireless.access.point.ip.address"

	AzureMetricTimeStampLayout = "2006-01-02T15:04:00Z"

	MetricAverage = "Average"

	MetricMaximum = "Maximum"

	MetricMinimum = "Minimum"

	MetricTotal = "Total"

	MetricCount = "Count"

	Office365URL = "https://login.microsoftonline.com/%s/oauth2/token"

	SystemLocation = "system.location"

	SystemOID = "system.oid"

	SystemDescription = "system.description"

	SystemName = "system.name"

	SourceFilePath = "src.file.path"

	StoragePath = "path"

	DeleteFile = "delete.src.file"

	TFTP = "TFTP"

	FTP = "FTP"

	SFTP = "SCP/SFTP"

	// -----> NCM config constants <-------

	PromptIndex = "prompt.index"

	FileTransferProtocolSCPSFTP = "SCP/SFTP"

	FileTransferProtocolDirectCopy = "NONE"

	FileTransferProtocol = "file.transfer.protocol"

	CommandGroups = "command.groups"

	Reboot = "Reboot"

	GetConfigurationRegisterInfo = "Get Configuration Register Info"

	UpdateConfigurationRegister = "Update Configuration Register"

	GetFreeSpace = "Get Free Space"

	FirmwareFreeSpace = "config.firmware.image.free.space"

	FirmwareUpgradeImageFileSize = "config.firmware.image.file.size"

	ConfigTemplateOperationCommandResponseRequired = "operation.response.required"

	OperationTypeInfo = "Info"

	BackupFileContent = ".file.content"

	ConfigTemplateOperationCommand = "operation.command"

	ConfigTemplateOperationCommandTimeout = "operation.timeout"

	ConfigTemplateOperationPrompt = "operation.prompt"

	ConfigTemplateOperationPromptCommand = "operation.prompt.command"

	ConfigPassword = "config.password"

	ConfigManageDirectory = "config-management"

	FileServerHost = "server.host"

	FileServerUserName = "server.username"

	FileServerPassword = "server.password"

	FirmwareDeviceBackupFileName = "config.firmware.device.backup.file.name"

	FirmwareUpgradeImageFiles = "firmware-files"

	FirmwareFileName = "config.firmware.image.file.name"

	Attributes = "attributes"

	ResultPattern = "result.pattern"

	AttributeName = "attribute.name"

	ExpectedValue = "expected.value"

	Prompt = "prompt"

	UniqueIdentifier = "linux.plugin.command.identifier."

	EnablePrompt = "enable.prompt"

	EnableUsername = "enable.username"

	EnablePassword = "enable.password"

	ConfigTemplate = "config.template"

	StartUpBackup = "Backup_Startup"

	OperationTypeSync = "Sync"

	ProxyServer = "proxy.server"

	ProxyPort = "proxy.port"

	ProxyUserName = "proxy.username"

	ProxyPassword = "proxy.password"

	TerminalRequired = "terminal.required"

	DefaultCommandDelayTime = 1500 // milli second

	DefaultBannerPrintTime = 5000 // millisecond - Will be used to read the banner from the device after login.

	DefaultCommandTimeout = 5000 // milli second

	ReadBufferBytes = 204800

	ConfigWorklogs = "config.work.logs"

	EnablePasswordPattern = "Password"

	ConnectionKey = "connection"

	WorkLogSeparator = "_|@@|_"

	EnablePromptKey = "enable prompt"

	EnableUserNameKey = "enable username"

	EnablePasswordKey = "enable password"

	PromptKey = "prompt"

	ConfigTemplateOperationBackupStartPrompt = "operation.backup.start.prompt"

	MongoConnectionURI = "mongodb://%s:%s@%s:%d"

	MongoConnectionURINoAuth = "mongodb://%s:%d"

	// -----> Error/Info messages <-------

	ErrorMessageUnableToCreateClient      = "Error occurred while creating %s client."
	ErrorMessageUnableToWriteFile         = "Error occurred while writing file %s"
	ErrorMessageFailedToCreateFile        = "Error occurred while creating file: %s"
	ErrorMessageFailedToCopyFile          = "Error occurred while coping source file: %s and destination file: %s"
	ErrorMessageInvalidCredentialForFTP   = "Invalid Credentials %s:%s"
	ErrorMessageUnableToTransferFile      = "Error occurred while transferring file: %s."
	ErrorMessageUnableToOpenFile          = "Error occurred while opening file %s"
	ErrorMessageFailedToChangeDir         = "Failed to change working directory"
	ErrorMessageUnableToGetWD             = "Error occurred while fetching WD"
	ErrorMessageSourceNotProvided         = "Source File Path not found"
	ErrorMessageFailReadingOutput         = "Got Error while reading output, error : %v for host %s"
	ErrorMessageTimeOutWhileReadingOutput = "Timed out while reading output for command : %s , pattern : %s for host %s"
	ErrorMessageReadingOutputTimeOut      = "Timed out while reading output for command : %s , pattern : %s"
	InfoMessageExecutingCommand           = "Executing command : %s for host %s"
	InfoMessageExecutedCommand            = "Command : %s executed for host %s with execution time is : %d"
	InfoMessageTryingTemplate             = "Trying template : %s for the host : %s"
	InfoMessageQualifiedTemplate          = "Qualified template is : %s for the host : %s"
	InfoMessageTryingCredentialProfile    = "Trying credential profile : %s for the host : %s"
	InfoMessageQualifiedCredentialProfile = "Qualified credential profile is : %s for the host : %s"
	InfoMessageBackupExecutionResult      = "%s result for the template : %s with host : %s is %v"
	InfoMessageExecutedCommandMessage     = "Successfully executed command : %s for the host : %s"
	InfoMessageBackupEndPrompt            = "Backup end prompt for the host : %s is : %s, prompt index : %d"
	InfoCommandNotFound                   = "command not found for the %s , template : %s,  host : %s"
	InfoMessageResultPatternResult        = "Result Pattern %s for operation %s for object %s"

	ErrorMessageSessionCreationIssue                      = "Error %v occurred for the host : %s while creating ssh session"
	ErrorMessageWhileInitSSHSession                       = "Error while initialising session for the host : %s:%d"
	ErrorMessageWhileRequestingPTY                        = "Error %v occurred while requesting Pty for the host : %s"
	ErrorMessageWhileRequestingPTYWithHost                = "Error while requesting Pty for the host : %s:%d"
	ErrorMessageWhileInvokingSSHShell                     = "Error %v occurred while invoking ssh Shell for the host : %s"
	ErrorMessageWhileInvokingSSHShellWithHost             = "Error while invoking ssh shell for the host : %s:%d"
	ErrorMessageWhileSendingEnableCommand                 = "Error %v occurred while sending enable command for the host : %s"
	ErrorMessageWhileSendingEnableCommandWithHost         = "Error while sending enable command for the host : %s:%d"
	ErrorMessageWhileSendingEnableUserCommand             = "Error %v occurred while sending enable user command for the host : %s"
	ErrorMessageWhileSendingEnableUserCommandWithHost     = "Error while sending enable user command for the host : %s:%d"
	ErrorMessageWhileSendingEnablePasswordCommand         = "Error %v occurred while sending enable password command for the host : %s"
	ErrorMessageWhileSendingEnablePasswordCommandWithHost = "Error while sending enable password command for the host : %s:%d"
	ErrorMessageFailToEnterEnableMode                     = "Failed to enter enable mode for the host : %s"
	ErrorMessageFailToEnterEnableModeWithHost             = "Failed to enter enable mode for the host : %s:%d"
	ErrorMessageEnablePasswordNotProvided                 = "Enable Password not provided for the host : %s"
	ErrorMessageCommandExecutionFail                      = "Execution failed for the request : %s for the host : %s"
	ErrorMessagePromptCommandExecutionFail                = "Prompt command execution failed for the request : %s for the host : %s"
	ErrorMessageConfigOutputError                         = "Error found in output of the command : %s for host %s, output : %s"
	ErrorMessageConfigOutputPromptCommandError            = "Error found in output of the prompt command : %s for host %s, output : %s"
	ErrorMessageConfigOutputErrorCheck                    = "Error found in output of the command"
	ErrorMessageConfigTemplateNotAvailable                = "No config template provided for the host : %s"
	ErrorMessageConfigOperationCommandsNotAvailable       = "No config operation commands provided for the host : %s, operation : %s"
	ErrorMessageOperationCommandNotAvailable              = "No operation group command added for the request : %s, template : %s, protocol : %s, host : %s"
	InfoMessageExtractingBackupResult                     = "Extracting backup result for host : %s"
	InfoMessageBackupFileName                             = "Backup file name %s"
	ErrorMessageUnsupportedIPSLA                          = "Unsupported IP SLA: Device does not support IP SLA for the object type %s."
	ErrorMessageInsufficientAccess                        = "Access Error: Insufficient permissions for the SNMP set operation."
	ErrorMessageSNMPSet                                   = "SNMP Error: %s at index %d during SNMP set operation."
	ErrorMessageInsufficientSpace                         = "Device does not have enough space"
	ErrorMessageAvailableSpaceWithFileSize                = "Available space is %d and file size is %d for object.ip %s"
	ErrorAvailableSpaceWithFileSize                       = "Available space is %d and file size is %d"
	ErrorMessageCompilingRegex                            = "Error in compiling regex for operation %s with result pattern %s for object %s"
	ErrorMessageUnableToFindResultPattern                 = "Result Pattern %s not found for operation %s for object %s"
	ErrorUnableToFindResultPattern                        = "Result Pattern %s not found for operation %s"
	ErrorMessageExpectedValueDoesNotMatchResultPattern    = "Expected Value %s does not match with Result Pattern %s for operation %s for object %s"
	ErrorExpectedValueDoesNotMatchResultPattern           = "Expected Value %s does not match with Result Pattern %s for operation %s"

	ErrorCodeFailExecutingCommand                      = "MD128"
	ErrorCodeWhileInitSSHSession                       = "MD129"
	ErrorCodeWhileSendingEnableCommandWithHost         = "MD130"
	ErrorCodeWhileSendingEnableUserCommandWithHost     = "MD131"
	ErrorCodeWhileSendingEnablePasswordCommandWithHost = "MD132"
	ErrorCodeFailToEnterEnableModeWithHost             = "MD133"
	ErrorCodeEnablePasswordNotProvided                 = "MD134"
	ErrorCodeConfigTemplateNotAvailable                = "MD135"
	ErrorCodeConfigOperationCommandNotAvailable        = "MD136"
	ErrorCodeWhileRequestingPTYWithHost                = "MD117"
	ErrorCodeWhileInvokingSSHShellWithHost             = "MD118"
	ErrorCodeUnableToCompileRegex                      = "MD137"
	ErrorCodeResultPatternValueNotFound                = "MD138"
	ErrorCodeExpectedValueIsNotEqualToResultPattern    = "MD139"
)

var (
	CurrentDir, _ = os.Getwd()

	SensitiveFields = []string{"$$$password$$$", "$$$snmp.private.password$$$", "$$$snmp.community$$$",
		"$$$snmp.authentication.password$$$", "$$$snmp.community$$$",
		"$$$snmp.authentication.password$$$", "$$$snmp.private.password$$$",
		"$$$user.password$$$", "$$$secret.key$$$", "$$$access.id$$$",
		"$$$client.id$$$", "$$$ssh.key$$$", "$$$passphrase$$$", Password,
		"snmp.community", "snmp.authentication.password", "snmp.private.password",
		ObjectContext, DiscoveryCredentialProfiles,
		"cloud.secret.key", "cloud.client.id", "cloud.access.id", "cloud.tenant.id",
		"ssh.key", "passphrase", "SNMP Community", "Authentication Password",
		"Private Password", "metric.plugin.variables", "mail.server.password",
		"ldap.server.password", "discovery.context", "credential.profile.context",
		"user.password", "enable.password", "storage.profile.context"}

	IPPattern = regexp.MustCompile("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$")

	IPv6Pattern = regexp.MustCompile("\\b^((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))\\s*$\\b")

	CompositeOIDPattern = regexp.MustCompile("[/*+()\\-%]")
)
