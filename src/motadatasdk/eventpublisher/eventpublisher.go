/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package eventpublisher

import (
	"encoding/base64"
	"errors"
	"fmt"
	zmq "github.com/pebbe/zmq4"
	. "motadatasdk/globals"
	"time"
)

var context *zmq.Context

func init() {

	context, _ = zmq.NewContext()
}

type EventPublisher struct {
	publisher *zmq.Socket

	events chan string

	eventTopic string

	logger *Logger

	endpoint string

	shutdown bool
}

func New(host string, port uint16, eventTopic string,
	size uint, logger *Logger) (EventPublisher, error) {

	publisher := EventPublisher{}

	publisher.publisher, _ = context.NewSocket(zmq.PUSH)

	publisher.eventTopic = eventTopic

	publisher.logger = logger

	publisher.endpoint = "tcp://" + host + ":" + ToString(port)

	if publisher.publisher != nil {

		if size > 0 {

			publisher.events = make(chan string, size)

		} else {

			publisher.events = make(chan string, 1) //default size
		}

	} else {

		return publisher, errors.New("failed to create event publisher")
	}

	return publisher, nil

}

func (publisher *EventPublisher) Start() error {

	if publisher.publisher != nil {

		publisher.publisher.SetLinger(0 * time.Second)

		publisher.publisher.SetSndhwm(5000)

		publisher.publisher.SetSndtimeo(0 * time.Second)

		err := publisher.publisher.Connect(publisher.endpoint)

		if err != nil {

			publisher.logger.Fatal(MotadataString(fmt.Sprintf("failed to start event publisher, reason: %v", err.Error())))

			publisher.Shutdown()

			return err
		} else {

			go publisher.publish()

			publisher.logger.Info(MotadataString(fmt.Sprintf("event publisher connected to %v successfully...", publisher.endpoint)))
		}

	} else {

		publisher.logger.Fatal("failed to start event publisher, reason: invalid socket")

		return errors.New("failed to start event publisher, reason: invalid socket")
	}

	return nil
}

func (publisher *EventPublisher) publish() {

	for {

		if !publisher.shutdown {

			select {

			case event := <-publisher.events:

				if len(event) > 0 {
					publisher.publisher.Send(publisher.eventTopic+base64.StdEncoding.EncodeToString([]byte(event)), 0)

				}
			}
		} else {

			break
		}
	}
}

func (publisher *EventPublisher) Publish(event MotadataMap) bool {

	if publisher.publisher != nil {

		publisher.events <- event.ToJSON().ToString()

		return true
	}

	return false
}

func (publisher *EventPublisher) Shutdown() {

	if publisher.publisher != nil {

		publisher.shutdown = true

		publisher.publisher.Disconnect(publisher.endpoint)

		publisher.publisher.Close()

		close(publisher.events)

		publisher.logger.Info("event publisher stopped...")
	}
}
