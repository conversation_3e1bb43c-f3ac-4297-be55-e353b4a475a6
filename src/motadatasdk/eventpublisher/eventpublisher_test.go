/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package eventpublisher

import (
	zmq "github.com/pebbe/zmq4"
	"github.com/stretchr/testify/assert"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"testing"
	"time"
)

var (
	loggerObj = NewLogger("EventBus", consts.BlankString)

	count = 0
)

func startSubscriber() {

	subscriber, _ := context.NewSocket(zmq.PULL)

	subscriber.SetLinger(0)

	subscriber.Bind("tcp://*:6767")

	message, _ := subscriber.Recv(0)

	if len(message) > 0 {

		count++
	}

	subscriber.Unbind("tcp://*:6767")

	subscriber.Close()

}

func TestEventPublisher(t *testing.T) {

	go startSubscriber()

	time.Sleep(2 * time.Second)

	publisher, _ := New("localhost", uint16(6767), "test", uint(1), &loggerObj)

	publisher.Start()

	time.Sleep(2 * time.Second)

	publisher.Publish(MotadataMap{"a": 1})

	time.Sleep(1 * time.Second)

	publisher.Shutdown()

	assert.Equal(t, 1, count)

}
