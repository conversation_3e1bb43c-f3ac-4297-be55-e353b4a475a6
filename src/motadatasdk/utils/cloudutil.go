/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package utils

import (
	bytes2 "bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/Azure/azure-sdk-for-go/profiles/2019-03-01/resources/mgmt/insights"
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/Azure/go-autorest/autorest"
	"github.com/Azure/go-autorest/autorest/azure/auth"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/cloudwatch"
	"motadatasdk/clients/httpclient"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"net/url"
	"time"
)

//Azure Cloud Methods

type AzureSession struct {
	Authorizer autorest.Authorizer
}

var Office365RequestContext = url.Values{
	"grant_type":    {"client_credentials"},
	"resource":      {"https://graph.microsoft.com"},
	"client_id":     {""},
	"client_secret": {""},
}

func GetAzureSession(context MotadataMap) (*AzureSession, error) {

	principalToken := auth.ClientCredentialsConfig{TenantID: context.GetStringValue(consts.TenantId),
		ClientID: context.GetStringValue(consts.ClientId), ClientSecret: context.GetStringValue(consts.SecretKey),
		Resource: "https://management.azure.com/", AADEndpoint: "https://login.microsoftonline.com"}

	authorizer, err := principalToken.Authorizer()

	session := AzureSession{

		Authorizer: authorizer,
	}

	return &session, err
}

func GetAzureCredential(context MotadataMap) (*azidentity.ClientSecretCredential, error) {

	credential, err := azidentity.NewClientSecretCredential(context.GetStringValue(consts.TenantId), context.GetStringValue(consts.ClientId), context.GetStringValue(consts.SecretKey), nil)

	return credential, err
}

func GetCloudObjectName(context MotadataMap, key string) MotadataString {

	name := context.GetMotadataStringValue(consts.ObjectTarget)

	if name.Contains("(" + context.GetStringValue(key) + ")") {

		return name.ReplaceAll("("+context.GetMotadataStringValue(key)+")", "").Trim(" ")
	}

	return name
}

func Timestamp(value time.Time) MotadataString {

	return MotadataString(value.Format(consts.CloudTimeFormat)).Split("+")[0].TrimSpace()
}

func GetAzureMetricValue(params []MotadataString, client insights.MetricsClient, cloudMetricBatches MotadataMap) error {

	var granularity *string

	if params[3].ToString() == "auto" {

		granularity = nil // means it will automatically select lowest available time granularity for specific metric

	} else {

		granularity = (*string)(&params[3])
	}

	response, err := client.List(context.TODO(), params[0].ToString(), params[1].ToString()+"/"+params[2].ToString(), granularity, params[4].ToString(), params[5].ToString(), nil, params[6].ToString(), consts.BlankString, consts.BlankString, consts.BlankString)

	if err == nil {

		if MotadataString(response.Response.Status).Contains("200") {

			for _, metric := range *response.Value {

				for _, timeSeries := range *metric.Timeseries {

					for _, data := range *timeSeries.Data {

						format, _ := time.Parse(consts.AzureMetricTimeStampLayout, data.TimeStamp.String())

						timestamp := ToString(ToInt(format.Unix()))

						if params[5].ToLower() == MotadataString(consts.MetricAverage).ToLower() && data.Average != nil {

							setAzureMetric(cloudMetricBatches, timestamp, params[7].ToLowerNative(), *data.Average)
						}
						if params[5].ToLower() == MotadataString(consts.MetricTotal).ToLower() && data.Total != nil {

							setAzureMetric(cloudMetricBatches, timestamp, params[7].ToLowerNative(), *data.Total)
						}
						if params[5].ToLower() == MotadataString(consts.MetricCount).ToLower() && data.Count != nil {

							setAzureMetric(cloudMetricBatches, timestamp, params[7].ToLowerNative(), *data.Count)
						}
						if params[5].ToLower() == MotadataString(consts.MetricMaximum).ToLower() && data.Maximum != nil {

							setAzureMetric(cloudMetricBatches, timestamp, params[7].ToLowerNative(), *data.Maximum)
						}

						if params[5].ToLower() == MotadataString(consts.MetricMinimum).ToLower() && data.Minimum != nil {

							setAzureMetric(cloudMetricBatches, timestamp, params[7].ToLowerNative(), *data.Minimum)
						}
					}
				}
			}
		}
	}

	return err
}

func setAzureMetric(cloudMetricBatches MotadataMap, timestamp string, metricName string, value float64) {

	if !cloudMetricBatches.Contains(timestamp) {

		cloudMetricBatches[timestamp] = make(MotadataMap)

	}

	cloudMetricBatches.GetMapValue(timestamp)[metricName] = value
}

func GetAzurePluginErrors(errors []MotadataStringMap, err error, context MotadataMap) []MotadataStringMap {

	context = RemoveSensitiveFields(context)

	if MotadataString(err.Error()).Contains("invalid_client") {

		errors = append(errors, MotadataStringMap{

			consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidSecret, context.GetStringValue(consts.ObjectAccountId)),

			consts.ErrorCode: consts.ErrorCodeInvalidSecretKey,
		})
	} else if MotadataString(err.Error()).Contains("unauthorized_client") {

		errors = append(errors, MotadataStringMap{

			consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidClientId, context.GetStringValue(consts.ObjectAccountId)),

			consts.ErrorCode: consts.ErrorCodeAPIInvalidClientId,
		})
	} else if MotadataString(err.Error()).Contains("invalid_request") {

		errors = append(errors, MotadataStringMap{

			consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidTenantId, context.GetStringValue(consts.ObjectAccountId)),

			consts.ErrorCode: consts.ErrorCodeAPIInvalidTenantId,
		})
	} else if MotadataString(err.Error()).Contains("InvalidSubscriptionId") {

		errors = append(errors, MotadataStringMap{

			consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidSubscriptionId, context.GetStringValue(consts.ObjectAccountId)),

			consts.ErrorCode: consts.ErrorCodeAPIInvalidSubscriptionId,
		})
	} else if MotadataString(err.Error()).Contains("ResourceGroupNotFound") {

		errors = append(errors, MotadataStringMap{

			consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidResourceGroup, context.GetStringValue(consts.ObjectAccountId)),

			consts.ErrorCode: consts.ErrorCodeAzureInvalidResourceGroup,
		})
	} else if MotadataString(err.Error()).Contains("ResourceNotFound") {

		errors = append(errors, MotadataStringMap{

			consts.Message: fmt.Sprintf(consts.ErrorMessageAzureInstanceNotFound, GetCloudObjectName(context, consts.ObjectResourceGroup), context.GetStringValue(consts.ObjectResourceGroup)),

			consts.ErrorCode: consts.ErrorCodeCloudInstanceNotFound,
		})

	} else {

		errors = append(errors, MotadataStringMap{

			consts.Message: err.Error(),

			consts.Error: err.Error(),

			consts.ErrorCode: consts.ErrorCodeInternalError,
		})

	}
	return errors
}

func GetAWSPluginErrors(errors []MotadataStringMap, err error, context MotadataMap) []MotadataStringMap {

	context = RemoveSensitiveFields(context)

	target := consts.BlankString

	if context[consts.ObjectAccountId] != nil {

		target = context.GetStringValue(consts.ObjectAccountId)

	}

	if MotadataString(err.Error()).Contains("SignatureDoesNotMatch") || MotadataString(err.Error()).Contains("InvalidSignatureException") {

		errors = append(errors, MotadataStringMap{

			consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidSecret, target),

			consts.ErrorCode: consts.ErrorCodeInvalidSecretKey,
		})

	} else if MotadataString(err.Error()).Contains("InvalidClientTokenId") || MotadataString(err.Error()).Contains("UnrecognizedClientException") || MotadataString(err.Error()).Contains("InvalidAccessKeyId") {

		errors = append(errors, MotadataStringMap{

			consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidAccessId, target),

			consts.ErrorCode: consts.ErrorCodeInvalidAccessKey,
		})

	} else if MotadataString(err.Error()).Contains("no such host") {

		errors = append(errors, MotadataStringMap{

			consts.Message: fmt.Sprintf("Failed to connect to Cloud %s, reason: Invalid region", target),

			consts.ErrorCode: consts.ErrorCodeInvalidRegionGroup,
		})

	} else if MotadataString(err.Error()).Contains("AuthFailure") {

		errors = append(errors, MotadataStringMap{

			consts.Message: fmt.Sprintf("Failed to connect to Cloud %s,reason: Invalid secret key or Invalid access key", target),

			consts.ErrorCode: consts.ErrorCodeInvalidCredentials,
		})

	} else {

		errors = append(errors, MotadataStringMap{

			consts.Message: err.Error(),

			consts.Error: err.Error(),

			consts.ErrorCode: consts.ErrorCodeInternalError,
		})
	}

	return errors
}

func BuildCloudWatchMetricQuery(params []interface{}, dimensions []*cloudwatch.Dimension) *cloudwatch.MetricDataQuery {

	return &cloudwatch.MetricDataQuery{
		Id: aws.String(ToString(params[0])),
		MetricStat: &cloudwatch.MetricStat{
			Period: aws.Int64(60),
			Stat:   aws.String(ToString(params[3])),
			Metric: &cloudwatch.Metric{
				MetricName: aws.String(ToString(params[1])),
				Dimensions: dimensions,
				Namespace:  aws.String(ToString(params[2])),
			},
		},
	}
}

func SetCloudWatchMetrics(pluginName MotadataString, metricMappers MotadataMap, queries []*cloudwatch.MetricDataQuery, cloudWatchClient *cloudwatch.CloudWatch, timeDiff int64, cloudMetricBatches MotadataMap, lastPollTime int64) error {

	param := &cloudwatch.GetMetricDataInput{
		StartTime:         aws.Time(time.Unix(lastPollTime, 0).UTC()),
		EndTime:           aws.Time(time.Unix(lastPollTime+timeDiff, 0).UTC()),
		MetricDataQueries: queries,
	}

	loggerObj.Debug(MotadataString(fmt.Sprintf("%s result:  %s", pluginName, param)))

	response, err := cloudWatchClient.GetMetricData(param)

	if response != nil && response.MetricDataResults != nil {

		for _, metricDataResult := range response.MetricDataResults {

			if metricDataResult.Values != nil && metricDataResult.Timestamps != nil {

				for i, timestamp := range metricDataResult.Timestamps {

					metrics := make(MotadataMap)

					timestamp := ToString(ToInt(timestamp.Unix()))

					if cloudMetricBatches.Contains(timestamp) {

						metrics = cloudMetricBatches.GetMapValue(timestamp)

					} else {

						cloudMetricBatches[timestamp] = metrics
					}

					metrics[metricMappers.GetStringValue(ToString(*metricDataResult.Id))] = ToFloat(*metricDataResult.Values[i])

				}

			}
		}
	}

	return err
}

func GetCloudWatchMetricValue(pluginName MotadataString, cloudWatchClient *cloudwatch.CloudWatch, params []interface{}) (MotadataFloat64, error) {

	param := &cloudwatch.GetMetricStatisticsInput{
		MetricName: aws.String(ToString(params[3])),
		Namespace:  aws.String(ToString(params[4])),
		Period:     aws.Int64(ToINT(params[0]).ToInt64()),
		StartTime:  aws.Time(params[1].(time.Time)),
		EndTime:    aws.Time(params[2].(time.Time)),
		Statistics: []*string{aws.String(ToString(params[5]))},
		Unit:       aws.String(ToString(params[6])),
	}

	if len(params) > 7 {

		param.Dimensions = params[7].([]*cloudwatch.Dimension)
	}

	loggerObj.Debug(MotadataString(fmt.Sprintf("%s result:  %s", pluginName, param)))

	response, err := cloudWatchClient.GetMetricStatistics(param)

	dataPoints := MotadataIntFloatMap{}

	if response != nil && response.Datapoints != nil {

		for _, dataPoint := range response.Datapoints {

			value := ToInt(dataPoint.Timestamp.Unix())

			if dataPoint.Average != nil {

				dataPoints[value] = *dataPoint.Average
			}
			if dataPoint.Sum != nil {

				dataPoints[value] = *dataPoint.Sum
			}
			if dataPoint.Minimum != nil {

				dataPoints[value] = *dataPoint.Minimum
			}
			if dataPoint.Maximum != nil {

				dataPoints[value] = *dataPoint.Maximum
			}
			if dataPoint.SampleCount != nil {

				dataPoints[value] = *dataPoint.SampleCount
			}
		}
	}
	if dataPoints.IsNotEmpty() {

		return MotadataFloat64(dataPoints[dataPoints.GetMaxKey()]).ToFloat64(), nil
	}

	return MotadataFloat64(-1), err
}

func GetAzureStorageTableURL(resource string, context MotadataMap) string {

	return "https://" + context.GetStringValue(consts.ObjectTarget) + ".table.core.windows.net/" + resource

}

func GetAzureStorageQueueURL(resource string, context MotadataMap) string {

	return "https://" + context.GetStringValue(consts.ObjectTarget) + ".queue.core.windows.net/" + resource

}

func ValidAzureInstance(systemTags map[string]*string, userTags []interface{}) bool {

	if systemTags != nil && userTags != nil {

		for systemTagKey, systemTagValue := range systemTags {

			for _, userTag := range userTags {

				if userTag == systemTagKey+consts.MotadataSeparator+*systemTagValue {

					return true
				}
			}
		}
	}
	return false
}

func ValidAWSInstance(systemTags map[string]string, userTags []interface{}) bool {

	if systemTags != nil && userTags != nil {

		for systemTagKey, systemTagValue := range systemTags {

			for _, userTag := range userTags {

				if userTag == systemTagKey+consts.MotadataSeparator+systemTagValue {

					return true
				}
			}
		}
	}
	return false
}

func SetOffice365AccessToken(httpClient *httpclient.HTTPClient, context MotadataMap, logger *Logger) (result MotadataMap) {

	requestContext := Office365RequestContext

	requestContext.Set("client_id", context.GetStringValue(consts.ClientId))

	requestContext.Set("client_secret", context.GetStringValue(consts.SecretKey))

	objectAccountId := context.GetStringValue(consts.ObjectAccountId)

	httpClient.
		SetContext(context, logger).
		SetURL(MotadataString(fmt.Sprintf(consts.Office365URL, context.GetMotadataStringValue(consts.TenantId)))).
		SetBody(bytes2.NewBufferString(requestContext.Encode()))

	if httpClient.Init(false) {

		response := httpClient.ExecutePOSTRequest()

		if len(httpClient.GetErrors()) == 0 && response.IsNotEmpty() && response.GetIntValue(httpclient.URLResponseCode) == httpclient.URLResponseCodeSuccess {

			if content := response.GetMapValue(httpclient.URLResponseContent); content.IsNotEmpty() && content.Contains(httpclient.AccessToken) && content.GetMotadataStringValue(httpclient.AccessToken).IsNotEmpty() {

				httpClient.
					SetParamKey(content.GetMotadataStringValue(httpclient.TokenType)).
					SetParamValue(content.GetMotadataStringValue(httpclient.AccessToken))

				result = content

			} else {

				httpClient.SetErrors(append(httpClient.GetErrors(), MotadataStringMap{

					consts.Error: response.GetStringValue(consts.URLBuffer),

					consts.ErrorCode: consts.ErrorCodeBadResponse,

					consts.Message: fmt.Sprintf(consts.ErrorMessageBadResponse, httpClient.GetURL()),
				}))

			}

		} else if response.GetIntValue(httpclient.URLResponseCode) == httpclient.URLResponseCodeUnauthorized || response.GetIntValue(httpclient.URLResponseCode) == httpclient.URLResponseCodeBadRequest && len(httpClient.GetErrors()) >= 1 {

			err := MotadataString(httpClient.GetErrors()[0][consts.Error])

			if err.Contains("error_codes") && err.Contains(consts.Error) {

				content := make(MotadataMap)

				_ = json.Unmarshal([]byte(err), &content)

				if content.GetStringValue(consts.Error) == "invalid_client" && contains(content.GetSliceValue("error_codes"), consts.CloudErrorCodeInvalidSecretId) && response.GetIntValue(httpclient.URLResponseCode) == httpclient.URLResponseCodeUnauthorized {

					httpClient.GetErrors()[0] = MotadataStringMap{

						consts.Error: err.ToString(),

						consts.ErrorCode: consts.ErrorCodeAPIInvalidSecretKey,

						consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidSecret, objectAccountId),
					}

				} else if content.GetStringValue(consts.Error) == "unauthorized_client" && contains(content.GetSliceValue("error_codes"), consts.CloudErrorCodeInvalidClientId) && response.GetIntValue(httpclient.URLResponseCode) == httpclient.URLResponseCodeBadRequest {

					httpClient.GetErrors()[0] = MotadataStringMap{

						consts.Error: err.ToString(),

						consts.ErrorCode: consts.ErrorCodeAPIInvalidClientId,

						consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidClientId, objectAccountId),
					}

				} else if content.GetStringValue(consts.Error) == "invalid_request" && contains(content.GetSliceValue("error_codes"), consts.CloudErrorCodeInvalidTenantId) && response.GetIntValue(httpclient.URLResponseCode) == httpclient.URLResponseCodeBadRequest {

					httpClient.GetErrors()[0] = MotadataStringMap{

						consts.Error: err.ToString(),

						consts.ErrorCode: consts.ErrorCodeAPIInvalidTenantId,

						consts.Message: fmt.Sprintf(consts.ErrorMessageInvalidTenantId, objectAccountId),
					}

				} else {

					httpClient.GetErrors()[0] = MotadataStringMap{

						consts.Error: err.ToString(),

						consts.ErrorCode: consts.ErrorCodeBadResponse,

						consts.Message: fmt.Sprintf(consts.ErrorMessageBadResponse, objectAccountId),
					}

				}

			} else {

				httpClient.GetErrors()[0] = MotadataStringMap{

					consts.Error: err.ToString(),

					consts.ErrorCode: consts.ErrorCodeBadResponse,

					consts.Message: fmt.Sprintf(consts.ErrorMessageBadResponse, objectAccountId),
				}

			}

		}

	}

	return
}

func contains(elements []interface{}, value float64) bool {

	for _, element := range elements {

		if value == element {

			return true

		}

	}

	return false
}
