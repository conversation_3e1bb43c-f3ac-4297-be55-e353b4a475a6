/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package utils

import (
	"github.com/stretchr/testify/assert"
	. "motadatasdk/globals"
	"testing"
)

func TestIsASCII(t *testing.T) {

	assert.True(t, IsASCII("a"))

	assert.False(t, IsASCII("❤"))

}

func TestGetMACAddress(t *testing.T) {

	assert.Equal(t, GetMACAddress([]byte{152, 1, 167, 171, 198, 135}).ToString(), "98:01:A7:AB:C6:87")

}

func TestStringSliceToMACAddress(t *testing.T) {

	macAddress := StringSliceToMACAddress([]uint8{92, 113, 13, 230, 99, 224})

	assert.Equal(t, MotadataString("5C:71:0D:E6:63:E0"), macAddress)
}
