/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package utils

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"motadatasdk/consts"
	. "motadatasdk/globals"
	"os"
	"os/exec"
	"strings"
	"time"
)

var loggerObj = NewLogger("Util", consts.BlankString)

func GetMACAddress(tokens interface{}) (macAddress MotadataString) {

	macAddress = consts.BlankString

	for index, token := range tokens.([]uint8) {

		hex := MotadataString(fmt.Sprintf("%X", token))

		if len(hex) == 1 {

			hex = "0" + hex
		}

		if index == len(tokens.([]uint8))-1 {

			macAddress = macAddress + hex

		} else {

			macAddress = macAddress + hex + ":"
		}
	}

	return
}

func LoadPluginContext(contextFile string) (MotadataMap, error) {

	path := consts.CurrentDir + consts.PathSeparator + consts.PluginContextDirectory + consts.PathSeparator + contextFile

	bytes, err := os.ReadFile(path)

	if err != nil {

		return nil, errors.New(fmt.Sprintf("Failed to load plugin context, reason: %s", err))
	}

	defer func() {

		err = os.Remove(path)

		if err != nil {

			loggerObj.Warn(MotadataString(fmt.Sprintf("Failed to delete plugin context file, reason: %s", err)))
		}

	}()

	decodedContext, err := base64.StdEncoding.DecodeString(string(bytes))

	if err != nil {

		return nil, errors.New(fmt.Sprintf("Unable to decode plugin context, reason: %s", err))
	}

	context := make(MotadataMap)

	err = json.Unmarshal(decodedContext, &context)

	if err != nil {

		return nil, errors.New(fmt.Sprintf("Failed to parse plugin context, reason: %s", err))
	}

	return context, nil
}

func CleanUp(context MotadataMap) {

	if r := recover(); r != nil {

		err := make(MotadataStringMap)

		err[consts.Message] = fmt.Sprintf("%v", r)

		err[consts.ErrorCode] = consts.ErrorCodeInternalError

		err[consts.Error] = fmt.Sprintf("%v", r)

		context[consts.Errors] = append(context.GetStringMapSliceValue(consts.Errors), err)
	}

	bytes, err := json.Marshal(context)

	if err != nil {

		context[consts.Status] = consts.StatusFail

		errors := []MotadataStringMap{
			{
				consts.ErrorCode: consts.ErrorCodeInternalError,
				consts.Error:     fmt.Sprintf("%v", err),
				consts.Message:   "Error occurred while marshalling Result",
			}}

		context[consts.Errors] = append(context.GetStringMapSliceValue(consts.Errors), errors...)
	}

	fmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)
}

func AddProcess(context MotadataMap, processName MotadataString, processCommand MotadataString, objects MotadataMap) {

	valid := true

	if context.Contains(consts.Objects) {

		valid = false

		for _, object := range context.GetSliceValue(consts.Objects) {

			if valid {

				break
			}

			for _, token := range strings.SplitN(ToMap(object).GetStringValue(consts.ObjectName), consts.Separator, 2) {

				valid = true

				if !strings.Contains(strings.ToLower(processName.ToString()), strings.ToLower(MotadataString(token).Strip().ToString())) &&
					!strings.Contains(strings.ToLower(processCommand.ToString()), strings.ToLower(MotadataString(token).Strip().ToString())) {

					valid = false

					break
				}
			}
		}
	}

	if valid {

		process := MotadataMap{
			consts.ObjectType: consts.Process,
		}

		if processCommand.IsNotEmpty() {

			process[consts.ObjectName] = (processName + consts.Separator + processCommand).ToString()

		} else {

			process[consts.ObjectName] = processName.ToString()
		}

		if !process.ContainValues(objects.GetMapSliceValue(consts.Objects)) {

			objects[consts.Objects] = append(objects.GetMapSliceValue(consts.Objects), process)
		}
	}
}

func StartProcess(command MotadataString, arguments MotadataStringList, timeout MotadataINT) (MotadataString, error) {

	if timeout <= 0 {

		timeout = 20
	}

	ctx, done := context.WithTimeout(context.Background(), time.Duration(timeout.ToInt64())*time.Second)

	defer done()

	stdOut, stdErr := exec.CommandContext(ctx, command.ToString(), arguments...).CombinedOutput()

	// if command timed out or context related errors

	if ctx.Err() != nil {

		return "", ctx.Err()
	}

	// returning standard stdOut and stdErr

	return MotadataString(stdOut), stdErr
}

func IsDir(file *os.File) (bool, []os.DirEntry, MotadataStringMap) {

	fileInfo, err := file.Stat()

	if err != nil {

		return false, nil, MotadataStringMap{
			consts.Error:     err.Error(),
			consts.ErrorCode: consts.ErrorCodeUnableToGetFileStat,
			consts.Message:   fmt.Sprintf(consts.ErrorMessageUnableToGetFileStat, ""),
		}
	}

	if fileInfo.IsDir() {

		files, err := file.ReadDir(0)

		if err != nil {

			return false, nil, MotadataStringMap{
				consts.Error:     err.Error(),
				consts.ErrorCode: consts.ErrorCodeFailedToReadDir,
				consts.Message:   fmt.Sprintf(consts.ErrorMessageFailedToReadDir, ""),
			}
		}

		return true, files, nil
	}

	return false, nil, nil
}
